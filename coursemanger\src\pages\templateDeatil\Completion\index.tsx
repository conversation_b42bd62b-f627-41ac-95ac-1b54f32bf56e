import statisticsApi from '@/api/statistics';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import { SearchOutlined } from '@ant-design/icons';
import { Button, Empty, Input, message, Select, Space, Table, Tooltip, TreeSelect } from 'antd';
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';
import { useHistory, useLocation, useSelector } from 'umi';
import null_png from '../../Syllabus/components/imgs/null.png';
import CourseTarget from './components/CourseTarget';
import Echart from './components/Echart';
import MicroBarChart from './components/MicroBarChart';
import MicroRadarChart from './components/MicroRadarChart';
import RadarChart from './components/RadarChart';
import TablePart from './components/TablePart';
import TargetChart from './components/TargetChart';
import './index.less';

const Completion: FC = () => {
  const { t } = useLocale();
  let history: any = useHistory();
  let location: any = useLocation();
  const { id, sm, code, type, reviewid, microMajor } = location.query;
  const [courseTip, setCcourseTip] = useState([]);
  const [tableData, setTableData] = useState<any[]>([]);
  //选中的目标
  const [selectTarget, setSelectTarget] = useState('')
  const [couresSyllabusCode, setCouresSyllabusCode] = useState('');
  const [detailStates, setDetailStates] = useState<any[]>([]);

  const { userInfo } = useSelector<any, any>(state => state.global);
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );
  // 列表和图的切换
  const [modeSwitch, setModeSwitch] = useState(true)

  //根据字符串排序
  const handleSortObject = (sortObj: any[], sortKey: string) => {
    return sortObj.sort((itemA, itemB) => (itemA?.[sortKey] as string).localeCompare(itemB?.[sortKey]))
  }

  //#region 微专业
  //获取模块列表
  const [selectMicroModule, setSelectMicroModule] = useState('')
  const [microModule, setMicroModule] = useState<any[]>([])
  const getMicroModuleList = (course_id: string) => {
    statisticsApi.queryMicroModules({ courseId: course_id }).then((res: any) => {
      if (res?.status == 200) {
        setMicroModule(res?.data?.data?.moduleInfos || [])
        setSelectMicroModule(res?.data?.data?.moduleInfos?.[0]?.nodeId || '')
      }
    })
  }
  useEffect(() => {
    if (type !== 'microMajor' || !id) return
    getMicroModuleList(id)
  }, [type, id])
  // 微专业选择树形数据调整
  const treeSelectData = useMemo(() => {
    const arr = [{
      value: 'from_major',
      title: '按微专业'
    }]
    if (microModule.length > 0) {
      const obj: any = {
        value: 'from_module',
        title: '按模块',
      }
      obj.children = microModule.map(item => ({ value: item.nodeId, title: item.nodeName }))
      arr.push(obj)
    }
    return arr
  }, [microModule])
  //选择树形数据
  const handleSelectTree = (value: any) => {
    if (value === 'from_major') {
      setSelectMicroModule('')
    } else
      if (value === 'from_module') {
        setSelectMicroModule(microModule[0].nodeId)
      } else {
        setSelectMicroModule(value)
      }
  }
  //微专业的雷达图
  const [microAchievementList, setMicroChievementList] = useState<any[]>([])
  //是否在更新统计
  const [isRefreshing, setIsRefreshing] = useState(false)
  const getMicroAchievementList = (param: any) => {
    statisticsApi.queryMicroModuleStatics(param).then((res: any) => {
      if (res?.status == 200) {
        setMicroChievementList(res?.data?.data?.allReach || [])
      }
    })
      .finally(() => {
        setIsRefreshing(false)
      })
  }
  useEffect(() => {
    if (type !== 'microMajor' || !id) return
    getMicroAchievementList({ courseId: id, courseSemester: Number(sm) || 1, nodeId: selectMicroModule })
  }, [selectMicroModule, type, id])
  //微专业手动更新统计
  const handleRefreshMicro = useCallback(() => {
    if (type !== 'microMajor' || !id) return
    setIsRefreshing(true)
    getMicroAchievementList({ courseId: id, courseSemester: Number(sm) || 1, nodeId: selectMicroModule })
  }, [selectMicroModule, type, id])
  //微专业横向柱形图
  const [stuAchieveModule, setStuAchieveModule] = useState<any[]>([])
  const getStudentAchieveByModule = (param: any) => {
    statisticsApi.queryStudentAchieveByModule(param).then((res: any) => {
      if (res?.status == 200) {
        const formatData = res?.data?.data?.allReach || []
        setStuAchieveModule(formatData.map((item: any) => ({
          achievedBelow20Percent: item?.zeroReach || 0,
          achieved21To40Percent: item?.twentyReach || 0,
          achieved41To60Percent: item?.fortyReach || 0,
          achieved61To80Percent: item?.sixtyReach || 0,
          achievedAbove80Percent: item?.eightyReach || 0,
          ...item
        })))
      }
    })
  }
  useEffect(() => {
    if (type !== 'microMajor' || !id) return
    getStudentAchieveByModule({ courseId: id, courseSemester: Number(sm) || 1, nodeId: selectMicroModule })
  }, [id, type, selectMicroModule])
  //#endregion

  //课程信息
  const [courseDetailInfo, setCourseDetailInfo] = useState<any>({})
  //是否已经请求过code
  const [isGetCode, setIsGetCode] = useState(false);
  const getCode = () => {
    statisticsApi
      .getCourseCode({
        contentId: id,
        courseSemester: sm,
      })
      .then(res => {
        if (res.data.status === 200) {
          setCouresSyllabusCode(res.data.data.entityData.couresSyllabusCode);
          setCourseDetailInfo(res.data.data)
        }
      })
      .finally(() => {
        setIsGetCode(true);
      });
  };

  //跳转能力图谱
  const handleToAbilityMap = useCallback(() => {
    if (mobileFlag) {
      message.info(t('暂不支持手机端，请前往电脑端操作'));
      return;
    }
    window.open(
      `${window.location.pathname}#/mapv3?couresSyllabusCode=${courseDetailInfo?.entityData?.couresSyllabusCode}&majorCode=${courseDetailInfo?.entityData?.applicableMajorCode}&perviewtype=3&grade=${courseDetailInfo?.entityData?.trainGrade}&applyGrade=${courseDetailInfo?.entityData?.trainGrade}&tabkey=2`,
    );
  }, [courseDetailInfo])

  //目标列表
  const [achievementList, setAchievementList] = useState<any[]>([]);
  //获取目标（雷达图数据， 列表数据）
  const getTargetData = (courseID: string, params: any) => {
    statisticsApi.queryAchievementByTarget(courseID, params).then(res => {
      if (res?.status === 200) {
        setAchievementList(handleSortObject(res.data?.data || [], 'graduateName'));
        const tips =
          res.data?.data?.map((item: any) => ({
            name: item?.graduateName,
            max: 100,
          })) || [];
        setCcourseTip(tips);
      }
    });
  };


  //分布数据
  const [distributData, setDistributData] = useState<any[]>([])
  // 课程目标达成度分布
  const getGoalDistributionData = (courseId: string, params: any) => {
    statisticsApi.getCourseGoalDistribution(courseId, params).then(res => {
      if (res.status == 200) {
        const { data } = res
        setDistributData(handleSortObject(data?.data || [], 'targetName'))
      }
    });
  };

  useEffect(() => {
    if (!id || !isGetCode) return;
    getGoalDistributionData(id, {
      courseCode: couresSyllabusCode || '',
    });
  }, [id, isGetCode, couresSyllabusCode]);
  //

  // 初始化目标列表的展示状态
  const handleInitStatus = (list: any[]) => {
    const statusList = Array(list.length).fill(false);
    statusList[0] = true;
    setDetailStates(statusList);
  };
  //目标集合
  const [goalsList, setGoalsList] = useState<any[]>([]);
  //根据课程获取目标
  const getGoalByCourseId = (courseId: string, courseCode?: string) => {
    statisticsApi
      .getCourseDetailByGoalId(courseId, {
        courseCode: courseCode,
      })
      .then(res => {
        if (res?.status === 200) {
          setGoalsList(res.data?.data || []);
          const tableList =
            res.data?.data?.map((item: any, index: any) => {
              return {
                title: item.name,
                subTitle: item.desc,
                id: item.id,
              };
            }) || [];
          setTableData(tableList);
          setSelectTarget(tableList[0]?.id)
          // getAchievementDetailByTargetId(tableList[0]?.id, () =>
          //   handleInitStatus(tableList),
          // );
        }
      });
  };

  useEffect(() => {
    if (!id || !isGetCode) return;
    getGoalByCourseId(id, couresSyllabusCode || '');
  }, [id, couresSyllabusCode, isGetCode]);

  useEffect(() => {
    if (!id || !isGetCode) return;

    getTargetData(id, {
      courseCode: couresSyllabusCode || '',
      courseSemester: sm || 1,
      applicableMajorCode: courseDetailInfo?.entityData?.applicableMajorCode,
      grade: courseDetailInfo?.entityData?.trainGrade,
      trainingPlanId: courseDetailInfo?.entityData?.trainingPlanId,
      searchType: 2
    });
  }, [id, couresSyllabusCode, isGetCode, courseDetailInfo]);

  const handleChangeGranduateType = (value: 1 | 2) => {
    if (!id) {
      return
    }
    getTargetData(id, {
      courseCode: couresSyllabusCode || '',
      courseSemester: sm || 1,
      applicableMajorCode: courseDetailInfo?.entityData?.applicableMajorCode,
      grade: courseDetailInfo?.entityData?.trainGrade,
      trainingPlanId: courseDetailInfo?.entityData?.trainingPlanId,
      searchType: value
    });
  }


  useEffect(() => {
    if (type === 'microMajor') return
    if (id && sm) {
      getCode();
    }
  }, [id, sm, type]);

  const getRadar = () => {
    return {
      title: {
        text: '毕业要求达成度',
        left: '0px',
        top: '0px',
      },
      tooltip: {
        trigger: 'item',
        valueFormatter: (value: any) => `${value.toFixed(1)}%`,
        position: 'inside'
      },
      radar: {
        // shape: 'circle',
        triggerEvent: true,
        nameGap: 10,
        center: ['50%', '55%'],
        radius: ['0%', '68%'],
        axisName: {
          color: '#76A2D2',
          backgroundColor: '#E8F3FE',
          lineHeight: 27,
          borderRadius: [5, 5, 5, 5],
          padding: [0, 5, 0, 5],
        },
        splitArea: {
          areaStyle: {
            color: ['#fff', '#fff', '#fff', '#fff'],
            shadowColor: 'rgba(0, 100, 0, 0.3)',
          },
        },
        indicator: achievementList?.map(item => ({
          name: item?.graduateName,
          max: 100,
        })),
      },
      series: [
        {
          type: 'radar',
          symbolSize: 0,
          data: [
            {
              // value: [77.8, 81.2, 72.4, 83.2, 75.6],
              value: achievementList?.map(
                item => (Number(item?.targetAvg) > 100 ? 100 : item?.targetAvg) || 0,
              ),
              // value: testData.aimd,
              name: '数据',
              itemStyle: {
                normal: {
                  color: 'rgba(5, 128, 242, 0.8)',
                },
              },
              areaStyle: {
                normal: {
                  color: '#B8D6FF',
                },
              },
            },
          ],
        },
      ],
    };
  };

  // 雷达图  防止页面重绘
  const overviewRadar = useMemo(() => {
    return achievementList.length > 0 ? (
      <RadarChart options={getRadar()} onClick={() => { }} />
    ) : (
      <Empty style={{ marginTop: 100 }} />
    );
  }, [achievementList]);

  //横向柱状图
  const getReverseBar = (dataArr: any[]) => {
    if (!dataArr?.length) return {}
    return {
      title: {
        text: '课程目标达成度分布',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      color: ['#00A7FF', '#C592FE', '#FEA800', '#FA7668', '#54C8D4'],
      legend: {
        data: [
          '指标点达成度:0-20%',
          '指标点达成度:21-40%',
          '指标点达成度:41-60%',
          '指标点达成度:61-80%',
          '指标点达成度:81-100%',
        ],
      },
      grid: {
        left: '3%',
        right: '5%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: '学生人数',
        nameGap: 20,
      },
      yAxis: {
        type: 'category',
        data: dataArr.map(item => item?.targetName),
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#A9A9A9',
          },
        },
      },
      series: [
        {
          name: '指标点达成度:0-20%',
          type: 'bar',
          stack: '总量',
          label: {
            show: true,
            color: 'rgba(255, 255, 255, 1)',
          },
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 15, 15],
          },
          data: dataArr.map(item => item?.achievedBelow20Percent || 0)
        },
        {
          name: '指标点达成度:21-40%',
          type: 'bar',
          stack: '总量',
          label: {
            show: true,
            color: 'rgba(255, 255, 255, 1)',
          },
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 15, 15],
          },
          data: dataArr.map(item => item?.achieved21To40Percent || 0)
        },
        {
          name: '指标点达成度:41-60%',
          type: 'bar',
          stack: '总量',
          label: {
            show: true,
            color: 'rgba(255, 255, 255, 1)',
          },
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 15, 15],
          },
          data: dataArr.map(item => item?.achieved41To60Percent || 0)
        },
        {
          name: '指标点达成度:61-80%',
          type: 'bar',
          stack: '总量',
          label: {
            show: true,
            color: 'rgba(255, 255, 255, 1)',
          },
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 15, 15],
          },
          data: dataArr.map(item => item?.achieved61To80Percent || 0)
        },
        {
          name: '指标点达成度:81-100%',
          type: 'bar',
          stack: '总量',
          label: {
            show: true,
            color: 'rgba(255, 255, 255, 1)',
          },
          barWidth: '15px',
          itemStyle: {
            borderRadius: [15, 15, 15, 15],
          },
          data: dataArr.map(item => item?.achievedAbove80Percent || 0)
        },
      ],
    };
  };

  //#region 目标的达成情况
  const [targetDetail, setTargetDetail] = useState<any[]>([])
  const getTargetAchievementDetail = (course_id: string, course_code: string, target_id: string) => {
    statisticsApi.queryTargetDetailById({ courseId: course_id, courseCode: course_code, targetId: target_id }).then(res => {
      if (res?.status == 200) {
        setTargetDetail(res?.data?.data || [])
      }
    })
  }

  useEffect(() => {
    if (!couresSyllabusCode || !selectTarget || !id) return
    getTargetAchievementDetail(id, couresSyllabusCode, selectTarget)
  }, [couresSyllabusCode, selectTarget, id])

  //#endregion

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [roles, setRoles] = useState<string>('');
  const [keyword, setKeyword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<any>({
    current: 1,
    position: ['bottomCenter'],
    pageSize: 10,
    total: 0,
    // size: "small",
    showTotal: (total: number) => t('共{name}条', String(total)),
    showQuickJumper: true,
    showSizeChanger: true,
  });

  const select = (value: string) => {
    setRoles(value);
  };
  const [selectAchievement, setSelectAchievement] = useState<number>()

  //排序
  const [sortBy, setSortBy] = useState<'ascend' | 'descend' | null>(null)

  const getPeopleList = (toPage1?: boolean, param?: any) => {
    setLoading(true);
    if (toPage1) {
      setPagination({
        ...pagination,
        current: 1,
      });
    }
    if (type === 'microMajor') {
      statisticsApi.queryStudentListByMicro({
        courseId: id,
        courseSemester: sm || 1,
        keyWord: keyword,
        page: toPage1 ? 1 : pagination.current,
        size: pagination.pageSize,
      }).then((res: any) => {
        if (res.data.status == 200) {
          setDataSource(res.data.data.results);
          setPagination({
            ...pagination,
            current: res.data.data.page,
            size: res.data.data.size,
            total: res.data.data.total,
          });
        }
      }).finally(() => {
        setLoading(false);
      })
    } else {
      const params: any = {
        courseId: id,
        role: roles,
        courseSemester: sm,
        page: toPage1 ? 1 : pagination.current,
        size: pagination.pageSize,
        keyword: keyword,
        courseType: type === 'map' ? 0 : 1,
        // 达成度区间 达成度区间 0：0-20、 1：21-40 2：41-60 3：61-10 4：81-100
        // attainmentRange: 0,
        // 排序方式 0：降序、1：升序（默认0）
        // sortBy: 0,
        // 排序字段----达成度：courseAchievement
        // sortTarget:
      }
      if (typeof selectAchievement === 'number') {
        params.attainmentRange = selectAchievement
      }
      if (param?.sortBy) {
        params.sortBy = param?.sortBy === 'ascend' ? 1 : 0
        params.sortTarget = 'courseAchievement'
      }
      statisticsApi
        .getPeopleList(params)
        .then(res => {
          if (res.data.status == 200) {
            setDataSource(res.data.data.results);
            setPagination({
              ...pagination,
              current: res.data.data.page,
              size: res.data.data.size,
              total: res.data.data.total,
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };
  useEffect(() => {
    getPeopleList();
  }, [pagination.current, pagination.pageSize]);
  const tablechange = (pagination: any, _: any, sorter: any, extra: any) => {
    if (extra?.action === 'sort' && sorter?.field === 'courseAchievement') {
      let sortByClone: null | 'descend' | 'ascend' = null
      switch (sortBy) {
        case 'ascend':
          sortByClone = 'descend'
          break;
        case 'descend':
          sortByClone = null
          break;
        default:
          sortByClone = 'ascend'
          break;
      }
      setSortBy(sortByClone)
      getPeopleList(true, { sortBy: sortByClone })
      return
    }
    if (extra?.action === 'paginate') {
      setPagination({
        ...pagination,
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    }
  };

  //表格组件
  const DetailTable: React.FC = () => {
    interface DataType {
      key: string;
      name: string;
      age: number;
      address: string;
      tags: string[];
    }
    const { id, type, sm } = location.query;
    const getColumns = () => {
      const columns = [
        {
          title: '姓名',
          dataIndex: 'userName',
          className: 'table-header-cell',
          key: 'userName',
          align: 'center',
        },
        {
          title: '学工号',
          dataIndex: 'userCode',
          className: 'table-header-cell',
          key: 'userCode',
          align: 'center',
        },
        {
          title: '角色',
          dataIndex: 'role',
          className: 'table-header-cell',
          key: 'role',
          align: 'center',
          render(roleValue: any, record: any) {
            return roleValue == 0 ? <span>学生</span> : <span>老师</span>;
          },
        },
        {
          title: '学院',
          dataIndex: 'college',
          className: 'table-header-cell',
          key: 'college',
          align: 'center',
        },
        {
          title: '专业',
          dataIndex: 'major',
          className: 'table-header-cell',
          key: 'major',
          align: 'center',
        },
        {
          title: '达成度',
          dataIndex: 'courseAchievement',
          className: 'table-header-cell',
          key: 'courseAchievement',
          align: 'center',
          render: (_: any, record: any) => {
            const rateValue = type === 'microMajor' ? record.reachRate : record.courseAchievement
            return Math.floor(Number(rateValue) * 100) / 100 + '%'
          },
          sorter: true,
          // sortOrder: 'descend',
          sortOrder: sortBy,
        },
        {
          title: t('操作'),
          key: 'action',
          align: 'center',
          className: 'table-header-cell',
          //  render: (_: any, record: any) => <Button type="link" onClick={() => { setShowDetail({ courseId, ...record }); setShowPage(2); }}>{t("查看详情")}</Button>
          render: (_: any, record: any) => (
            <Space
              size="middle"
              onClick={() => {
                if (type === 'map') {
                  history.push(
                    `/editcourse/completion/detail?id=${id}&sm=${sm}&code=${record.userCode}&type=${type}`,
                  );
                  return;
                }
                if (type === 'microMajor') {
                  history.push(
                    `/editcourse/completion/detail?id=${id}&sm=${sm}&code=${record.userCode}&type=${type}&microMajor=${microMajor}`
                  )
                  return
                }
                history.push(
                  `/editcourse/studentachievement?id=${id}&type=${type}&reviewid=${reviewid}&sm=${sm}&studentId=${record?.userCode}`,
                );
              }}
            >
              <a style={{ color: '#549CFF' }}>查看详情</a>
            </Space>
          ),
        },
      ];

      return columns;
    };

    return (
      <div>
        <Table
          size="small"
          dataSource={dataSource}
          loading={loading}
          rowClassName={(record, index) => {
            return index % 2 == 0 ? 'odd' : 'aaa';
          }}
          rowKey={(record: any) => record.userCode}
          columns={getColumns() as any}
          pagination={{
            ...pagination,
            size: 'small',
          }}
          showSorterTooltip={false}
          onChange={tablechange}
        />
      </div>
    );
  };
  return (
    <div
      className="completion_container"
      style={(!couresSyllabusCode && type !== 'microMajor') ? { height: '100%' } : {}}
    >
      {(!!couresSyllabusCode || type === 'microMajor') ? (
        <>
          <div className="top">
            {
              type === 'microMajor' ? (
                <>
                  <div className='top_title'>微专业指标达成度</div>
                  <div className='top_select'>
                    <TreeSelect
                      showSearch
                      style={{ width: '100%' }}
                      value={selectMicroModule || 'from_major'}
                      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                      treeDefaultExpandAll
                      onChange={handleSelectTree}
                      treeData={treeSelectData}
                    />
                  </div>
                  <div className='top_fresh_time'>
                    {/* 1130后端接口出不了 */}
                    {/* <span className='refresh_btn'>上次统计时间：2024-11-05 19:30</span> */}
                    {/* <Button type='primary' disabled={isRefreshing} onClick={handleRefreshMicro}>{isRefreshing ? '统计中' : '更新统计'}</Button> */}
                  </div>
                </>
              ) :  ( courseDetailInfo?.entityData?.applicableMajorCode && courseDetailInfo?.entityData?.trainGrade && <div style={{ position: 'absolute', right: 15, top: 15 }}>
                <Button type='primary' onClick={handleToAbilityMap}>能力图谱</Button>
              </div>)
            }
            {
              type === 'microMajor' ?
                (
                  <MicroRadarChart dataSource={microAchievementList} />
                ) : (
                  <>
                    <div className="top_left">
                      {
                        achievementList?.length > 0 && (
                          <div style={{ position: 'absolute', top: 36, left: 5, zIndex: 10 }}>
                            <Select
                              style={{ width: 120 }}
                              defaultValue={2}
                              getPopupContainer={triggerNode => triggerNode.parentElement}
                              options={[
                                { label: '毕业要求', value: 2 },
                                { label: '子指标', value: 1 }
                              ]}
                              onChange={(value: 1 | 2) => handleChangeGranduateType(value)}
                            />
                          </div>
                        )
                      }
                      {overviewRadar}
                    </div>
                    <div className="top_right">
                      {achievementList.map((item: any, index) => {
                        return (
                          <div className="top_right_container" key={index}>
                            <div className="header">
                              <div className="text">
                                <span
                                  className='text1'
                                >
                                  {item?.graduateName}
                                </span>
                                <div className="text2">{item?.desc}</div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </>
                )
            }
          </div>
          {type !== 'microMajor' && (
            <CourseTarget id={id} couresSyllabusCode={couresSyllabusCode} type={type} isGetCode={isGetCode} />
          )}
          {
            type === 'microMajor' ? <MicroBarChart dataSource={microAchievementList} /> : (
              <div className="center">
                <Select style={{ width: 120 }} value={selectTarget} getPopupContainer={triggerNode => triggerNode.parentElement} options={tableData?.map(item => ({ label: item?.title, value: item?.id }))} onChange={setSelectTarget}></Select>
                <div className="mode_switch_wrapper">
                  <div onClick={() => setModeSwitch(true)} className="mode_switch">
                    <Tooltip title={t("图例模式")}>
                      <IconFont
                        type="iconhebingxingzhuangfuzhi2"
                        className={modeSwitch ? 'active' : ''} />

                    </Tooltip>
                  </div>
                  <div onClick={() => setModeSwitch(false)} className="mode_switch">
                    <Tooltip title={t("列表模式")}>
                      <IconFont
                        type="iconliebiao"
                        className={modeSwitch ? '' : 'active'} />
                    </Tooltip>
                  </div>
                </div>
                {targetDetail?.length > 0 ? (
                  <>
                    {
                      modeSwitch ? <TargetChart data={targetDetail} /> : <TablePart data={targetDetail} />
                    }
                  </>
                ) : (
                  <Empty style={{ marginTop: 40 }} />
                )}
              </div>
            )
          }

          <div className="centerTwo">
            {(stuAchieveModule?.length > 0 || distributData.length > 0) ? (
              <Echart options={getReverseBar(type === 'microMajor' ? stuAchieveModule : distributData)} />
            ) : (
              <Empty style={{ marginTop: 80 }} />
            )}
          </div>
          <div className="bottom">
            <div className="filter" id='select-container' >
              <Space>
                <Select
                  defaultValue="全部"
                  style={{ width: 120 }}
                  onChange={select}
                  getPopupContainer={(triggerNode): any => triggerNode.parentNode}
                  options={[
                    { value: '', label: '全部' },
                    { value: 0, label: '学生' },
                    { value: 1, label: '老师' },
                  ]}
                />
                <Select
                  style={{ width: 160 }}
                  placeholder="请选择达成度区间"
                  getPopupContainer={(triggerNode): any => triggerNode.parentNode}
                  onChange={setSelectAchievement}
                  allowClear
                  options={[
                    { value: 0, label: '达成度：0-20%' },
                    { value: 1, label: '达成度：21-40%' },
                    { value: 2, label: '达成度：41-60%' },
                    { value: 3, label: '达成度：61-80%' },
                    { value: 4, label: '达成度：81-100%' },
                  ]}
                />
                <Input
                  placeholder="请输入学员姓名/学工号"
                  allowClear
                  onChange={(e: any) => setKeyword(e.target.value)}
                  onPressEnter={() => getPeopleList(true)}
                />
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={() => getPeopleList(true)}
                  color="#549CFF"
                  style={{
                    background: '#549CFF',
                    borderColor: '#549CFF',
                  }}
                >
                  搜索
                </Button>
              </Space>
            </div>
            <DetailTable></DetailTable>
          </div>
        </>
      ) : (
        <div className="completion_empty">
          <Empty
            image={null_png}
            imageStyle={{
              width: 150,
              height: 150,
            }}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
            description={
              <div>
                未绑定课程大纲，无达成度数据，
                <a
                  href={`#/editcourse/moocbaseinfo?id=${id}&type=${type}&reviewid=${reviewid}&sm=${sm}&tabKey=3`}
                >
                  去绑定
                </a>
              </div>
            }
          />
        </div>
      )}
    </div>
  );
};

export default Completion;
