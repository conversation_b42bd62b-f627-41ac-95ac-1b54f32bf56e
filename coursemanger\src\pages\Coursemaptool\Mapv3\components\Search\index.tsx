import React, {
  FC,
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useRef,
} from 'react';
import './index.less';
import {
  Drawer,
  List,
  Tag,
} from 'antd';
import useLocale from '@/hooks/useLocale';
import { hasRichTextContent } from '@/pages/Coursemaptool/Editmap/util';
import close_icon1 from '@/assets/imgs/coursemap/v3/close.png';
import close_icon2 from '@/assets/imgs/coursemap/v3/close2.png';
import title_bg1 from '@/assets/imgs/coursemap/v3/title_bg.png';
import title_bg2 from '@/assets/imgs/coursemap/v3/title_bg_dark.png';

const Search: FC<any> = forwardRef(
  (
    {
      graph,
      selecttype,
      querytype,
      inputtext,
      visible,
      setVisible,
      centerednode,
      skin='2'
    },
    ref,
  ) => {
    const { t } = useLocale();
    const [listdata, setListdata] = useState<any>([]);
    //原数据
    const [originaldata, setOriginaldata] = useState<any>([]);

    useImperativeHandle(ref, () => ({
      // 暴露给父组件的方法
      querynode,
    }));

    // 初始化方法
    useEffect(() => {
      if (graph && visible) {
        querynode();
      }
    }, [graph, visible, querytype]);


    // 检索知识点
    const querynode = () => {
      if (!graph) {
        return;
      }
      const data = graph.toJSON();
      let newdata: any = [];
      // 先过滤 数据
      newdata = data.cells.filter((item: any) => {
        if (item.shape == 'react-shape') {
          return true;
        } else {
          return false;
        }
      });

      // 名称搜索
      if (inputtext != '') {
        newdata = newdata.filter((item: any) => {
          let newname = item.data.label.toLowerCase();
          let newinput = inputtext.toLowerCase();
          if (newname.indexOf(newinput) != -1) {
            return true;
          } else {
            return false;
          }
        });
      }

      // 过滤筛选筛选的类型
      if (selecttype != '0') {
        newdata = newdata.filter((item: any) => {
          if (item.data.type == selecttype) {
            return true;
          } else {
            return false;
          }
        });
      }
      const val = querytype?.split("_") || [];
      // 过滤自定义标签
      if(val.length && val[0] == 'labels'){
        newdata = newdata.filter((item: any) => {          
          if (item.data.labelsconfig && item.data.labelsconfig.length > 0 && item.data.type == 2) {
            // 判断自定义标签是否包含当前id
            const flage = item.data.labelsconfig.some((label:any) => {
              return label.id == val[1];
            });
            return flage;
          } else {
            return false;
          }
        });
      }else if (querytype == '1') {
        // 过滤筛疑难点 和 有讲解的节点
        newdata = newdata.filter((item: any) => {
          if (item.data.isyinandian) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '2') {
        // 过滤有课程资源的节点
        newdata = newdata.filter((item: any) => {
          if (item.data.type == 2 && (hasRichTextContent(item.data.explanation) || item.data.bindresource.length || item.data.referenceMaterials.length)) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '3') {
        newdata = newdata.filter((item: any) => {
          if (item.data.isDiscriminate) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '4') {
        // 过滤跨课节点
        newdata = newdata.filter((item: any) => {
          if (item.data.linkmapid.length > 0) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '5') {
        // 过滤核心知识点
        newdata = newdata.filter((item: any) => {
          if (item.data.iscoreknowledge) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '6') {
        // 过滤拓展知识点
        newdata = newdata.filter((item: any) => {
          if (item.data.isexpandknowledge) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '7') {
        // 过滤拓展知识点
        newdata = newdata.filter((item: any) => {
          if (item.data.iscase) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '8') {
        // 过滤拓展知识点
        newdata = newdata.filter((item: any) => {
          if (item.data.isexperiment) {
            return true;
          } else {
            return false;
          }
        });
      } else if (querytype == '9') {
        // 过滤拓展知识点
        newdata = newdata.filter((item: any) => {
          if (item.data.homework.length > 0) {
            return true;
          } else {
            return false;
          }
        });
      }else if (querytype == '10') {
        // 过滤拓展知识点
        newdata = newdata.filter((item: any) => {
          // 判断富文本里面是否有内容
          if (hasRichTextContent(item.data.explanation) && item.data.type == 2) {
            return true;
          } else {
            return false;
          }
        });
      }else if (querytype == '11') {
        // 过滤拓展知识点
        newdata = newdata.filter((item: any) => {
          if (!hasRichTextContent(item.data.explanation) && item.data.type == 2) {
            return true;
          } else {
            return false;
          }
        });
      }else if (querytype == '12') {
        // 过滤无课程资源的节点
        newdata = newdata.filter((item: any) => {
          if(item.data.type != 2){
            return false;
          }
          if (hasRichTextContent(item.data.explanation) || item.data.bindresource.length || item.data.referenceMaterials.length) {
            return false;
          } else {
            return true;
          }
        });
      }


      setListdata(newdata);
      setOriginaldata(newdata);
    };

    const expandall = (flage: boolean) => {
      // 获取所有节点
      const nodes = graph.getNodes();
      const successors = graph.getSuccessors(nodes[0], { distance: 1 }).map((item: any) => item.id);
      nodes.forEach((node: any) => {
        const data = node.getData();
        const isroot = graph.isRootNode(node);
        // 如果不是根节点和二级菜单
        if (!isroot) {
          // 设置展开状态
          node.updateData({
            ...data,
            isCollapsed: flage
          });
        }
        if (isroot || successors.includes(node.id)) {
          node.setVisible(true);
        } else {
          node.setVisible(flage);
        }
      });    
    };

    return (
      <Drawer        
        title={
          <div style={{ position: 'relative', width: '100%', height: '100%' }}>
            <img style={{ width: '100%', height: 'auto' }} src={skin == '1'? title_bg1:title_bg2 } />
            <img onClick={() => setVisible(0)} style={{ position: 'absolute', right: '20px', width: '15px', top: '17px',cursor:'pointer' }} src={skin == '1' ? close_icon1: close_icon2} alt="" />
          </div>}
        placement="right"
        mask={false}
        closable={false}
        visible={visible == 6}
        getContainer={false}
        style={{ position: 'absolute' }}
        width="500px"
        className={skin == '1' ? 'newsearch' : 'newsearch_dark'}
      >
        <List
          bordered
          locale={{ emptyText: t('暂无搜索结果') }}
          dataSource={listdata}
          renderItem={(item: any) => (
            <List.Item
              onClick={() => {
                expandall(true);
                let cell = graph.getCellById(item.id);
                graph.getNodes().forEach((item: any) => {
                  // 设置节点透明
                  item.attr('foreignObject/opacity', 0.2);
                });
                graph.getEdges().forEach((item: any) => {
                  if((window as any).skin == '1'){
                    // 设置节点透明
                    item.attr('line/stroke', '#333333');
                  }else{
                    // 设置节点透明
                    item.attr('line/opacity', 0.2);
                  }
                });
                // 设置节点高亮
                cell.attr('foreignObject/opacity', 1);
                cell.attr('line/stroke', '#B3BAC4');
                graph.centerCell(cell, { animation: { duration: 400 } });
              }}
              style={{ cursor: 'pointer' }}
            >
              {item.data.type == 1 && <Tag color="orange">{t('分类节点')}</Tag>}
              {item.data.type == 2 && <Tag color="orange">{t('知识节点')}</Tag>}
              {item.data.type == 3 && <Tag color="orange">{t('课程节点')}</Tag>}
              {item.data.type == 4 && <Tag color="orange">{t('专业节点')}</Tag>}
              {item.data.type == 5 && <Tag color="orange">{t('教学模块')}</Tag>}
              <span style={ skin == '1' ? { color: '#fff' } : {}} >{item.data.label}</span>
            </List.Item>
          )}
        />
      </Drawer>
    );
  },
);

export default Search;
