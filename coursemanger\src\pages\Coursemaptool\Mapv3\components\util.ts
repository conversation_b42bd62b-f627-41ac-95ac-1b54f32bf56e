

import { getUuid } from '@/utils';
// 默认的节点data
export const defaultNodeData:any  = {
    isroot: false,//是不是根节点
    isCollapsed: true, //是否展开
    isedit: false,//是否编辑
    isyinandian: false,//是否是重难点
    explanation: '',//详解
    bindresource: [],//绑定的资源
    isDiscriminate: false,// 是否有对比辨析
    quoteKnowledge: [],//引用的知识点
    bindlink: [],//绑定的外部链接
    referenceMaterials: [],//参考资料
    homework:[], //作业
    linkmapid:[],//关联的其他地图id和节点的id    
    iscoreknowledge:false,// 核心知识点        
    isexpandknowledge:false,// 是否是拓展知识点
    iscase:false,// 是否是案例
    isexperiment:false,// 是否是实验
    caselist:[],//案例列表
    teachingModuleManager:[],// 教学模块负责人
    score:0, //分数
    studytime:[], //学习时间段,
    notNeedToStudy:false,// 是否免修
    createuser:'',//创建人
    status: 0, //0未编辑 1已编辑
    attribute:'',//属性
    referenceStudyTime:0,//参考学时
    nochangedId: '',// 唯一id
    labelsconfig:[],//标签配置
}

export const MapConfig = {
    marjor:{
        fontSize:20,  
        size: [108, 108],
    }, //专业
    course:{
        fontSize:18,
        size: [71, 71], 
    },//课程
    fenlei:{
        fontSize:16,
        size: [44, 44],
    },//分类节点
    knowledge:{
        fontSize:14,
        size: [25, 25],
    },//知识点
    teachingmodule:{
      fontSize:16,
      size: [50, 50],
  },//教学模块
}

export function createguid() {
    return getUuid();
}

// 构造节点数据
export const createNodeData = (data:any) => {
    let rootid = createguid();
    let nodes:any = [{
        nodeId: rootid,
        valueMap: JSON.stringify({
          data:{
            isroot: true,//是不是根节点
            label: data.majorName,
            type:4, // 1子节点 2知识节点 3课程节点 4专业节点
            layoutname:1, //默认布局
            ...defaultNodeData,
          }
        }),
      }];
    let edges:any= [];
    let courseNumber:any = [];
    let mapcourseobj:any = {};  //这里构造一字典 方便查询到课程地图后 找到节点id
    data.trainingPlanCourseRelations.forEach((item:any) => {
        let targetid = createguid();
        courseNumber.push(item.course.courseId);
        mapcourseobj[item.course.courseId] = targetid;
        nodes.push({
            nodeId:targetid ,
            valueMap: JSON.stringify({
              data:{
                isroot: false,//是不是根节点
                label: item.course.courseName,
                course: item.course,
                type:3, // 1子节点 2知识节点 3课程节点 4专业节点
                layoutname:1, //默认布局
                ...defaultNodeData,
              }
            }),
        });//课程节点
        edges.push({
            data: "{\"visible\":true,\"type\":1,\"isnew\":false}",
            source: rootid,
            target: targetid,
            type: 1
        },);//专业节点和课程节点的关系
    });
    return {
        nodes:nodes,
        edges:edges,
        courseNumber:courseNumber,
        mapcourseobj:mapcourseobj
    }
}

// 蛇形布局算法  laytype 1是弯道  2是直道
export const  snakeLayout = (data:any,fx:number=200,fy:number=120,startx:number=5,laytype:number=1) => {
    // let fx = 200;
    // let fy = 150;
    // 当前是加还是减
    let flag = true;
    // 累计加几个开始转弯
    // let startx = 4;
    // 下一个是否转弯
    let nextnode = false;
    // 上一个节点
    let prenode:any = null;
    let radiuids:any = [];
    let radisinfo:any = [];
    let newnodes = data.nodes.map((item:any,index:number)=>{
      let newindex = index + 1;
      let obj = {
        ...item,
        x: 0,
        y: 0,
      };
      
      // 第一个节点
      if(index == 0){
        
      }else if(newindex%startx == 0){
        // 第一个转弯
        nextnode = true;
        if(laytype == 1){
            if(flag){
                obj = {
                  ...item,
                  x: prenode.x + fx,
                  y: prenode.y - fy,
                }
              }else{
                obj = {
                  ...item,
                  x: prenode.x - fx,
                  y: prenode.y + fy,
                }
              }
        }else{
            if(flag){
                obj = {
                  ...item,
                  x: prenode.x + fx,
                  y: prenode.y,
                }
              }else{
                obj = {
                  ...item,
                  x: prenode.x - fx,
                  y: prenode.y,
                }
              }
        }
        
        radiuids.push(obj.nodeId);
        radisinfo.push(obj);
      }else if(nextnode){        
        // 第二个转弯
        nextnode = false;
        if(laytype == 1){
            obj = {
                ...item,
                x: prenode.x - fx * 1.2,
                y: prenode.y - fy * 2,
              }
        }else{
            obj = {
                ...item,
                x: prenode.x ,
                y: prenode.y - fy * 1.4,
              }
        }
        
        // 开始反转
        flag = !flag;
      }else{
        if(laytype == 1){
            if(flag){ 
                obj = {
                  ...item,
                  x: prenode.x + fx,
                  y: prenode.y - fy,
                } 
              }else{
                obj = {
                  ...item,
                  x: prenode.x - fx,
                  y: prenode.y + fy,
                } 
              }
        }else{
            if(flag){ 
                obj = {
                  ...item,
                  x: prenode.x + fx,
                  y: prenode.y,
                } 
              }else{
                obj = {
                  ...item,
                  x: prenode.x - fx,
                  y: prenode.y,
                } 
              }
        }
        
      }
      // 记录上一个节点的位置
      prenode = obj;
      return obj;
    })

    // 恢复原来的位置 判断是加坐标还是减去坐标
    flag = true;
    // 开始换算圆角转折点坐标
    let newedges = data.edges.map((item:any)=>{
      if(radiuids.includes(item.source)){      
        let sourcenode = radisinfo.find((item2:any)=>item2.nodeId == item.source);  
        let obj = {
            ...item,
            source: {
              cell: item.source,
              // anchor: 'right' //midSide
            },
            target: {
              cell: item.target
            },
            connector: {
                name: 'rounded',
                args: {
                    radius: 530,
                },
            },
        }
        if(laytype == 1){
            if(flag){
            flag = false;
            obj.vertices = [
                { 
                x : sourcenode.x + 30,
                y : sourcenode.y - 20
                },
                // { 
                //   x : sourcenode.x - 10,
                //   y : sourcenode.y - 160
                // },             
                { 
                x : sourcenode.x - 160,
                y : sourcenode.y - 190
                },
            ]
            
            }else{
            flag = true;
            obj.vertices = [
                { 
                x : sourcenode.x - 50,
                y : sourcenode.y + 40
                },
                // { 
                //   x : sourcenode.x - 130,
                //   y : sourcenode.y + 35
                // },
                { 
                x : sourcenode.x - 220,
                y : sourcenode.y - 90
                },
            ]          
            }
        }else{
            if(flag){
                flag = false;
                // obj.vertices = [
                //     { 
                //     x : sourcenode.x + 80,
                //     y : sourcenode.y - 40
                //     },
                //     { 
                //     x : sourcenode.x + 60,
                //     y : sourcenode.y - 140
                //     },
                // ]
                
            }else{
                flag = true;
                // obj.vertices = [
                //     { 
                //     x : sourcenode.x - 20,
                //     y : sourcenode.y
                //     },
                //     { 
                //     x : sourcenode.x - 20,
                //     y : sourcenode.y - 140
                //     },
                // ]          
            }
        }
        return obj;
      }else{
        return item;
      }
    })

    return {
        nodes:newnodes,
        edges:newedges
    }
}