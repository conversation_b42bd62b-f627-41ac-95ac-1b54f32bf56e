import {
  getSemesters,
  pushCourses,
  queryColleges,
  reqCourseCopy,
} from '@/api/course';
import { deleteHomeworks } from '@/api/homework';
import {
  createCourseByTpl,
  exportword,
  getSpocCourse,
  nweMoocCourse,
  offShelfCourse,
  offShelfCourseNew,
  releaseCourseNew,
} from '@/api/mooclist';
import { courseOfSensitivew } from '@/api/addCourse';
import recycleBinApi from '@/api/recycleBin';
import AuthenticationModal from '@/components/AuthenticationModal';
import CourseBlock from '@/components/CourseBlock/CourseBlock';
import DeleteProcess from '@/components/DeleteProcessModal';
import MobileSearch from '@/components/SearchForm/mobileSearch';
import TurnThePageDataItem from '@/components/formItemBox/turnThePageDataItem';
import { IconFont } from '@/components/iconFont';
import SelectTemplateModal from '@/components/selectTemplateModal';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { COURSE_TYPE, ModuleCfg2 } from '@/permission/moduleCfg';
import { confirmModal } from '@/utils';
import { PlusCircleFilled, ReloadOutlined, ExclamationCircleTwoTone } from '@ant-design/icons';
import {
  Button,
  Cascader,
  Checkbox,
  DatePicker,
  Empty,
  Form,
  Image,
  Input,
  Modal,
  Pagination,
  Popover,
  Radio,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  TreeSelect,
  message,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import moment from 'moment';
import React, { FC, useEffect, useRef, useState } from 'react';
import { useDispatch, useHistory, useSelector } from 'umi';
import './index.less';

const { Option } = Select;
const { confirm } = Modal;
const { RangePicker } = DatePicker;
interface ISemesters {
  id: string;
  code: string;
  name: string;
  showSemester?: string;
}

const ellipsisSetting = (width: Number) => {
  return {
    style: {
      maxWidth: width,
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      cursor: 'pointer',
    },
  };
};

const SpocListPage: FC = () => {
  const { t } = useLocale();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [selectKey, setSelectKey] = useState<string[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [newSelectedRows, setNewSelectedRows] = useState<any[]>([]);
  const [publishSelectedRows, setPublishSelectedRows] = useState<any[]>([]);
  const [releaseDis, setReleaseDis] = useState(true);
  const [offShelfDis, setOffShelfDis] = useState(true);
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(true);
  const [operationData, setOperationData] = useState<any>();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [releaseOrNot, setReleaseOrNot] = useState<0 | 1 | 2>(0);
  const [release, setRelease] = useState<boolean>(false);
  const [offlineCourse, setOfflineCourse] = useState<any[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [courseName, setCourseName] = useState<string>('');
  const offlineId = useRef('');

  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [cascaderOptions, setCascaderOptions] = useState<any[]>([]);
  const [cascaderValue, setCascaderValue] = useState<any>([]);
  const [semesters, setSemesters] = useState<ISemesters[]>([]);
  const [realTreeValue, setRealTreeValue] = useState<any>('');
  const copyParamRef = useRef<string>('');

  const { location }: any = useHistory();
  const isTraining = useRef<boolean>(location.pathname.includes('training'));
  const pushList = [
    { label: t('四川省平台'), value: 1 },
    { label: t('国家平台'), value: 2 },
  ];

  const pushLevelList = [
    { label: t('校级'), value: 1 },
    { label: t('省级'), value: 2 },
    { label: t('国家级'), value: 3 },
  ];
  const [query, setQuery] = useState<any>({
    pageIndex: 1,
    pageSize: Number(localStorage.getItem('spooclist_size') ?? 24),
    courseType: isTraining.current ? 3 : 2,
    personalMode: isTraining.current ? null : false,
  });
  const userInfo = useSelector<Models.Store, any>(
    state => state.global.userInfo,
  );

  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  const [deleteProcessModalVisible, setDeleteProcessModalVisible] = useState(
    false,
  );
  const [isCopyModalVisible, setIsCopyModalVisible] = useState(false);
  const [copyLoading, setCopyLoading] = useState(false);
  const [processId, setProcessId] = useState<string>('');
  const authenticationModalRef = useRef<any>(null);
  const [personalMode, setPersonalMode] = useState<boolean>(false);
  const [curSemester, setCurSemester] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const isSuper =
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_sys_manager') || //是否是系统管理员
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_course_manager') || //是否是课程管理员
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_second_manager') || //第二权限
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1'); // admin
  const [isReject, setIsReject] = useState<boolean>(false);
  const [recallButton, setRecallButton] = useState<boolean>(true); // 撤回发布状态
  const [createLoading, setCreateLoading] = useState<boolean>(false);
  useEffect(() => {
    if (!isSuper && !isTraining.current) {
      setPersonalMode(true);
      setModeSwitch(true);
      setQuery({ ...query, personalMode: true });
    }
  }, [userInfo]);

  //如果不是管理员，就使用个人模式
  useEffect(() => {
    if (!isSuper) {
      setPersonalMode(true);
    }
  }, [isSuper]);
  //如果是工作台跳转过来打开新建页面
  useEffect(() => {
    if (location.query.openShow == 'true') {
      setCreateModalVisible(true);
    }
  }, [location]);
  useEffect(() => {
    if (!isSuper) {
      setPersonalMode(true);
    }
  }, [isSuper]);
  const pageVisibleHandle = () => {
    if (!document.hidden) {
      fetchDataList();
    }
  };
  useEffect(() => {
    // 初始化
    // if (localStorage.getItem('spooclist_size')) {
    //   setQuery({
    //     ...query,
    //     pageSize: Number(localStorage.getItem('spooclist_size')),
    //   });
    // }
    queryCourseSearch();

    // document.addEventListener('visibilitychange', pageVisibleHandle);
    if (!isTraining.current) {
      querySemester();
    }

    if (location.query.isAdd == 'true') {
      setCreateModalVisible(true);
    }
    return () => {
      // document.removeEventListener('visibilitychange', pageVisibleHandle);
    };
  }, []);
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    JSON.parse(localStorage.getItem('learn_view_mode') || '{}').spoclist != '0',
  );
  // 视图切换

  //总的的列表
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);

  // const [createForm] = Form.useForm();

  const [pushPlatVisible, setPushPlatVisible] = useState<boolean>(false);
  const [pushValue, setPushValue] = useState<string[]>([]);
  const [pushLevel, setPushLevel] = useState<number>(1);
  const [subjectList, setSubjectList] = useState<any[]>([]);
  const [courseTypeList, setCourseTypeList] = useState<any[]>([]);
  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const [releaseLoading, setReleaseLoading] = useState<boolean>(false);
  const [sensitiveWordInfos, setSensitiveWordInfos] = useState<any>([]); // 敏感词信息
  const { parameterConfig, buttonPermission, permission } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any }
  >(state => state.global);
  useEffect(() => {
    const temp = localStorage.getItem('learn_view_mode') || '{}';
    const value = {
      ...JSON.parse(temp),
      spoclist: modeSwitch ? '1' : '0',
    };
    localStorage.setItem('learn_view_mode', JSON.stringify(value));
  }, [modeSwitch]);
  const querySemester = () => {
    getSemesters().then((res: any) => {
      if (res.status === 200) {
        setSemesters(res.data.semesterDataList);
        setCurSemester(res.data.currentSemester?.name || '');
        if (res.data.currentSemester?.name) {
          form.setFieldsValue({
            semester_teaching_courses: res.data.currentSemester.name,
          });
          setQuery({
            ...query,
            semester_teaching_courses: res.data.currentSemester.name,
          });
        }
      } else {
        message.error(res.message);
      }
    });
  };

  const queryCourseSearch = () => {
    queryColleges().then(res => {
      if (res.status === 200) {
        setCourseTypeList(res.data?.classificationType ?? []);
        setSubjectList(res.data?.subjectEducation ?? []);
        setCascaderOptions(res.data?.subjectEducation ?? []);
      }
    });
  };

  const onSearch = (value: any) => {
    setQuery({
      ...query,
      pageIndex: 1,
      ...value,
      isend: value.isend || null,
      startTime: value.startTime ? value.startTime[0].format('x') : undefined,
      endTime: value.startTime ? value.startTime[1].format('x') : undefined,
    });
  };
  const onReset = () => {
    form.resetFields();
    setQuery({
      pageIndex: 1,
      pageSize: query.pageSize,
      courseType: query.courseType,
    });
  };
  const rowSelection = {
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectKey(newSelectedRowKeys);
      setNewSelectedRows(newSelectedRows);
      setPublishSelectedRows(
        newSelectedRows.filter((d: any) => d.publishStatus == 1),
      );
    },
    selectedRowKeys: selectKey,
  };


  // 把获取列表的逻辑抽离出来
  const getListData = (param: any) => {
    console.log('query--->', query)
    getSpocCourse(param)
      .then(res => {
        setTableLoading(false);
        dispatch({
          type: 'config/changeShowLoading',
          payload: {
            value: false,
          },
        });
        if (res.message === 'OK') {
          const { results, total } = res.data;
          console.log('results--->', results)
          setTotal(total);
          const data = results.map((item: any) => {
            let {
              name,
              contentId_,
              subject,
              end_time,
              start_time,
              publishStatus,
              teacher,
              publishTime,
              createDate_,
            } = item.entityData;
            return {
              ...item.entityData,
              name,
              key: contentId_,
              // subject: subjectName && subjectName.join(),
              opentime: end_time
                ? `${moment(start_time).format('YYYY-MM-DD')}${t('到')}${moment(
                  end_time,
                ).format('YYYY-MM-DD')}`
                : '',
              publishStatus,
              updatetime: publishTime
                ? moment(publishTime).format('YYYY-MM-DD HH:mm:ss')
                : createDate_,
            };
          });
          setDataSource(data);
          setPublishSelectedRows(
            [...data]
              .filter((d: any) => selectKey.includes(d.contentId_))
              .filter((d: any) => d.publishStatus == 1),
          );
        }
      })
      .catch(() => setTableLoading(false));
  }


  const fetchDataList = () => {
    setTableLoading(true);
    copyParamRef.current = {...query};
    getListData({ ...query });
  };

  //详情页面关闭，通知本页面获取列表数据
  const updateList = () => {
    // 使用useRef存储的值来获取最新的参数
    const currentParam = copyParamRef.current;
    if (!currentParam) {
      console.error('No param available for update');
      return;
    }
    getListData(currentParam);
  }

  // 监听localStorage中的isClosed属性变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'isClosed' && e.newValue === 'true') {
        // 当isClosed值变为true时，重新获取列表数据
        console.log('isClosed detected, updating list')
        updateList();
      }
    };

    // 添加storage事件监听器
    window.addEventListener('storage', handleStorageChange);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 同时检查当前窗口的localStorage值
  useEffect(() => {
    const isClosed = window.localStorage.getItem('isClosed');
    if (isClosed === 'true') {
      updateList();
      window.localStorage.removeItem('isClosed');
    }
  }, []);


  const handleDeleteOk = () => {
    let param: any = [];
    if (oneOrBatch) {
      param.push(operationData.key);
    } else {
      param = [...selectKey];
    }
    recycleBinApi.deleteCourse(param, { type: 0 }).then((res: any) => {
      if (res && res.message === 'OK') {
        // message.success('删除成功');
        deleteHomeworks(param);
        // setProcessId(res.data);
        // setDeleteProcessModalVisible(true);
        fetchDataList();
        message.success("删除成功");
      } else {
        message.error(t('删除失败'));
      }
      // setTimeout(() => fetchDataList(), 500);
      setSelectKey([]);
      setIndeterminate(false);
      setIsDeleteModalVisible(false);
    });
  };
  //复制
  const handleCopyOk = () => {
    setCopyLoading(true);
    reqCourseCopy(operationData?.contentId_, operationData?.courseType)
      .then(res => {
        if (res.status == 200) {
          message.success(t('复制成功'));
          setIsCopyModalVisible(false);
          fetchDataList();
        } else {
          message.error(t('复制失败'));
        }
      })
      .finally(() => setCopyLoading(false));
  };

  const batchDelete = () => {
    if (selectKey.length) {
      setOneOrBatch(false);
      setIsDeleteModalVisible(true);
    } else {
      message.info(t('请选择课程'));
    }
  };

  const batchRelease = () => {
    if (selectKey.length) {
      setOneOrBatch(false);
      setRelease(true);
    } else {
      message.info(t('请选择课程'));
    }
  };

  const handleCreate = () => {
    if (courseName == null || courseName === '') {
      message.error(t('请填写课程名称'));
      return;
    }
    if (permission.includes(ModuleCfg2.template)) {
      // offlineId.current = course;
      setTemplateModalVisible(true);
    } else {
      // offlineId.current = course;
      handleOk();
    }
  };

  const handleOk = (tplId?: string) => {
    setCreateLoading(true);
    console.log('courseName',courseName,tplId)
    // const course = offlineCourse.find(item => item.id === offlineId.current);
    if (tplId) {
      console.log('courseName2',courseName)
      createCourseByTpl(
        {
          // related_courses: offlineId.current,
          courseType: isTraining.current ? 3 : 2,
          name: courseName,
          // college: [course.college],
          // major: Array.isArray(course.major)
          //   ? course.major
          //   : course.major?.split(',') || [],
          // teacher: course.teacher.split(','),
          // courseNo: course.course_no,
          // courseId: course.course_id,
        },
        tplId,
      )
        .then(res => {
          if (res && res.message === 'OK') {
            setCreateModalVisible(false);
            setTemplateModalVisible(false);
            window.open(
              `#/editcourse/baseInfo?id=${res.data}&type=${isTraining.current ? 'training' : 'spoc'
              }&first=1&sm=1&action=add`,
            );

          } else {
            message.error(t('新建失败'));
          }
          fetchDataList();
          offlineId.current = '';
        })
        .finally(() => setCreateLoading(false));
    } else {
      //获取不到最新的值 需要强制更新
       let saveName = '';
      setCourseName((pre: string)=>{
        saveName = pre
        return pre
      })
      nweMoocCourse({
        // related_courses: offlineId.current,
        courseType: isTraining.current ? 3 : 2,
        name: saveName,
        // college: [course.college],
        // major: Array.isArray(course.major)
        //   ? course.major
        //   : course.major?.split(',') || [],
        // teacher: course.teacher.split(','),
        // courseNo: course.course_no,
        // courseId: course.course_id,
      })
        .then(res => {
          if (res && res.message === 'OK') {
            setCreateModalVisible(false);
            window.open(
              `#/editcourse/baseInfo?id=${res.data.contentId}&type=${isTraining.current ? 'training' : 'spoc'
              }&first=1&sm=1&action=add`,
            );
          } else {
            message.error(t('新建失败'));
          }
          fetchDataList();
          offlineId.current = '';
        })
        .finally(()=>{
          setCreateLoading(false)
          setTemplateModalVisible(false)
        });
    }
  };
  const [publishLoading, setPublishLoading] = useState<boolean>(false);


  // 敏感词信息dom
  const sensitiveMsgListDom = (list: any) => {
    return ''
    // if (!list || list.length === 0) return ''

    // return list.map((item: any) => <div style={{ padding: '0 15px' }}>
    //   <div style={{ marginTop: 10 }}><ExclamationCircleTwoTone twoToneColor="#faad14" /> 资源  <span>{item.name}</span>：</div>
    //   <div style={{ paddingLeft: 20, margin: '10px 0' }}>
    //     {
    //       item.sensitiveInfos?.map((ele: any) => <div>
    //         <span>({ele.source}) - 包含敏感词：</span>
    //         <span>{ele.sensitiveMsgs.join('、')}</span>
    //       </div>)
    //     }
    //   </div>
    // </div>)
  }

  const handleReleseOK = () => {
    setPublishLoading(true);
    let param = [];
    if (oneOrBatch) {
      param.push(operationData.key);
    } else {
      param = [...selectKey];
    }
    if (releaseOrNot === 1) {
      releaseCourseNew(param, query.courseType).then(res => {
        if (res && res.message === 'OK') {
          const messageText = getPermission(
            ['spoc', 'training'],
            '_course_release_review',
            true,
            isTraining.current ? 'training' : 'spoc',
          )
            ? `${isSuper
              ? t('提交成功，您是管理员，已为您自动完成课程发布审核')
              : t('课程已提交至管理员审核！')
            }`
            : t('课程发布成功！');
          message.success(messageText);
        } else {
          res && message.error(res.message);
        }
        setTimeout(() => fetchDataList(), 500);
        setSelectKey([]);
        setRelease(false);
      }).finally(() => {
        setPublishLoading(false);
      });
    } else if (releaseOrNot === 0) {
      if (isReject) {
        offShelfCourse(param, {
          courseType: query.courseType,
          withdrawalFlag: 1,
        }).then(res => {
          if (res && res.message === 'OK') {
            const messageText = !isReject
              ? t('课程取消发布成功！')
              : t('课程撤回审核成功！');
            message.success(messageText);
          }
          setTimeout(() => fetchDataList(), 500);
          setSelectKey([]);
          setRelease(false);
        }).finally(() => {
          setPublishLoading(false);
        });
      } else {
        offShelfCourseNew(param, {
          courseType: query.courseType,
          withdrawalFlag: !isReject ? 0 : 1,
        }).then(res => {
          if (res && res.message === 'OK') {
            const messageText = !isReject
              ? t('课程取消发布成功！')
              : t('课程撤回审核成功！');
            message.success(messageText);
          }
          setTimeout(() => fetchDataList(), 500);
          setSelectKey([]);
          setRelease(false);
        }).finally(() => {
          setPublishLoading(false);
        });
      }
    }
  };

  // 全选
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    // setReleaseDis(false);
    setSelectKey(
      e.target.checked ? dataSource.map((item: any) => item.contentId_) : [],
    );

    setNewSelectedRows([...dataSource]);
    setPublishSelectedRows(
      e.target.checked
        ? [...dataSource].filter((d: any) => d.publishStatus == 1)
        : [],
    );
    setIndeterminate(false);
    setCheckAll(e.target.checked);
    setReleaseDis(false);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setSelectKey(check);
    setNewSelectedRows(
      [...dataSource].filter((d: any) => check.includes(d.contentId_)),
    );

    setPublishSelectedRows(
      [...dataSource]
        .filter((d: any) => check.includes(d.contentId_))
        .filter((d: any) => d.publishStatus == 1),
    );
    setIndeterminate(!!check.length && check.length < dataSource.length);
    setCheckAll(check.length === dataSource.length);
  };
  const [param, setParam] = useState<any>({
    page: 1,
    size: 50,
    course_name: '',
    course_form: t('班级课'),
  });
  const [courseTotal, setCourseTotal] = useState<any>(0);
  useEffect(() => {
    if (createModalVisible) {
      //查询当前学期
      // fetchSemeter().then(ress => {
      //   console.log(ress);
      //   getCourse_floor(
      //     (userInfo.userCode === 'admin' || userInfo.userCode === 'sys')
      //       ? {
      //         ...param,
      //         semester: ress.extendMessage.name,
      //       }
      //       : {
      //         ...param,
      //         teacher: userInfo.userCode,
      //         semester: ress.extendMessage.name,
      //         // teacher:'zj2018326050004'
      //       },
      //   ).then(res => {
      //     if (res.error_code === 'cloud_sc.0000.0000' && res.extend_message) {
      //       setCourseTotal(res.extend_message.count)
      //       if (param.page === 1) {
      //         setOfflineCourse(res.extend_message.results);
      //       } else {
      //         let arr =
      //           [
      //             ...offlineCourse,
      //             ...res.extend_message.results,
      //           ]
      //         setOfflineCourse(arr);
      //       }
      //     }
      //   });
      // });
      // });
      // getCourse_floor({
      //   page: 1,
      //   size: 50,
      //   course_form: 'SPOC课程',
      //   teacher:userCode
      // }).then(res => {
      //   if (res.error_code === 'cloud_sc.0000.0000' && res.extend_message) {
      //     setOfflineCourse(res.extend_message.results);
      //   }
      // });
      // getOfflineCourse({
      //   pageIndex: 1,
      //   pageSize: 50,
      // }).then((res) => {
      //   if (res.errorCode === 'course_0000_0000' && res.extendMessage) {
      //     setOfflineCourse(
      //       res.extendMessage.results.map((item: any) => item.entityData),
      //     );
      //   }
    }
  }, [createModalVisible, param]);
  useEffect(() => {
    let offshelf = newSelectedRows.some(
      (item: any) => item.publishStatus === 0 || item.publishStatus === 2,
    );
    setOffShelfDis(offshelf);
    let release = newSelectedRows.some((item: any) => {
      return item.publishStatus !== 0;
    });
    setReleaseDis(release);
    setRecallButton(
      newSelectedRows.some((item: any) => {
        return item.publishStatus !== 2;
      }),
    );
  }, [newSelectedRows]);
  useEffect(() => {
    if ('semester_teaching_courses' in query || isTraining.current) {
      fetchDataList();
    }
  }, [query]); //记住该页码大小
  const pageChange = (pageIndex: any, pageSize: any) => {
    setQuery({ ...query, pageIndex, pageSize });
    localStorage.setItem('spooclist_size', pageSize);
  };
  const { getOptAuth, getPermission } = usePermission();
  const columns: ColumnsType<any> = [
    {
      title: '',
      dataIndex: 'check',
      key: 'check',
      align: 'center',
      width: 50,
      fixed: 'left',
      render: (_text: any, record: any) => (
        <Checkbox value={record.contentId_} />
      ),
    },
    {
      title: t('课程封面'),
      dataIndex: 'cover',
      align: 'center',
      key: 'cover',
      width: 120,
      render: (text: any) => <Image width={'100%'} src={text} />,
    },
    {
      title: t('课程名称'),
      dataIndex: 'name',
      align: 'center',
      key: 'name',
      width: 270,
      ellipsis: {
        showTitle: true,
      },
      onCell: () => ellipsisSetting(270),
    },
    {
      title: t('教师'),
      dataIndex: 'teacher_names',
      align: 'center',
      key: 'teacher_names',
      width: 180,
      ellipsis: true,
      render: (text: any) => {
        return text?.join('，');
      },
      onCell: () => ellipsisSetting(180),
    },
    // {
    //   title: '学科',
    //   dataIndex: 'subject',
    //   key: 'subject',
    // },
    {
      title: t('开课学院'),
      dataIndex: 'collegeName',
      key: 'collegeName',
      align: 'center',
      width: 180,
      ellipsis: {
        showTitle: false,
      },
      render: (text: { value: string }[]): React.ReactNode => {
        return (
          <Tooltip
            placement="topLeft"
            title={(text || []).join(',')}
            mouseEnterDelay={0.8}
          >
            <div
              style={{
                maxWidth: '300px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {(text || []).join(',')}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: t('所属学科'),
      dataIndex: 'subjectName',
      align: 'center',
      width: 150,
      // ellipsis: true,
      render: (text: any, record: any) => {
        return text?.join('，');
      },
      onCell: () => ellipsisSetting(150),
    },
    {
      title: t('开课时间'),
      dataIndex: '',
      key: 'start_time',
      ellipsis: {
        showTitle: false,
      },
      width: 180,
      align: 'center',
      render: (text: number, record: any): React.ReactNode => {
        return (
          <Tooltip
            placement="topLeft"
            title={
              moment(record.start_time).format('YYYY年MM月DD日') +
              ' - ' +
              moment(record.end_time).format('YYYY年MM月DD日')
            }
            mouseEnterDelay={0.8}
          >
            {moment(record.start_time).format('YYYY年MM月DD日') +
              ' - ' +
              moment(record.end_time).format('YYYY年MM月DD日')}
          </Tooltip>
        );
      },
    },
    {
      title: t('所属学期'),
      dataIndex: 'semester_teaching_courses',
      key: 'semester_teaching_courses',
      width: 100,
      align: 'center',
      render: value => {
        const cur = semesters?.filter(
          (item: ISemesters) => item.name === value,
        )?.[0];
        return cur ? cur?.showSemester ?? cur.name : value;
      },
    },
    {
      title: t('发布状态'),
      dataIndex: 'publishStatus',
      key: 'publishStatus',
      align: 'center',
      width: 100,
      render: value => {
        const flag = value === 0;
        // '未发布' : '已发布';
        return (
          <span style={{ color: flag ? '#B5B5B5' : 'rgba(0, 0, 0, 0.85)' }}>
            {flag ? t('未发布') : t('已发布')}
          </span>
        );
      },
    },
    {
      title: t('课程状态'),
      dataIndex: 'courseStatus',
      key: 'courseStatus',
      align: 'center',
      width: 100,
      render: text => {
        const flag = text === 1;
        return (
          <span
            style={{
              color: flag ? '#FF913D' : text === 2 ? '#6BBF51' : '#B5B5B5',
            }}
          >
            {flag ? t('未开始') : text === 2 ? t('进行中') : t('已结束')}
          </span>
        );
      },
    },
    {
      title: t('参与人数'),
      dataIndex: 'joinUserSum',
      key: 'joinUserSum',
      align: 'center',
      width: 100,
    },
    {
      title: t('课程分类'),
      dataIndex: 'classificationName',
      align: 'center',
      // ellipsis: true,
      render: (text: Array<{ code: string; value: string }>) => {
        return text?.length > 0 ? text?.join('，') : '';
      },
      width: 120,
    },
    {
      title: t('推送平台'),
      dataIndex: 'pushType',
      align: 'center',
      width: 120,
      // ellipsis: true,
      render: (text: Array<{ code: string; value: string }>) => {
        return text?.length > 0
          ? pushList
            .filter((item: any) => text.includes(item.value))
            .map(item => item.label)
            .join('，')
          : '';
      },
    },
    {
      title: t('创建人'),
      dataIndex: 'createUser_',
      align: 'center',
      width: 180,
      render: (text: Array<{ code: string; value: string }>) => {
        return text.length > 0 ? text[0].value : '';
      },
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate_',
      align: 'center',
      render: (createDate_: string) => {
        return createDate_;
      },
      width: 180,
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      align: 'center',
      width: 200,
      key: 'action',
      fixed: 'right',
      render: (text, record: any) => {
        return (
          <span>
            <Tooltip title={t('预览')}>
              <Button
                type="text"
                icon={<IconFont type="iconviews" />}
                onClick={() => {
                  window.open(
                    `/learn/course/preview/${COURSE_TYPE[record.courseType]}/${record.contentId_
                    }?preview=1&show=1`,
                  );
                }}
              />
            </Tooltip>
            {getOptAuth('edit', record.courseType, [2, 3]) && (
              <Tooltip title={t('编辑')}>
                <Button
                  type="text"
                  icon={<IconFont type="iconedit" />}
                  onClick={() => {
                    window.open(
                      `#/editcourse/baseInfo?id=${record.key}&type=${COURSE_TYPE[record.courseType]
                      }&sm=${record.course_semester_id}`,
                    );
                  }}
                />
              </Tooltip>
            )}

            {record.publishStatus === 0
              ? getOptAuth('publish', record.courseType, [2, 3]) && (
                <Tooltip title={t('发布')}>
                  <Button
                    type="text"
                    icon={<IconFont type="iconrelease" />}
                    onClick={() => {
                      setOneOrBatch(true);
                      setOperationData(record);
                      setReleaseOrNot(1);
                      setRelease(true);
                    }}
                  />
                </Tooltip>
              )
              : record.publishStatus === 1
                ? getOptAuth('remove', record.courseType, [2, 3]) && (
                  <Tooltip title={t('下架')}>
                    <Button
                      type="text"
                      icon={<IconFont type="iconoffShelf" />}
                      disabled={!buttonPermission.includes('spoc_remove')}
                      onClick={() => {
                        setOneOrBatch(true);
                        setOperationData(record);
                        setReleaseOrNot(0);
                        setRelease(true);
                      }}
                    />
                  </Tooltip>
                )
                : getPermission(
                  ['spoc', 'training'],
                  '_course_release_review',
                  true,
                  isTraining.current ? 'training' : 'spoc',
                ) && (
                  <Tooltip title={t('撤回')}>
                    <Button
                      type="text"
                      icon={<IconFont type="iconrecall" />}
                      onClick={() => {
                        setOneOrBatch(true);
                        setOperationData(record);
                        setReleaseOrNot(0);
                        setRelease(true);
                        setIsReject(true);
                      }}
                    />
                  </Tooltip>
                )}

            {getOptAuth('copy', record.courseType, [2, 3]) && (
              <Tooltip title={t('复制')}>
                <Button
                  type="text"
                  icon={<IconFont type="iconcopy" />}
                  onClick={() => {
                    setOneOrBatch(true);
                    setOperationData(record);
                    setIsCopyModalVisible(true);
                  }}
                />
              </Tooltip>
            )}

            {record.publishStatus === 0 &&
              getOptAuth('delete', record.courseType, [2, 3]) && (
                <Tooltip title={t('删除')}>
                  <Button
                    type="text"
                    icon={<IconFont type="icondelete" />}
                    onClick={() => {
                      setOneOrBatch(true);
                      setOperationData(record);
                      setIsDeleteModalVisible(true);
                    }}
                  />
                </Tooltip>
              )}
          </span>
        );
      },
    },
  ];

  const handleSearch = (value: string) => {
    setParam({
      ...param,
      page: 1,
      course_name: value,
    });
  };
  // const handleChange = (value: string) => {
  //   if (!value || value.length) {
  //     setParam({
  //       ...param,
  //       page:1,
  //       course_name:
  //     })
  //   }
  // };
  const handleScroll = (e: any) => {
    const { target } = e;
    const total = target.scrollTop + target.offsetHeight;
    const scrollHeight = target.scrollHeight;
    if (
      total >= scrollHeight - 1 &&
      total < scrollHeight + 1 &&
      param.page < Math.ceil(courseTotal / param.size) //向上取整
    ) {
      setParam({
        ...param,
        page: param.page + 1,
      });
    }
  };
  const onTeacherChange = (value: any): void => {
    form.setFieldsValue({ teacher: value });
  };

  // 导出课程word
  const exportWord = () => {
    confirm({
      title: t('导出'),
      content: t('确认导出课程资源包?'),
      onOk: async () => {
        const tasks = selectKey.map(item => () => exportword([item]));
        const names = selectKey.map(
          item => dataSource.find((el: any) => el.contentId_ === item)?.name,
        );
        for (let i = 0; i < tasks.length; i++) {
          const res = await tasks[i]();
          if (res?.status !== 500) {
            download(res, names[i]);
          }
        }
        setSelectKey([]);
        setRelease(false);
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  // 下载文件
  const download = (blob: any, name: string) => {
    // let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });//type是文件类，详情可以参阅blob文件类型
    // 创建新的URL并指向File对象或者Blob对象的地址
    const blobURL = window.URL.createObjectURL(blob);
    // 创建a标签，用于跳转至下载链接
    const tempLink = document.createElement('a');
    tempLink.style.display = 'none';
    tempLink.href = blobURL;
    tempLink.setAttribute('download', `${name}${t('资源包.zip')}`);
    // 兼容：某些浏览器不支持HTML5的download属性
    if (typeof tempLink.download === 'undefined') {
      tempLink.setAttribute('target', '_blank');
    }
    // 挂载a标签
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    // 释放blob URL地址
    window.URL.revokeObjectURL(blobURL);
  };

  // 发布课程敏感词检测
  const checkCourseContent = () => {
    // 仅发布操作：发布前先进行敏感词检测
    if (releaseOrNot) {
      const publishIds = operationData?.contentId_ ? [operationData?.contentId_] : selectKey;
      const params = {
        coursePublishInfos: publishIds.map(item => ({ courseId: item })),
        courseType: query.courseType,
      }
      setReleaseLoading(true);
      courseOfSensitivew(params).then((res) => {
        if (res?.statusCode === 200) {
          setSensitiveWordInfos(res.data || []);
        }
      })
        .finally(() => { setReleaseLoading(false); })
    }
  }

  useEffect(() => {
    // 发布弹窗时，先进行敏感词检测
    if (release && releaseOrNot == 1) {
      checkCourseContent();
    }
  }, [release, releaseOrNot]);

  const pushValueChange = (value: any[]) => {
    setPushValue(value);
  };
  const pushLevelChange = (e: any) => {
    setPushLevel(e.target.value);
  };

  const handlePushConfirm = () => {
    if (pushValue.length <= 0) {
      message.warning(t('请选择一个推送平台'));
      return;
    }
    if (cascaderValue.length <= 0) {
      message.warning(t('请选择推送学科'));
      return;
    }
    pushCourses(
      pushValue.join(','),
      { school: pushLevel, subjectCategory: cascaderValue.join('/') },
      selectKey,
    ).then((res: any) => {
      if (res.status === 200) {
        message.success(t('推送成功'));
        fetchDataList();
        handlePushCancel();
      } else {
        message.error(t('推送失败'));
      }
    });
  };

  const handlePushCancel = () => {
    setPushValue([]);
    setPushLevel(1);
    setCascaderValue('');
    setPushPlatVisible(false);
  };

  const showPushModal = () => {
    if (newSelectedRows.length === 1) {
      setPushValue(newSelectedRows[0].pushType ?? []);
    }
    setPushPlatVisible(true);
  };

  const onTreeValueChange = (value: any) => {
    setCascaderValue(value);
  };

  //添加认证
  const showAuthenticationModal = () => {
    authenticationModalRef?.current?.open(publishSelectedRows);
  };
  //取消认证
  const cancelAuthentication = () => {
    authenticationModalRef?.current?.cancel(publishSelectedRows);
  };
  const handleCourseNameChange = (e: any) => {
    // 添加限制：输入字符不超过50字符
    console.log('input', e.target.value);
    const input = e.target.value;
    if (input.length > 50) {
      message.error(t('名称最多50个字符'));
      return;
    }

      setCourseName(input);
  };
  //按钮列表
  let btn_list: any = [];
  if (getOptAuth('publish', isTraining.current ? 3 : 2, [2, 3])) {
    btn_list.push({
      title: t('发布'),
      func: () => {
        setReleaseOrNot(1);
        batchRelease();
      },
      disabled: releaseDis || !selectKey.length,
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconrelease" />}
          onClick={() => {
            setReleaseOrNot(1);
            batchRelease();
          }}
          disabled={releaseDis || !selectKey.length}
        >
          {t('发布')}
        </Button>
      ),
    });
  }

  if (getOptAuth('remove', isTraining.current ? 3 : 2, [2, 3])) {
    btn_list.push({
      title: t('下架'),
      func: () => {
        setReleaseOrNot(0);
        batchRelease();
      },
      // offShelfDis || !selectKey.length
      disabled:offShelfDis || !selectKey.length || !buttonPermission.includes('spoc_remove'),
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconoffShelf" />}
          onClick={() => {
            setReleaseOrNot(0);
            batchRelease();
          }}
          disabled={offShelfDis || !selectKey.length || !buttonPermission.includes('spoc_remove')}
        >
          {t('下架')}
        </Button>
      ),
    });
  }
  if (
    getPermission(
      ['spoc', 'training'],
      '_course_release_review',
      true,
      isTraining.current ? 'training' : 'spoc',
    )
  ) {
    btn_list.push({
      title: t('撤回'),
      disabled: recallButton || !selectKey.length,
      func: () => {
        setReleaseOrNot(0);
        batchRelease();
        setIsReject(true);
      },
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconrecall" />}
          onClick={() => {
            setReleaseOrNot(0);
            batchRelease();
            setIsReject(true);
          }}
          disabled={recallButton || !selectKey.length}
        >
          {t('撤回')}
        </Button>
      ),
    });
  }
  if (getOptAuth('delete', isTraining.current ? 3 : 2, [2, 3])) {
    btn_list.push({
      title: t('删除'),
      func: () => batchDelete(),
      disabled: releaseDis || !selectKey.length,
      dom: (
        <Button
          type="text"
          ghost
          onClick={batchDelete}
          icon={<IconFont type="icondelete" />}
          disabled={releaseDis || !selectKey.length}
        >
          {t('删除')}
        </Button>
      ),
    });
  }
  if (getOptAuth('package_export', isTraining.current ? 3 : 2, [2, 3])) {
    btn_list.push({
      title: t('导出'),
      // func:()=>exportWord(),
      disabled: !selectKey.length,
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconexport" />}
          onClick={exportWord}
          disabled={!selectKey.length}
        >
          {t('导出')}
        </Button>
      ),
    });
  }
  if (getOptAuth('push', isTraining.current ? 3 : 2, [2, 3])) {
    btn_list.push({
      title: t('推送'),
      func: () => showPushModal(),
      disabled: !selectKey.length,
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconpush" />}
          onClick={showPushModal}
          disabled={!selectKey.length}
        >
          {t('推送')}
        </Button>
      ),
    });
  }

  if (
    (userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_sys_manager') ||
      userInfo.roles
        ?.map((item: any) => item.roleCode)
        ?.includes('r_course_manager')) &&
    getPermission(
      ['spoc', 'training'],
      '_certification_display',
      true,
      isTraining.current ? 'training' : 'spoc',
    )
  ) {
    btn_list.push({
      title: t('校内认证'),
      func: () => showAuthenticationModal(),
      disabled: !publishSelectedRows.length,
      dom: (
        <Button
          type="text"
          icon={<IconFont type="iconauthentication" />}
          onClick={showAuthenticationModal}
          disabled={!publishSelectedRows.length}
        >
          {t('校内认证')}
        </Button>
      ),
    });
    const disable = !publishSelectedRows.filter(
      (d: any) => d.certification_type && d.inside_isbn,
    ).length;
    btn_list.push({
      title: t('取消校内认证'),
      func: () => cancelAuthentication(),
      disabled: disable,
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconcancelAuthentication" />}
          disabled={disable}
          onClick={() =>
            confirmModal('确认取消校内认证?', () => cancelAuthentication())
          }
        >
          {t('取消校内认证')}
        </Button>
      ),
    });
  }

  return (
    <div className="sp-page-wrapper">
      <div className="sp-page-content">
        <div className="mode-wrp">
          {mobileFlag ? (
            <MobileSearch
              resourceSearch={onSearch}
              selected={isTraining.current ? 'training' : 'spoc'}
              form={form}
              reset={onReset}
              isShowSemester={!isTraining.current}
              showPersonal={isSuper && personalMode}
            />
          ) : (
            <>
              <Form
                layout="inline"
                name="basic"
                form={form}
                onFinish={onSearch}
                style={{
                  display:
                    isTraining.current && personalMode ? 'none' : 'block',
                }}
              >
                {isSuper && !personalMode ? (
                  <div className="search-wrp">
                    <Form.Item name="courseName">
                      <Input
                        placeholder={t('搜索课程名称')}
                        autoComplete="off"
                        style={{ width: 250 }}
                        onPressEnter={() => {
                          form.submit();
                        }}
                      />
                    </Form.Item>

                    <Form.Item name="teacher">
                      <TurnThePageDataItem
                        message={t('请选择教师')}
                        type={0}
                        value={undefined}
                        onTeacherChange={onTeacherChange}
                      />
                    </Form.Item>
                    <Form.Item name="pubish">
                      <Select
                        placeholder={t('请选择发布状态')}
                        allowClear
                        style={{ width: 180 }}
                      >
                        <Option value="0">{t('全部')}</Option>
                        <Option value="1">{t('已发布')}</Option>
                        <Option value="-1">{t('未发布')}</Option>
                        {getPermission(
                          ['spoc', 'training'],
                          '_course_release_review',
                          true,
                          isTraining.current ? 'training' : 'spoc',
                        ) && <Option value="2">{t('待审核')}</Option>}
                      </Select>
                    </Form.Item>
                    {!isTraining.current && (
                      <Form.Item name="semester_teaching_courses">
                        <Select
                          placeholder={t('所属学期')}
                          allowClear
                          style={{ width: 180 }}
                        >
                          <Option key={Date.now()} value={''}>
                            {t('全部学期')}
                          </Option>
                          {semesters.map((item: ISemesters) => (
                            <Option key={item.id} value={item.name}>
                              {item.showSemester || item.name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    )}
                    {/* <Form.Item name="isend">
                   <Select
                     placeholder="课程发布状态"
                     allowClear
                     style={{ width: 180 }}
                   >
                     <Option value={''}>全部</Option>
                     <Option value={'2'}>进行中</Option>
                     <Option value={'1'}>未开始</Option>
                     <Option value={'-1'}>已结束</Option>
                   </Select>
                  </Form.Item> */}

                    {/* <Form.Item name="classificationId">
                   <Select
                     placeholder="请选择课程分类"
                     allowClear
                     style={{ width: 150 }}
                   >
                     {courseTypeList.map((item: any) => (
                       <Option key={item.code} value={item.code}>{item.name}</Option>
                     ))}
                   </Select>
                  </Form.Item> */}
                    {!isTraining.current && (
                      <Form.Item name="subjectId">
                        {/* <Select
                     placeholder="请选择学科"
                     allowClear
                     style={{ width: 180 }}
                    >
                       {subjectList.map((item: any) => (
                       <Option key={item.id} value={item.id}>{item.name}</Option>
                     ))}
                    </Select> */}

                        <TreeSelect
                          placeholder={t('学科专业')}
                          allowClear
                          treeData={subjectList}
                          showArrow
                          // treeNodeFilterProp={{}}
                          fieldNames={{
                            label: 'categoryName',
                            value: 'categoryCode',
                            children: 'children',
                          }}
                          style={{ width: '180px' }}
                        />
                      </Form.Item>
                    )}
                    {/* {
                   jurisdictionList?.includes(perCfg.spoc_push) &&
                   <Form.Item name="pushType">
                     <Select
                       placeholder="请选择推送平台"
                       allowClear
                       style={{ width: 180 }}
                     >
                       {pushList.map((item: any) => (
                         <Option key={item.value} value={item.value}>{item.label}</Option>
                       ))}
                     </Select>
                   </Form.Item>
                  } */}
                    <div className="reset-wrp" onClick={onReset}>
                      <span>{t('清空')}</span>
                      <ReloadOutlined />
                    </div>
                    <Button type="primary" htmlType="submit">
                      {t('搜索')}

                      <IconFont type="iconsousuo2" />
                    </Button>
                  </div>
                ) : (
                  <Form.Item name="semester_teaching_courses">
                    {!isTraining.current && (
                      <Select
                        className="teacher-semester"
                        bordered={false}
                        onChange={() => form.submit()}
                      >
                        <Option key={Date.now()} value={''}>
                          {t('全部学期')}
                        </Option>
                        {semesters.map((item: ISemesters) => (
                          <Option key={item.id} value={item.name}>
                            {item.showSemester || item.name}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                )}
              </Form>
              {isSuper && isTraining.current && !personalMode ? (
                <div
                  className="mode-btn"
                  onClick={() => {
                    onReset();
                    // if (!personalMode) {
                    form.setFieldsValue({
                      semester_teaching_courses: curSemester,
                    });
                    setQuery({
                      pageIndex: 1,
                      pageSize: query.pageSize,
                      courseType: query.courseType,
                      semester_teaching_courses: curSemester,
                      personalMode: !personalMode,
                    });
                    setPersonalMode(!personalMode);
                    if (!personalMode) {
                      setModeSwitch(true);
                    }
                    // }
                  }}
                >
                  {t('切换')}
                  {personalMode ? t('管理') : t('个人')}
                  {t('模式')}
                </div>
              ) : (
                isSuper &&
                !isTraining.current && (
                  <div
                    className="mode-btn"
                    onClick={() => {
                      onReset();
                      // if (!personalMode) {
                      form.setFieldsValue({
                        semester_teaching_courses: curSemester,
                      });
                      setQuery({
                        pageIndex: 1,
                        pageSize: query.pageSize,
                        courseType: query.courseType,
                        semester_teaching_courses: curSemester,
                        personalMode: !personalMode,
                      });
                      setPersonalMode(!personalMode);
                      if (!personalMode) {
                        setModeSwitch(true);
                      }
                      // }
                    }}
                  >
                    {t('切换')}
                    {personalMode ? t('管理') : t('个人')}
                    {t('模式')}
                  </div>
                )
              )}
            </>
          )}
        </div>
        {isTraining.current ||
          (isSuper && (!personalMode || mobileFlag) && (
            <div className="splitLine"></div>
          ))}
        <div className="button_box">
          <Space>
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              {t('全部')}
            </Checkbox>
            <div className="btn-wrp">
              {getOptAuth('new', isTraining.current ? 3 : 2, [2, 3]) && (
                <Button
                  type="primary"
                  shape="round"
                  icon={<PlusCircleFilled />}
                  onClick={() => {
                    if (mobileFlag) {
                      message.info(t('暂不支持手机端，请前往电脑端操作'));
                    } else {
                      setCreateModalVisible(true);
                    }
                  }}
                >
                  {t('新建')}
                </Button>
              )}
              {!mobileFlag ? (
                btn_list.map((item: any, index: number) => {
                  return (
                    <div
                      className={`item_${item.disabled ? ' disabled' : ''}`}
                      key={index}
                    >
                      {item.dom}
                    </div>
                  );
                })
              ) : (
                //移动端取前两个展示即可
                <>
                  {/* {
                   btn_list.slice(0,1).map((item:any,index:number)=>{
                     return <div className={`item_${item.disabled?' disabled':''}`} key={index}>{item.dom}</div>
                   })
                  } */}
                  {btn_list.length > 0 && (
                    <Popover
                      className="mobile_btns_popover"
                      onOpenChange={(newOpen: boolean) =>
                        setOpreatMenuVisible(newOpen)
                      }
                      open={operatMenuVisible}
                      content={
                        <div className="mobile_btns">
                          {btn_list.map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className={item.disabled ? 'disabled' : ''}
                                onClick={() => {
                                  if (!item.disabled) {
                                    setOpreatMenuVisible(false);
                                    // item?.func();
                                  }
                                }}
                              >
                                {item.dom}
                              </div>
                            );
                          })}
                        </div>
                      }
                    >
                      <Button
                        onClick={(e: any) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setOpreatMenuVisible(!operatMenuVisible);
                        }}
                      >
                        <IconFont type="iconziyuanku1" />
                        {t('管理')}
                      </Button>
                    </Popover>
                  )}

                  {isSuper && !personalMode && (
                    <div
                      className="mode-btn"
                      onClick={() => {
                        onReset();
                        // if (!personalMode) {
                        form.setFieldsValue({
                          semester_teaching_courses: curSemester,
                        });
                        setQuery({
                          pageIndex: 1,
                          pageSize: query.pageSize,
                          courseType: query.courseType,
                          semester_teaching_courses: curSemester,
                          personalMode: !personalMode,
                        });
                        setPersonalMode(!personalMode);
                        if (!personalMode) {
                          setModeSwitch(true);
                        }
                        // }
                      }}
                    >
                      {t('切换')}
                      {personalMode ? t('管理') : t('个人')}
                      {t('模式')}
                    </div>
                  )}
                </>
              )}
            </div>
            {!personalMode ? (
              <div className="mode_switch_wrapper">
                <div
                  onClick={() => setModeSwitch(true)}
                  className="mode_switch"
                >
                  <Tooltip title={t('图例模式')}>
                    <IconFont
                      type="iconhebingxingzhuangfuzhi2"
                      className={modeSwitch ? 'active' : ''}
                    />
                  </Tooltip>
                </div>
                <div
                  onClick={() => setModeSwitch(false)}
                  className="mode_switch"
                >
                  <Tooltip title={t('列表模式')}>
                    <IconFont
                      type="iconliebiao"
                      className={modeSwitch ? '' : 'active'}
                    />
                  </Tooltip>
                </div>
              </div>
            ) : (
              <div className="mode-wrp">
                <Input
                  placeholder={t('搜索课程名称')}
                  onChange={(e: any) => setSearchValue(e.target.value)}
                  suffix={
                    <IconFont
                      type="iconsousuo2"
                      onClick={(e: any) => {
                        setQuery({
                          ...query,
                          courseName: searchValue,
                        });
                      }}
                    />
                  }
                  autoComplete="off"
                  style={{ width: 200 }}
                  onPressEnter={(e: any) => {
                    setQuery({
                      ...query,
                      courseName: e.target.value,
                    });
                  }}
                />
                {isSuper && isTraining.current ? (
                  <div
                    className="mode-btn"
                    style={{ marginLeft: '20px' }}
                    onClick={() => {
                      onReset();
                      // if (!personalMode) {
                      form.setFieldsValue({
                        semester_teaching_courses: curSemester,
                      });
                      setQuery({
                        pageIndex: 1,
                        pageSize: query.pageSize,
                        courseType: query.courseType,
                        semester_teaching_courses: curSemester,
                        personalMode: !personalMode,
                      });
                      setPersonalMode(!personalMode);
                      if (!personalMode) {
                        setModeSwitch(true);
                      }
                      // }
                    }}
                  >
                    {t('切换')}
                    {personalMode ? t('管理') : t('个人')}
                    {t('模式')}
                  </div>
                ) : null}
              </div>
            )}
          </Space>
        </div>
        <Checkbox.Group
          value={selectKey}
          onChange={onChange}
          className={personalMode ? 'personalMode' : ''}
          style={{ width: '100%' }}
        >
          {modeSwitch ? (
            <div className="data_wrapper">
              {dataSource && dataSource.length > 0 ? (
                dataSource.map((item: any) => (
                  <CourseBlock
                    key={item.contentId_}
                    item={item}
                    onEdit={() => {
                      if (mobileFlag) {
                        message.info(t('暂不支持手机端，请前往电脑端操作'));
                      } else {
                        window.open(
                          `#/editcourse/baseInfo?id=${item.contentId_}&type=${COURSE_TYPE[item.courseType]
                          }&sm=${item.course_semester_id}`,
                        );
                      }
                    }}
                    onDelete={() => {
                      setOneOrBatch(true);
                      setOperationData(item);
                      setIsDeleteModalVisible(true);
                    }}
                    onPublish={() => {
                      setOneOrBatch(true);
                      setOperationData(item);
                      setReleaseOrNot(1);
                      setRelease(true);
                    }}
                    onUnPublish={() => {
                      setOneOrBatch(true);
                      setOperationData(item);
                      setReleaseOrNot(0);
                      setRelease(true);
                      if (item.publishStatus === 2) {
                        setIsReject(true);
                      }
                    }}
                    onPreview={() =>
                      window.open(
                        `/learn/course/preview/${COURSE_TYPE[item.courseType]
                        }/${item.contentId_}?preview=1&show=1&type=released`,
                      )
                    }
                    onCopy={() => {
                      setOneOrBatch(true);
                      setOperationData(item);
                      setIsCopyModalVisible(true);
                    }}
                  />
                ))
              ) : (
                <Empty style={{ width: '100%' }} />
              )}
            </div>
          ) : (
            <Table
              scroll={{ x: 'max-content', y: 'calc(100vh - 320px)' }}
              // rowSelection={rowSelection}
              rowKey="contentId_"
              columns={columns}
              size="small"
              dataSource={dataSource}
              loading={tableLoading}
              pagination={false}
            />
          )}
        </Checkbox.Group>
        {total > 0 && (
          <Pagination
            style={{ textAlign: 'center', marginTop: 10 }}
            {...{
              current: query.pageIndex,
              pageSize: query.pageSize,
              total: total,
              onChange: pageChange,
              showQuickJumper: true,
              defaultCurrent: 1,
              size: 'small',
              showTotal: total => t('共{name}条', String(total)),
              showSizeChanger: true,
              pageSizeOptions: ['24', '36', '48', '60'],
            }}
          />
        )}
        <Modal
          title={t('删除')}
          visible={isDeleteModalVisible}
          onOk={handleDeleteOk}
          onCancel={() => setIsDeleteModalVisible(false)}
        >
          {t('确定要删除该课程吗？')}
        </Modal>
        <Modal
          title={t('复制')}
          visible={isCopyModalVisible}
          onOk={handleCopyOk}
          onCancel={() => setIsCopyModalVisible(false)}
          confirmLoading={copyLoading}
        >
          {t('确定要复制课程吗？')}
        </Modal>
        <Modal
          title={t('新建课程')}
          visible={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          onOk={handleCreate}
          confirmLoading={createLoading}
        >
          {/* <Form
             form={createForm}
             initialValues={{}}
             onFinish={handleCreate}
             labelCol={{ span: 6 }}
             wrapperCol={{ span: 16 }}
            >
             <Form.Item
               name="course"
               label="选择课程"
               rules={[{ required: true, message: '请选择课程' }]}
             >
               <Select
                 style={{ width: '100%' }}
                 showSearch
                 onSearch={handleSearch}
                 onPopupScroll={handleScroll}
                 filterOption={false}
                 // onChange={handleChange}
                 // key={JSON.stringify(offlineCourse)}
                 allowClear={true}
               >
                 {offlineCourse.map((course: any) => (
                   <Select.Option
                     key={course.id}
                     value={course.id}
                   >{`${course.course_name}（${course.course_no}）`}</Select.Option>
                 ))}
               </Select>
             </Form.Item>
            </Form> */}
          <div className="course_name_container">
            <label>
              <span className="course_label">{t('课程名称：')}</span>
              <Input
                style={{ width: 300 }}
                placeholder={t('请输入课程名称')}
                value={courseName}
                onChange={handleCourseNameChange}
                required
                onKeyDown={(e: any) => {
                  e.keyCode === 13 && handleCreate();
                }}
              />
            </label>
            {/* <div className="course_tip">{courseTip}</div> */}
          </div>
        </Modal>
        <Modal
          title={
            releaseOrNot ? t(`发布${sensitiveWordInfos?.length > 0 ? '（课程包含敏感内容）' : ''}`) : `${isReject ? t('撤回') : t('下架')}`
          }
          visible={release}
          confirmLoading={publishLoading}
          onOk={handleReleseOK}
          onCancel={() => setRelease(false)}
        >
          <Spin spinning={releaseLoading}>
            {t('确定要')}
            {releaseOrNot
              ? <>
                <>
                  {getPermission(
                    ['spoc', 'training'],
                    '_course_release_review',
                    true,
                    isTraining.current ? 'training' : 'spoc',
                  )
                    ? t('发布后将提交至管理员进行审核发布，是否确认提交？')
                    : t('发布该课程？')
                  }
                </>
                {/* {sensitiveMsgListDom(sensitiveWordInfos)} */}
              </>
              : `${isReject
                ? t('撤回课程发布审核？')
                : t('下架该课程？')
              }`}
          </Spin>
        </Modal>
        <Modal
          title={t('选择推送平台')}
          visible={pushPlatVisible}
          onOk={handlePushConfirm}
          onCancel={handlePushCancel}
        >
          <div className="checkbox-container">
            <span>{t('推送平台：')}</span>
            <Checkbox.Group value={pushValue} onChange={pushValueChange}>
              {pushList.map((item: any) => (
                <Checkbox key={item} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </Checkbox.Group>
          </div>
          <div>
            <span>{t('推送等级：')}</span>
            <Radio.Group
              options={pushLevelList}
              value={pushLevel}
              onChange={pushLevelChange}
            ></Radio.Group>
          </div>
          <div className="cascader-container">
            <span>{t('推送学科：')}</span>
            <Cascader
              style={{ width: '100%' }}
              value={cascaderValue}
              fieldNames={{
                label: 'categoryName',
                value: 'categoryName',
                children: 'children',
              }}
              options={cascaderOptions}
              placeholder={t('请选择推送学科')}
              onChange={onTreeValueChange}
            />
          </div>
        </Modal>
        <SelectTemplateModal
          onCancel={() => setTemplateModalVisible(false)}
          visible={templateModalVisible}
          onOk={handleOk}
          createLoading={createLoading}
        />

        <DeleteProcess
          title={t('删除进度')}
          visible={deleteProcessModalVisible}
          processId={processId}
          closeModal={() => setDeleteProcessModalVisible(false)}
          callback={fetchDataList}
        />

        <AuthenticationModal
          ref={authenticationModalRef}
          onConfirm={fetchDataList}
        />
      </div>
    </div>
  );
};

export default SpocListPage;
