.edit-course {
  width: 100%;
  height: 100%;
  min-width: 1200px;
  .ant-menu-item {
    padding-right: 0;
    margin: 0 13px;
    border-radius: 8px;
    width: calc(100% - 25px)!important;
    &.ant-menu-item-selected {
      background-color: var(--third-color)!important;
    }
    &:not(.ant-menu-item-selected) a {
      color: #525252;
    }
  }
  .publishStatus {
    // position: absolute;
    top: 6px;
    margin-left: 15px;
    color: #fff;
    height: 22px;
    line-height: 22px;
    padding: 0 10px;
    font-size: 14px;
    display: inline-block;
    border-radius: 4px;
    user-select: none;
  }
  .published {
    background: var(--primary-color);
  }
  .unpublished {
    background: #acacac;
  }

  .edit-top {
    height: 52px;
    width: 100%;
    // background: url(../../assets/imgs/EditCourse/banner2.png) no-repeat;
    // background: url(../../assets/imgs/EditCourse/bg2.png) no-repeat;
    // background-size: 100% 100%;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .top-right {
      display: flex;
      align-items: center;
      flex: 1;


      .info_box {
        display: flex;
        align-items: center;
        flex: 1;
        color: white;
        // flex-direction: column;
        font-size: 15px;
        color: #4a4f64;
        position: relative;
        a {
          margin-right: 20px;
          margin-top: 4px;
        }
        .course-title {
          max-width: calc(100vw - 400px);
          display: inline-block;
          font-size: 20px;
          overflow: hidden;
          text-overflow: ellipsis; //超出部分以省略号显示
          white-space: nowrap;
        }
        .course-data {
          margin-top: 6px;
          display: flex;
          align-items: center;
          .publish {
            font-weight: bold;
          }
        }
      }
      .buttom_box {
        //width: 100px;
        display: flex;
        justify-content: space-between;
        // margin-left: 50px;
        // padding-top: 10px;
        // button{
        //   margin-right: 20px;
        // }
      }
    }

    .top-left {
      display: flex;
      width: 402px;

      .course-data {
        margin-left: 60px;
        display: flex;

        .data {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin-left: 10px;

          span:nth-of-type(1) {
            font-size: 22px;
          }

          span:nth-of-type(2) {
            font-size: 13px;
          }
        }
      }

      .publish {
        color: #ff7947;
      }

      .browse {
        color: #ffcf47;
      }

      .participatein {
        color: #478cff;
      }
    }
  }

  .edit-detail {
    height: calc(100% - 104px);
    display: flex;

    .edit-menu {
      width: 232px;
      height: 100%;
      border: 1px solid #d9d9d9;
      padding-top: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #fff;

      .course-img {
        > img {
          width: 178px;
          border-radius: 4px;
          margin-bottom: 20px;
          object-fit: cover;
        }
      }
      .ant-menu-item span, .ant-menu-submenu-title {
        font-size: 16px;
      }
    }

    .edit-content {
      height: 100%;
      width: calc(100% - 232px);
      background-color: #f6f6f6;
      padding: 15px;
      overflow-y: auto;
    }
  }
}
.npu-app-container {
  .edit-course {
    .edit-top {
      height: 88px;
      .publishStatus {
        margin-left: 20px;
        font-style: italic;
        height: 28px;
        line-height: 28px;
        background-color: #F09429;
      }
      .course-title {
        color: #fff;
        font-size: 22px;
        font-weight: 500;
      }
      a {
        color: #fff;
        margin-right: 10px;
        .anticon {
          font-size: 22px;
        }
      }

    }
    .edit-detail {
      height: calc(100% - 88px);
      border-radius: 20px 20px 0 0;
      overflow: hidden;
      .edit-content {
        background-color: #fff;
      }
    }
  }
}
#root {
  height: 100%;
}
.btn_margin {
  margin-right: 16px;
}
.ant-modal-confirm .ant-modal-body {
  padding: 24px !important;
  padding-bottom: 12px !important;
}
