import React, { FC, ReactNode, useState, CSSProperties, useEffect } from "react";
import { HashRout<PERSON>, Link } from 'react-router-dom';
import { Layout, Menu, Tabs } from "antd";
import { useLocation, useSelector } from "umi";
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import NPUHeader from "@/components/NPUHeader";
import Header from "@/components/Header";
import LeftMenu from '@/components/LeftMenu';
import './index.less';
import useLocale from "@/hooks/useLocale";

interface IProps {
  children: ReactNode
}
interface MenuItems {
  key: string
  to: string
  title: string
}
const { Content, Sider } = Layout;
const ReviewCenterLayout: FC<IProps> = ({ children }) => {
  const { parameterConfig, buttonPermission } = useSelector<
    { global: any; },
    { buttonPermission: string[]; parameterConfig: any; }>(
      (state) => state.global);
  //移动端适配
  const { mobileFlag } = useSelector<{ config: any; }, any>(
    ({ config }) => config);
  const path = useLocation().pathname;
  const { t } = useLocale();
  const [menuItems, setMenuItems] = useState<MenuItems[]>([])

  const generateMenus = () => {
    // 每一个人都可能是流程审核的对象
    const items: MenuItems[] = [
      // {
      //   key: '/reviewcenter/process',
      //   to: '/reviewcenter/process',
      //   title: '流程审核'
      // },
    ]
    // 权限项
    if (buttonPermission.includes('import_review')) {
      items.unshift({
        key: '/reviewcenter/import',
        to: '/reviewcenter/import',
        title: '入库审核'
      })
    }
    if (buttonPermission.includes('share_review')) {
      items.unshift({
        key: '/reviewcenter/share',
        to: '/reviewcenter/share',
        title: '共享审核'
      })
    }
    // 先暂时屏蔽
    // if (
    //   parameterConfig?.microcourse_course_release_review === 'true' ||
    //   parameterConfig?.mooc_course_release_review === 'true' ||
    //   parameterConfig?.training_course_release_review === 'true' ||
    //   parameterConfig?.mooc_course_release_review === 'true'
    // ) {
    //   items.push({
    //     key: '/reviewcenter/course',
    //     to: '/reviewcenter/course',
    //     title: '课程审核'
    //   },)
    // }
    // items.push({
    //   key: '/reviewcenter/call',
    //   to: '/reviewcenter/call',
    //   title: '三方调用'
    // })
    items.push({
      key: '/reviewcenter/syllabus',
      to: '/reviewcenter/syllabus',
      title: '课程大纲发布'
    })
    setMenuItems(items)
  }

  useEffect(() => {
    generateMenus()
  }, [buttonPermission])

  const tabChange = () => {

  }

  const contentStyle: CSSProperties = {
    margin: '0 0 0 1px',
    minHeight: 280,
    overflowX: 'hidden',
    overflowY: 'auto'
  }

  return (
    <HashRouter>
      <div className={`App${mobileFlag ? ' mobileContainer' : ''}`}>
        <Layout>
          {parameterConfig.target_customer === CUSTOMER_NPU ? <NPUHeader /> : <Header />}
          <div className={`content-box`}>
            {parameterConfig.target_customer != null && parameterConfig.target_customer !== CUSTOMER_NPU && <LeftMenu />}
            <Layout>
              <Sider width={258} theme="light" className={`site-layout-background none`}>
                <Menu defaultSelectedKeys={[path]} mode="inline" theme="light">
                  {
                    menuItems.map(item => (
                      <Menu.Item key={item.key}>
                        <Link to={item.to}>{t(item.title)}</Link>
                      </Menu.Item>
                    ))
                  }
                </Menu>
              </Sider>
              <Layout className={mobileFlag ? 'mobile_right' : ''}>
                {
                  mobileFlag ?
                    <div className='mobile_container'>
                      <div className='tabs'>
                        <Tabs defaultActiveKey='/reviewcenter/resource' onChange={tabChange}>
                          {
                            menuItems.map(item => {
                              return <Tabs.TabPane key={item.key} tab={t(item.title)}></Tabs.TabPane>
                            })
                          }
                        </Tabs>
                      </div>
                      <Content className="site-layout-background" style={contentStyle}>
                        {children}
                      </Content>
                    </div> :
                    <Content className="site-layout-background" style={contentStyle}>
                      {children}
                    </Content>}
              </Layout>
            </Layout>
          </div>
        </Layout>
      </div>
    </HashRouter>
  )
}

export default ReviewCenterLayout
