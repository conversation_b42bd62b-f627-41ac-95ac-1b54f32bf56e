import { fetchEventSource } from '@microsoft/fetch-event-source';
import { Button, Checkbox, Col, Form, Input, InputNumber, Modal, Row, Select, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';
// @ts-ignore
import useLocale from '@/hooks/useLocale';
import type { FormProps } from 'antd';
const { Option } = Select;
const abortController = new AbortController();
const signal = abortController.signal;
interface IAutomaticProblem {
  selectKeys?: any;
  type?: string; // 单选还是多选
  visible: boolean;
  onConfirm: (data: any, type: any) => void;
  onAdd?: () => void;
  onclose: () => void;
  disabled?: number[];
  currentname?: string;
}

const AutomaticProblem: React.FC<IAutomaticProblem> = props => {
  const { t } = useLocale();
  const {
    selectKeys,
    type,
    visible,
    onAdd,
    onConfirm,
    onclose,
    disabled = [],
    currentname
  } = props;
  const [addform] = Form.useForm();
  const [getJsonData, setjsonData] = useState<any>([]);
  const [getSelectValue, setSelectValue] = useState<number>(-1);
  const [valueNumber, setOnNumberChange] = useState<number>(0);
  const [getList, setList] = useState<any>([]);
  const [getChecked, setChecked] = useState<any>([]);
  const [spinning, setSpinning] = useState<boolean>(false);
  useEffect(() => {
    addform.setFieldsValue({demand:currentname})
  }, [currentname,visible]);

  useEffect(() => {
    setSpinning(false)
    setList(getJsonData)
  }, [getJsonData]);

  const close = () => {
    setjsonData([])
    addform.resetFields();
    onclose();
  };
  const titleRender = () => {
    return t('自动出题');
  };
  const onFinish: FormProps['onFinish'] = (values) => {
    setSpinning(true)
    let param: any = {
      "question_type": values.type,
      "question_num": values.number,
      "question_requirements": values.demand
    }
    let arr: any = []
    fetchEventSource('/terminator/api/v1/skillSet/AiGEQ', {
      method: 'POST',
      signal,
      body: JSON.stringify(param),
      headers: {
        // 'Content-Type': 'text/event-stream',
        'Content-Type': 'application/json',
        'Accept': '*/*',
      },
      onmessage(e) {
        try {
          const { data } = e;
          if(data !="[DONE]"){
            let arr1 = JSON.parse(data)
            arr.push(arr1)           
          }
        } catch (e) {
          console.log(e);
        }
      },
      onclose() {
        setjsonData(arr);
      },
    });
  };

  const onFinishFailed: FormProps['onFinishFailed'] = (errorInfo) => {
    console.log('Failed:', errorInfo);
  };
  const onChange = (checkedValues: any) => {
    console.log('checked = ', checkedValues);
    setChecked(checkedValues)
  };
  const onChangeSelect = (value: any) => {
    console.log(value);
    setSelectValue(value)
  }
  const onOk = () => {
    let arr: any = []
    getChecked.map((i: any) => {
      arr.push(getJsonData[i])
    })
    console.log('这里是确认传参*************', arr);+
      onConfirm(arr, getSelectValue);

    close();
  }
  const onNumberChange = (value: any) => {
    // debugger
    console.log(value);
    
    setOnNumberChange(value)
  }
  return (
    <Modal
      title={titleRender()}
      open={visible}
      onCancel={close}
      width={944}
      zIndex={1002}
      destroyOnClose
      className="topic_modal"
      footer={[
        <Button
          type="primary"
          key="1"
          onClick={onOk}
          disabled={getChecked.length === 0}
        >
          {t('确定')}
        </Button>,
        <Button key="2" onClick={close}>
          {t('取消')}
        </Button>,
      ]}
    >
      <div className='AutomaticProblem_view'>
        <Form
          name="basic"
          // layout={'inline'}
          form={addform}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Row>
            <Col span={24}>
              <Form.Item
                label="题目要求"
                name="demand"
                rules={[{ required: true, message: '请填写题目要求' }]}
              >
                <Input defaultValue={currentname} disabled/>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item
                label="题目类型"
                name="type"
                rules={[{ required: true, message: '请选择题目类型' }]}
              >
                {/* placeholder={t("请选择题目类型")} */}
                <Select style={{ width: 130 }} onChange={onChangeSelect}>
                  <Option value={0}>单选题</Option>
                  <Option value={1}>多选题</Option>
                  <Option value={2}>填空题</Option>
                  <Option value={3}>主观题</Option>
                  <Option value={4}>判断题</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="题目数量"
                name="number"
                rules={[{ required: true, message: '请输入题目数量' }]}
              >
                <InputNumber min={1} max={10}  style={{width:'130px'}} value={valueNumber} onStep={onNumberChange}></InputNumber>
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
                <Button type="primary" htmlType="submit">
                  出题
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className='content'>

          <Spin spinning={spinning}>
            {
              getSelectValue !== -1 && (
                <Checkbox.Group style={{ width: '100%' }} onChange={onChange}>
                  {
                    getSelectValue == 0 ? (
                      <Row>
                        {
                          getList.length > 0 && getList.map((item: any, index: number) => {
                            return (
                              <Col span={24}>
                                <Checkbox value={index}>
                                  <div className='question'>
                                    <div className='title'>{item.content}</div>
                                    <div className='options'>
                                      {
                                        item.options?.map((i: any) => {
                                          return <div>{i}</div>
                                        })
                                      }
                                    </div>
                                    <div className='answer'>
                                      答案:{item.answer}
                                    </div>
                                    <div className='answer'>
                                      解析:{item.analysis}
                                    </div>
                                  </div>
                                </Checkbox>
                              </Col>
                            )
                          })
                        }
                      </Row>
                    ) : getSelectValue == 1 ? (
                      <Row>
                        {
                          getList.length > 0 && getList.map((item: any, index: number) => {
                            return (
                              <Col span={24}>
                                <Checkbox value={index}>
                                  <div className='question'>
                                    <div className='title'>{item.content}</div>
                                    <div className='options'>
                                      {
                                        item.options?.map((i: any) => {
                                          return <div>{i}</div>
                                        })
                                      }
                                    </div>
                                    <div className='answer'>
                                      答案:{item.answer}
                                    </div>
                                    <div className='answer'>
                                      解析:{item.analysis}
                                    </div>
                                  </div>
                                </Checkbox>
                              </Col>
                            )
                          })
                        }
                      </Row>
                    ) : getSelectValue == 2 ? (
                      <Row>
                        {
                          getList.length > 0 && getList.map((item: any, index: number) => {
                            return (
                              <Col span={24}>
                                <Checkbox value={index}>
                                  <div className='question'>
                                    <div className='title'>{item.content}</div>
                                    <div className='options'>
                                      {
                                        item.options
                                      }
                                    </div>
                                    <div className='answer'>
                                      答案:{item.answer}
                                    </div>
                                    <div className='answer'>
                                      解析:{item.analysis}
                                    </div>
                                  </div>
                                </Checkbox>
                              </Col>
                            )
                          })
                        }
                      </Row>) : getSelectValue == 3 ? (
                        <Row>
                          {
                            getList.length > 0 && getList.map((item: any, index: number) => {
                              return (
                                <Col span={24}>
                                  <Checkbox value={index}>
                                    <div className='question'>
                                      <div className='title'>{item.content}</div>
                                      <div className='options'>
                                        {
                                          item.options
                                        }
                                      </div>
                                      <div className='answer'>
                                        答案:{item.answer}
                                      </div>
                                      <div className='answer'>
                                        解析:{item.analysis}
                                      </div>
                                    </div>
                                  </Checkbox>
                                </Col>
                              )
                            })
                          }
                        </Row>) : (
                      <Row>
                        <Col span={24}>
                          {
                            getList.length > 0 && getList.map((item: any, index: number) => {
                              return (
                                <Checkbox value={index}>
                                  <div className='question'>
                                    <div className='title'>{item.content}</div>
                                    <div className='options'>
                                      {
                                        item.options
                                      }
                                    </div>
                                    <div className='answer'>
                                      答案:{item.answer ? '正确' : '错误'}
                                    </div>
                                    <div className='answer'>
                                      解析:{item.analysis}
                                    </div>
                                  </div>
                                </Checkbox>
                              )
                            })
                          }
                        </Col></Row>)
                  }
                </Checkbox.Group>
              )
            }
          </Spin>
        </div>
      </div>
    </Modal>
  );
};

export default AutomaticProblem;
