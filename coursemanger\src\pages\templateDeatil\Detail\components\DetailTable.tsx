import statisticsApi from '@/api/statistics';
import { ContainerOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Select, Space, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './DetailTable.less';

const DetailTable: React.FC<any> = ({ type, courseId, sm, code, mapId }) => {
  const columns: any['columns'] = [
    {
      title: '知识点',
      dataIndex: 'nodeName',
      key: 'nodeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '上级节点',
      dataIndex: 'parentNodeName',
      key: 'parentNodeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '关联课程目标',
      dataIndex: 'courseTargetBeans',
      className: 'table-header-cell',
      key: 'courseTargetBeans',
      align: 'center',
      ellipsis: true,
      render: (courseTargetBeans: any) => (
        <Space wrap>
          {courseTargetBeans?.map((member:{ name: string }) => member.name)}
        </Space>
      ),
    },
    {
      title: '完成率',
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      // sorter: (a: any, b: any) => {
      //   const numA = a.finishRate == '-' ? -1 : Number(a.finishRate);
      //   const numB = b.finishRate == '-' ? -1 : Number(b.finishRate);
      //   return numA - numB;
      // },
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      },
    },
    {
      title: '掌握率',
      dataIndex: 'masterRate',
      key: 'masterRate',
      align: 'center',
      // sorter: (a: any, b: any) => {
      //   const numA = a.masterRate == '-' ? -1 : Number(a.masterRate);
      //   const numB = b.masterRate == '-' ? -1 : Number(b.masterRate);
      //   return numA - numB;
      // },
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      },
    },
    // {
    //   title: t("操作"),
    //   key: 'action',
    //   align: 'center',
    //   render: (_: any, record: any) => <Button type="link" onClick={() => {
    //     sesdetail(record);
    //   }}>{t("查看详情")}</Button>
    // }
    //  , {
    //     title: "操作",
    //     key: 'action',
    //     align: 'center',
    //     render: (_: any, record: any) => <Button type="link" onClick={() => { setShowDetail({ courseId, ...record }); setShowPage(3); }}>{t("查看详情")}</Button>
    //   }
  ];

  const [pagination, setPagination] = useState<any>({
    current: 1,
    position: ['bottomCenter'],
    pageSize: 10,
    total: 0,
    // size: "small",
    showTotal: (total: number) => '共' + total + '条',
    showQuickJumper: true,
    showSizeChanger: true,
  });
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [keyword, setKeyword] = useState<string>('');
  // 表格loading
  const [loading, setLoading] = useState<boolean>(false);

  const sortField = useRef<0 | 1>() // 0 完成率，1掌握率
  const sortTYpe = useRef<0 | 1>() // 0升序，1降序

  const knowledgeStudy = (toPage1?: boolean) => {
    setLoading(true);
    if (toPage1) {
      setPagination({
        ...pagination,
        current: 1,
      });
    }
    statisticsApi
      .getKnowlegeStudy({
        courseId: courseId,
        courseSemester: sm,
        code: code,
        nodeName: keyword,
        page: toPage1 ? 1 : pagination.current,
        size: pagination.pageSize,
        sortField: sortField.current,
        sortType: sortTYpe.current,
      })
      .then((res: any) => {
        if (res.data.status == 200) {
          setDataSource(
            res.data.data.results.map((item: any, index: number) => {
              return {
                ...item,
                key: index,
              };
            }),
          );
          setPagination({
            ...pagination,
            current: res.data.data.page,
            size: res.data.data.size,
            total: res.data.data.total,
          });
        }
        setLoading(false);
      });
  };
  const tablechange = (pagination: any, filters: any, sorter: any, extra: any) => {
    if (extra?.action === 'sort') {
      sortField.current = sorter.field == 'finishRate'? 0 : 1
      let typeSort: 1 | 0 | undefined = undefined
      if (sorter.order == 'ascend') {
        typeSort = 0
      } else if (sorter.order == 'descend') {
        typeSort = 1
      }
      sortTYpe.current = typeSort

      knowledgeStudy(true);
      return
    }
    setPagination({
      ...pagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  useEffect(() => {
    knowledgeStudy();
  }, [keyword, pagination.current, pagination.pageSize]);
  const data: any = [];

  const exportresource = () => {
    window.open(
      `/learn/m1/statistics/knowledge/learning/export?courseId=${courseId}&courseSemester=${sm}&userCode=${code}&mapId=${mapId}`,
    );
  };
  const select = (value: string) => {
    console.log(`selected ${value}`);
  };
  const inputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    console.log(e);
  };
  const renderContent = () => {
    if (type === 'knowledge') {
      return (
        <div>
          <div className="table_head">
            <Space>
              <Input
                placeholder="请输入知识点名称"
                allowClear
                onChange={(e: any) => setKeyword(e.target.value)}
                onPressEnter={() => knowledgeStudy(true)}
              />
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => knowledgeStudy(true)}
              >
                搜索
              </Button>
            </Space>
            <Space>
              <Button
                type="primary"
                icon={<ContainerOutlined />}
                onClick={exportresource}
              >
                导出Excel
              </Button>
            </Space>
          </div>
          {/* <Table columns={columns} pagination={false} dataSource={data}  className='table_content'/> */}
          <Table
            size="small"
            dataSource={dataSource}
            loading={loading}
            columns={columns}
            rowKey={(record: any) => record.key}
            pagination={{ ...pagination, size: 'small' }}
            showSorterTooltip={false}
            onChange={tablechange}
            className="table_content"
          />
        </div>
      );
    } else if (type === 'homework') {
      return (
        <div>
          <div className="table_head">
            <Space>
              <Select
                defaultValue="全部"
                style={{ width: 120 }}
                onChange={select}
                options={[{ value: '全部', label: '全部' }]}
              />
              <Input
                placeholder="请输入学员姓名/学工号"
                allowClear
                onChange={inputChange}
              />
              <Button type="primary" icon={<SearchOutlined />}>
                搜索
              </Button>
            </Space>
            <Space>
              <Button type="primary" icon={<ContainerOutlined />}>
                导出Excel
              </Button>
            </Space>
          </div>
          <Table
            columns={columns}
            pagination={false}
            dataSource={data}
            rowKey={(record: any) => record.key}
            rowClassName={(record, index) => {
              return index % 2 == 0 ? 'odd' : 'aaa';
            }}
          />
        </div>
      );
    }
  };
  return <div>{renderContent()}</div>;
};
export default DetailTable;
