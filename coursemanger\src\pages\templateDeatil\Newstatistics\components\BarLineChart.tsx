import React, { FC, useState, useEffect } from 'react';
import ReactEcharts from 'echarts-for-react';
import './BarLineChart.less';
import useLocale from '@/hooks/useLocale';
import * as echarts from 'echarts';
const LineChart = (props: any) => {
  const { t } = useLocale();
  const { dataSource, height, colors = ['#FF9073', '#5E8AFF'], names = [t("学习完成率"), t("学习次数")] } = props;
  console.log(dataSource,'dataSource')
  const [option, setOption] = useState<any>();
  const validNodes = dataSource
  console.log(validNodes,'validNodes');
  function getHashParam(paramName:string) {
    const hashPart = window.location.hash.split('?')[1] || '';
    const hashParams = new URLSearchParams(hashPart);
    return hashParams.get(paramName);
  }
  useEffect(() => {
    const type = getHashParam('type')
    if (dataSource) {
      if (type === 'map') {
        setOption(getOptionPic());
      } else {
        setOption(getOptionTwo())
      }
    }
  }, [dataSource]);
  const getOptionPic = () => {
    // 生成颜色集合（可根据需要扩展）
    const colors = [
      '#1890ff', '#52c41a', '#722ed1', '#fa8c16',
      '#a0d911', '#13c2c2', '#eb2f96', '#fadb14'
    ];
    const nodeCount = validNodes.length;
    const xAxisData = validNodes.map(node => node.label);
    if (nodeCount <= 0) return;
    // 生成series：每个资源项对应一个系列
    const series = [];
    validNodes.forEach((node, nodeIndex) => {
      node.resources.forEach((resource, resourceIndex) => {
        // 创建数据数组，初始为全0
        const data = new Array(nodeCount).fill(0);
        // 设置当前节点的值为studyUserCount
        data[nodeIndex] = resource.studyUserCount;
        series.push({
          name: resource.resourceName,
          type: 'bar',
          stack: 'studyUserCount', // 所有系列使用相同的stack名称
          data: data,
          itemStyle: {
            color: colors[resourceIndex % colors.length]
          },
          // label: {
          //   show: true,
          //   position: 'inside',
          //   formatter: ({ value }) => value > 0 ? value : ''
          // },
          barWidth: 24
        });
      });
    });
    // console.log('dataSource', dataSource)
    return {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const node = validNodes[params[0].dataIndex];
          return `
          <strong>${node.nodeName}</strong><br>
          ${node.resources.map((r:any, i: number) => `
            <span style="color:${colors[i % colors.length]}">▋</span>
            ${r.resourceName}:${r.studyUserCount}<br>
          `).join('')}
        `;
        }
      },
      grid: {
        left: 25,
        right: 25,
        bottom: 40, // 增加底部空间以容纳滚动条
        containLabel: true
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 50,
          height: 20,
          bottom: 0
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: 50
        }
      ],
      xAxis: {
        type: 'category',
        splitLine: {show: false},
        data: validNodes.map((node: { label: string }) => node.label),
        axisLabel: {
          formatter: (value: string) => {
            return value.length > 5
              ? `${value.substring(0, 5)}...`
              : value;
          },
        },
      },
      yAxis: { type: 'value' },
      series: series
    };
  };
  const getOptionTwo = () => {
    // console.log('dataSource', dataSource)
    return {
      tooltip: {
        trigger: 'axis',
        formatter: function (param: any) {
          if (!param.length) return;
          let str = `<div>${param[0].axisValueLabel}</div>`;
          param.map((el: any, i: any) => {
            str += `<div style="display:flex;align-items:center;">${el.marker}<span style="flex:1;display:flex;justify-content: space-between;">${el.seriesName}<span style="margin-left:20px;">${el.data}${i == 0 ? '%' : ''}</span></span></div>`;
          });
          return str;
        }
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '0',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dataSource.map((el: any) => el.name),
        axisLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#333',
          overflow: 'truncate',
          interval: 0,
          width: 100
        }
      },
      yAxis: [{
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#333'
        },
        minInterval: 1
      }, {
        type: 'value',
        position: 'right',
        splitLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#333',
          formatter: '{value}%'
        },
        minInterval: 1
      }],
      series: [
        {
          name: names[0],
          type: 'line',
          data: dataSource.map((el: any) => el.value2),
          yAxisIndex: 1,
          itemStyle: {
            color(params: any) {
              return colors[1];
            }
          },
          barWidth: 24
        },
        {
          name: names[1],
          type: 'bar',
          data: dataSource.map((el: any) => el.value1),
          yAxisIndex: 0,
          itemStyle: {
            color: colors[0],
            barBorderRadius: [4, 4, 0, 0]
          },
          barWidth: 24
        }],

      dataZoom: dataSource?.length > 12 ? [// 有滚动条 平移
          {
            type: 'slider',
            realtime: true,
            start: 0,
            end: dataSource?.length > 12 ? (12 / dataSource.length * 100).toFixed(0) : 100, // 初始展示20%
            height: 4,
            fillerColor: "rgba(17, 100, 210, 0.42)", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%'

            // zoomLock:true, // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            end: 10,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }] :
        []
    };
  };
  const getOptionTwo = () => {
    // console.log('dataSource', dataSource)
    return {
      tooltip: {
        trigger: 'axis',
        formatter: function (param: any) {
          if (!param.length) return;
          let str = `<div>${param[0].axisValueLabel}</div>`;
          param.map((el: any, i: any) => {
            str += `<div style="display:flex;align-items:center;">${el.marker}<span style="flex:1;display:flex;justify-content: space-between;">${el.seriesName}<span style="margin-left:20px;">${el.data}${i == 0 ? '%' : ''}</span></span></div>`;
          });
          return str;
        }
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '0',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dataSource.map((el: any) => el.name),
        axisLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#333',
          overflow: 'truncate',
          interval: 0,
          width: 100
        }
      },
      yAxis: [{
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#333'
        },
        minInterval: 1
      }, {
        type: 'value',
        position: 'right',
        splitLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#333',
          formatter: '{value}%'
        },
        minInterval: 1
      }],
      series: [
        {
          name: names[0],
          type: 'line',
          data: dataSource.map((el: any) => el.value2),
          yAxisIndex: 1,
          itemStyle: {
            color(params: any) {
              return colors[1];
            }
          },
          barWidth: 24
        },
        {
          name: names[1],
          type: 'bar',
          data: dataSource.map((el: any) => el.value1),
          yAxisIndex: 0,
          itemStyle: {
            color: colors[0],
            barBorderRadius: [4, 4, 0, 0]
          },
          barWidth: 24
        }],

      dataZoom: dataSource?.length > 12 ? [// 有滚动条 平移
          {
            type: 'slider',
            realtime: true,
            start: 0,
            end: dataSource?.length > 12 ? (12 / dataSource.length * 100).toFixed(0) : 100, // 初始展示20%
            height: 4,
            fillerColor: "rgba(17, 100, 210, 0.42)", // 滚动条颜色
            borderColor: "rgba(17, 100, 210, 0.12)",
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%'

            // zoomLock:true, // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: "inside", // 支持内部鼠标滚动平移
            start: 0,
            end: 10,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }] :
        []
    };
  };
  return (
    <div className="bar_line_chart">
      {/* <span>课程笔记数目</span> */}
      <ReactEcharts style={{ height: height || '300px' }} option={option ? option : {}} className='chart' />
    </div>);

};

export default LineChart;
