import { IconFont } from '@/components/iconFont';
import {
  Button,
  Checkbox,
  Col,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Switch,
  Tooltip,
} from 'antd';
import React, { FC, useEffect, useState } from 'react';
// import Link from 'next/link';
// import Link from 'react-router-dom';
import useLocale from '@/hooks/useLocale';
import { CUSTOMER_NPU, CUSTOMER_YNU } from '@/permission/moduleCfg';
import { TeamOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import './videoItem.less';
import { updateAuthoritySingle, updateAuthorityBatch, updateMark, courseReviewDel, batchAddWatermark,
  getCopyrightInfo
 } from '@/api/course';
import { Link, useSelector } from 'umi';
import './videoItem.less';
// import config from '@/utils/config';
import UploadModal  from './UploadModal'
import { useGetTargetCustomer } from '@/hooks/useTargetCustomer'
const WEEK = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

const TeacherCollege: FC<{ d: any; showTeacherPre?: boolean }> = ({
  d,
  showTeacherPre,
}) => {
  const { t } = useLocale();
  return (
    <div className="teacher-college-wrp">
      <div className="teacher_info_wrapper">
        {d.teacherList && (
          <span
            className="teacher_name"
            title={d.teacherList?.map((item: any) => item.userName)?.join(',')}
          >
            {showTeacherPre && t('讲师：')}
            {d.teacherList?.map((item: any) => item.userName)?.join(',')}
          </span>
        )}
      </div>
      <div className="college_info_wrapper">{d.collegeNames}</div>
    </div>
  );
};

const TimeLocation: FC<{ d: any }> = ({ d }) => {
  const { t } = useLocale();
  const { semesterMap } = useSelector<any, any>(state => state.global);
  return (
    <div className="time-location-wrp">
      <p className="cd_addr_item">
        <IconFont type="iconcalendar" className="icon" />
        {/* 优先显示学期名 */}
        <span>
          {
            semesterMap[
            (d.schoolYear ? d.schoolYear + t('学年') : '') + d.semester
            ]
          }
        </span>
      </p>
      <p className="cd_addr_item">
        <IconFont type="iconshijian" className="icon" />
        {d.courseTimeData && d.courseTimeData.length > 0 ? (
          <span title={d.courseTimeData ? d.courseTimeData.join('；') : ''}>
            {d.courseTimeData ? d.courseTimeData.join('；') : ''}
          </span>
        ) : (
          <span>{t('暂无')}</span>
        )}
      </p>
      <p className="cd_addr_item">
        <IconFont type="icondizhi1" className="icon" />
        <span
          title={
            d.coursePositionList
              ? formatAddress(d.coursePositionList)
              : d.coursePositionList
          }
        >
          {d.coursePositionList
            ? formatAddress(d.coursePositionList)
            : d.coursePositionList}
        </span>
      </p>
    </div>
  );
};

const CourseNoAndSerialNumber: FC<{ d: any }> = ({ d }) => {
  const { serialNumber,courseNumber } = d
  return (
    <div className="course-no-wrap">
      <p className='cd_addr_item' title={courseNumber}>{courseNumber}</p>
      <p className='cd_addr_item' title={serialNumber}>{serialNumber}</p>
    </div>
  )
}

const StatusDisplay: FC<{ d: any; isNpu?: boolean }> = ({ d, isNpu }) => {
  const { t } = useLocale();
  const { parameterConfig } = useSelector<
    { global: any },
    { parameterConfig: any }
  >(state => state.global);
  return (
    <div className="opt_wrapper">
      <div className="collection">
        {isNpu ? (
          <img src={require('@/assets/imgs/icon/collection-icon.svg')} alt="" />
        ) : (
          <IconFont type="iconshoucang11" className="icon" />
        )}

        {d.collectionsTotal}
        {t('人收藏')}
      </div>
      <div>
        <IconFont type="iconbofang1" className="play_icon" />
        <span>{t('播放') + (d.playbackCount || 0)}</span>
      </div>
      <div>
        <TeamOutlined className="play_icon" />
        <span className="course_authority">
          {d.courseAuthority === 'public'
            ? t('公开')
            : d.courseAuthority === 'private'
            ? t('私有')
            : t(parameterConfig.target_customer == CUSTOMER_YNU? '班级可见': '部分可见')}
        </span>
      </div>
      <Tooltip title={t('课程直播')}>
        <div className="live-item-wrp">
          <IconFont type="iconzhibo" className="play_icon" />
          <span>
            {d.liveFlag ? t('已') : t('未')}
            {t('开启')}
          </span>
        </div>
      </Tooltip>
    </div>
  );
};

const Operation: FC<{
  d: any;
  canDelete: boolean;
  canSetting: boolean;
  locationHref: any;
  handleDelete: any;
}> = ({ d, locationHref, canDelete, handleDelete, canSetting }) => {
  const { t } = useLocale();
  return (
    <div className="operation">
      {canSetting && <Link
        to={`/course/classreview/visibleSetting?courseId=${d.courseId}&authorityType=${d.courseAuthority}&semester=${d.semester}&schoolYear`}
      >
        {t('设置')}
      </Link>}
      <a
        onClick={() => locationHref(d.courseId, d.id, d.schoolYear, d.semester)}
      >
        {t('查看')}
      </a>
      {canDelete && (
        <Popconfirm
          title={t('删除的课堂回看将无法复原，是否确认删除？')}
          onConfirm={() => handleDelete(d)}
        >
          <a>{t('删除')}</a>
        </Popconfirm>
      )}
    </div>
  );
};

const LiveIcon: FC = () => {
  return (
    <div className="live-icon-wrp">
      <div className="live-icon-item item1"></div>
      <div className="live-icon-item item2"></div>
      <div className="live-icon-item item3"></div>
    </div>
  );
};

export const formatAddress = (positions: any) =>
  positions
    .map(
      (p: any) =>
        `${p.xqShowName ?? p.campus}-${p.jxlShowName ??
        p.academicBuilding}${p.jsShowName ?? p.classroomNumber}`,
    )
    .join('；');

interface TimeInterface {
  section: string;
  week: string;
  weekDay: keyof typeof weekMap;
}
// * 时间转换(星期：英转中)
const weekMap = {
  Monday: '周一',
  Tuesday: '周二',
  Wednesday: '周三',
  Thursday: '周四',
  Friday: '周五',
  Saturday: '周六',
  Sunday: '周日',
};

export const formatTime = (times: any) =>
  times
    .map(
      (t: TimeInterface) =>
        // `${WEEK[t.week - 1]} ${
        //   t.section.length > 1
        //     ? t.section[0] + '~' + t.section[t.section.length - 1]
        //     : t.section[0]
        // }节`
        `${weekMap[t.weekDay]} ${t.section.split(',').length > 1
          ? t.section.split(',')[0] +
          '~' +
          t.section.split(',')[t.section.split(',').length - 1]
          : t.section.split(',')[0]
        }节`,
    )
    .join('；');

interface CreateModalProps {
  orignalData: any;
  allCourseId: any;
  refresh: () => void; // 刷新
}
const VideoItem: FC<CreateModalProps> = ({
  orignalData,
  allCourseId,
  refresh,
}) => {
  const [modalVisible, setModalVisible] = useState<any>(false);
  const [copyRightVisible, setCopyRightVisible] = useState<any>(false);
  const [indeterminate, setIndeterminate] = useState<any>(false);
  const [checkedList, setCheckedList] = useState<any>([]);
  const [dataList, setDataList] = useState<any>([]);
  const [updateParams, setUpdateParams] = useState<any>({});
  const [checkAll, setCheckAll] = useState(false);
  const [copyRight, setCopyRight] = useState<any>('');
  const [UploadModalVisible, setUploadModalVisible] = useState<boolean>(false)
  const [contentConfirmLoading, setContentConfirmLoading] = useState<boolean>(false);
  const [switchLoading, setSwitchLoading] = useState<boolean>(false);
  const [watermark, setWatermark] = useState<boolean>(false);
  const { userInfo, semesterMap } = useSelector<any, any>(
    state => state.global,
  );
  const jurisdictionList = useSelector<any, any>(
    ({ jurisdiction }) => jurisdiction.jurisdictionList,
  );
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );
  const { t } = useLocale();
  const target_custom = useGetTargetCustomer();

  useEffect(() => {
    setDataList(orignalData);
  }, [orignalData]);
  useEffect(() => {
    getCopyrightInfo().then(res => {
      if(res.status === 200){
        setCopyRight(res.data.copyright === 1 ? false : true)
        setWatermark(res.data.watermark === 1)
      }
    })
  }, [])
  const onContentModalOk = (data: any) => {
    setUploadModalVisible(false);
  }
  useEffect(() => {
    if (checkedList.length > 0) {
      console.log(checkedList.sort().toString(), allCourseId.sort().toString());
      if (checkedList.sort().toString() === allCourseId.sort().toString()) {
        setCheckAll(true);
        setIndeterminate(false);
      } else {
        setIndeterminate(true);
      }
    } else {
      setIndeterminate(false);
      setCheckAll(false);
    }
  }, [checkedList]);
  const changeVisiable = () => {
    if (checkedList.length === 1) {
      updateAuthoritySingle({
        courseId: checkedList[0],
        ...updateParams,
      }).then((res: any) => {
        if (res.message === 'OK') {
          message.success(t('修改成功'));
          refresh();
          setModalVisible(false);
        } else {
          message.error(t('修改失败'));
          setModalVisible(false);
        }
      });
    } else {
      updateAuthorityBatch(checkedList, updateParams).then((res: any) => {
        if (res.message === 'OK') {
          message.success(t('修改成功'));
          refresh();
          setModalVisible(false);
        } else {
          message.error(t('修改失败'));
          setModalVisible(false);
        }
      });
    }
  };
  const visibleSetting = () => {
    setModalVisible(true);
  };
  const locationHref = (
    courseId: any,
    id: any,
    schoolYear: string,
    semester: string,
  ) => {
    window.open(
      window.location.origin + `/learn/videoreview/${courseId}?id=${id}&edit=true`,
    );
    // window.open('http://localhost:4000/learn/videoreview/d9135df6b760425599b43f7c03516f27?edit=true')
  };
  const updateAuthority = (key: string,item: any) => {
    console.log(item.target?.value);
    setUpdateParams({
      ...updateParams,
      [key]: item.target?.value,
    });
  };
  const onCheckAllChange = (e: any) => {
    setCheckedList(e.target.checked ? allCourseId : []);
    setCheckAll(e.target.checked);
  };
  const changeCheckArray = (e: any, key: any) => {
    if (checkedList.length > 0) {
      if (checkedList.indexOf(key) > -1) {
        const arr_key = checkedList.filter(
          (item: any, index: number) => item !== key,
        );
        setCheckedList(arr_key);
      } else {
        setCheckedList([...checkedList, key]);
      }
    } else {
      setCheckedList([key]);
    }
  };

  const markChangeHandle = (value: boolean, courseId: string) => {
    setSwitchLoading(true);
    updateMark([{ courseId, watermark: value }]).then(res => {
      if (res.status === 200) {
        refresh();
        message.success(t('修改成功'));
        setSwitchLoading(false);
      } else {
        message.error(t('修改失败'));
      }
    });
  };

  const handleDelete = (row: any) => {
    setSwitchLoading(true);
    courseReviewDel([row.courseId])
      .then(res => {
        if (res.status === 200) {
          refresh();
          message.success('删除成功');
        } else {
          return Promise.reject();
        }
      })
      .catch(err => {
        message.error('删除失败');
      })
      .finally(() => {
        setSwitchLoading(false);
      });
  };
  const { parameterConfig } = useSelector<
    { global: any },
    { parameterConfig: any }
  >(state => state.global);
  return (
    <div className='video_item'>
      {jurisdictionList.includes("course_review_setting") && <div style={{display: 'flex', alignItems: 'center', justifyContent: "space-between"}}>
        <div>
          <Checkbox onChange={onCheckAllChange} indeterminate={indeterminate} checked={checkAll}>{t("全选")}</Checkbox>
          <Button
            shape="round"
            type="primary"
            ghost
            onClick={visibleSetting}
            disabled={checkedList.length > 0 ? false : true}>
            {t("谁可以看")}

          </Button>
        </div>
        <div className="setting_wrap">
          <span>
            {t("视频水印")}
            <Tooltip title={t("开启则学生在观看视频时，视频上会显示学生的姓名、学号水印，以防录屏")}>
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
          <Switch checked={watermark} onChange={(checked: boolean) => {
            batchAddWatermark({ watermark: Number(checked) }).then(res =>{
              if (res && res.status == 200) {
                message.success(t("设置成功！"));
              }
            })
            setWatermark(checked)
          }}></Switch>
          <span>{t("版权说明")}</span>
          <Switch checked={copyRight} onChange={(checked: boolean) => {
            if(!checked){
              batchAddWatermark({copyright: 1}).then(res =>{
                if (res && res.status == 200) {
                  message.success(t("设置成功！"));
                }
              })
            }
              setCopyRight(checked)
          }}></Switch>
          {copyRight && <span onClick={()=>setUploadModalVisible(true)} className='setting'>设置</span>}
        </div>
      </div>}
      {
        dataList.map((d: any) => {
          return (
            <div className="cd_video_item_top" key={d.courseId}>
              {jurisdictionList.includes("course_review_setting") && <Checkbox
                value={d.courseId}
                onClick={e => changeCheckArray(e, d.courseId)}
                checked={
                  checkedList.length > 0 && checkedList.indexOf(d.courseId) > -1
                    ? true
                    : false
                }
              ></Checkbox>}
              <div
                className={`cd_video_item_wrapper ${parameterConfig.target_customer ===
                  CUSTOMER_NPU &&
                  !mobileFlag &&
                  'npu'}`}
                target={d.openNewWin ? '_blank' : '_self'}
              >
                {!mobileFlag &&
                  parameterConfig.target_customer === CUSTOMER_NPU ? (
                  <div className="npu-box">
                    <div className="npu-header-wrp">
                      <h2
                        className="cd_course_title"
                        onClick={() =>
                          locationHref(
                            d.courseId,
                            d.id,
                            d.schoolYear,
                            d.semester,
                          )
                        }
                      >
                        <img
                          src={require('@/assets/imgs/icon/small-video.svg')}
                          alt=""
                        />
                        <span className="cd_course_name">{d.courseName}</span>
                        {d.live ? (
                          <span className="cd_live_tag">
                            <LiveIcon />
                            {t('直播中')}
                          </span>
                        ) : (
                          ''
                        )}
                      </h2>
                      <StatusDisplay
                        d={d}
                        isNpu={parameterConfig.target_customer === CUSTOMER_NPU}
                      />
                    </div>
                    <div className="npu-content-wrp">
                      <div className="left-wrp">
                        <TeacherCollege d={d} showTeacherPre={true} />
                        <TimeLocation d={d} />
                      </div>
                      <div className="right-wrp">
                        <Operation
                          d={d}
                          canDelete={
                            jurisdictionList.includes("course_review_delete")
                          }
                          canSetting={jurisdictionList.includes("course_review_setting")}
                          handleDelete={handleDelete}
                          locationHref={locationHref}
                        />

                        <img
                          src={require('@/assets/imgs/icon/big-video.svg')}
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                ) : !mobileFlag ? (
                  <Row align="middle" gutter={32}>
                    <Col span={4}>
                      <h2
                        className="cd_course_title"
                        onClick={() =>
                          locationHref(
                            d.courseId,
                            d.id,
                            d.schoolYear,
                            d.semester,
                          )
                        }
                      >
                        <span className="cd_course_name">{d.courseName}</span>
                        {d.live ? (
                          <span className="cd_live_tag">
                            <LiveIcon />
                            {t('直播中')}
                          </span>
                        ) : (
                          ''
                        )}
                      </h2>
                      <TeacherCollege d={d} />
                    </Col>
                    <Col span={4}>
                      <TimeLocation d={d} />
                    </Col>
                    {
                      parameterConfig.target_customer == 'shangHaiTech' &&
                      <Col span={4}>
                       <CourseNoAndSerialNumber d={d} />
                    </Col>
                    }
                    <Col span={parameterConfig.target_customer == 'shangHaiTech' ? 6 : 8} offset={ parameterConfig.target_customer == 'shangHaiTech' ? 0 : 2}>
                      <StatusDisplay d={d} />
                    </Col>
                    <Col span={6}>
                      <Operation
                        d={d}
                        canDelete={jurisdictionList.includes("course_review_delete")}
                        canSetting={jurisdictionList.includes("course_review_setting")}
                        handleDelete={handleDelete}
                        locationHref={locationHref}
                      />
                    </Col>
                  </Row>
                ) : (
                  <>
                    <div className="content">
                      <h2
                        className="cd_course_title"
                        onClick={() =>
                          locationHref(
                            d.courseId,
                            d.id,
                            d.schoolYear,
                            d.semester,
                          )
                        }
                      >
                        <span className="cd_course_name">{d.courseName}</span>
                        {d.live ? (
                          <span className="cd_live_tag">
                            <LiveIcon />
                            {t('直播中')}
                          </span>
                        ) : (
                          ''
                        )}
                      </h2>
                      <div className="teacher_info_wrapper">
                        {d.teacherList && (
                          <>
                            <span
                              className="teacher_name"
                              title={d.teacherList
                                ?.map((item: any) => item.userName)
                                ?.join(',')}
                            >
                              {d.teacherList
                                ?.map((item: any) => item.userName)
                                ?.join(',')}
                            </span>
                          </>
                        )}
                      </div>
                      <div className="college_info_wrapper">
                        {d.collegeNames}
                      </div>
                      <div className="live-item-wrp">
                        <IconFont type="iconzhibo" className="play_icon" />
                        <span>
                          {d.liveFlag ? t('已') : t('未')}
                          {t('开启')}
                        </span>
                      </div>
                    </div>
                    <div className="bottom-box">
                      <div className="opt_wrapper">
                        <div className="collection">
                          <IconFont type="iconshoucang11" className="icon" />{' '}
                          {d.collectionsTotal}
                          {t('人收藏')}
                        </div>
                        <div>
                          <IconFont type="iconbofang1" className="play_icon" />
                          <span>{t('播放') + (d.playbackCount || 0)}</span>
                        </div>
                        <div>
                          <TeamOutlined className="play_icon" />
                          <span className="course_authority">
                            {d.courseAuthority === 'public'
                              ? t('公开')
                              : d.courseAuthority === 'private'
                              ? t('私有')
                              : t(parameterConfig.target_customer == CUSTOMER_YNU? '班级可见': '部分可见')}
                          </span>
                        </div>
                      </div>
                      <div className="right">
                        <p className="cd_addr_item">
                          <IconFont type="iconcalendar" className="icon" />
                          <span>
                            {
                              semesterMap[
                              (d.schoolYear ? d.schoolYear + t('学年') : '') +
                              d.semester
                              ]
                            }
                          </span>
                        </p>
                        <p className="cd_addr_item">
                          <IconFont type="iconshijian" className="icon" />
                          {d.courseTimeData && d.courseTimeData.length > 0 ? (
                            <span
                              title={
                                d.courseTimeData
                                  ? d.courseTimeData.join('；')
                                  : ''
                              }
                            >
                              {d.courseTimeData
                                ? d.courseTimeData.join('；')
                                : ''}
                            </span>
                          ) : (
                            <span>{t('暂无')}</span>
                          )}
                        </p>
                        <p className="cd_addr_item">
                          <IconFont type="icondizhi1" className="icon" />
                          <span
                            title={
                              d.coursePositionList
                                ? formatAddress(d.coursePositionList)
                                : d.coursePositionList
                            }
                          >
                            {d.coursePositionList
                              ? formatAddress(d.coursePositionList)
                              : d.coursePositionList}
                          </span>
                        </p>
                      </div>
                    </div>
                    <div className="operation">
                      <a
                        onClick={() =>
                          locationHref(
                            d.courseId,
                            d.id,
                            d.schoolYear,
                            d.semester,
                          )
                        }
                      >
                        <IconFont type="iconliulanliang" />
                        {t('查看')}
                      </a>
                      {jurisdictionList.includes("course_review_setting") && <Link
                        onClick={event => {
                          if (mobileFlag) {
                            message.info('暂不支持手机端，请前往电脑端操作');
                            event?.preventDefault();
                          }
                        }}
                        to={`/course/classreview/visibleSetting?courseId=${d.courseId}&authorityType=${d.courseAuthority}&semester=${d.semester}&schoolYear`}
                      >
                        <IconFont type="iconshezhi2" />
                        {t('设置')}
                      </Link>}
                      {jurisdictionList.includes("course_review_delete") && (
                        <Popconfirm
                          title={t('删除的课堂回看将无法复原，是否确认删除？')}
                          onConfirm={() => handleDelete(d)}
                        >
                          <IconFont type="iconshanchu-heise-copy" />
                          <a>{t('删除')}</a>
                        </Popconfirm>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          );
        })}
    {UploadModalVisible && <UploadModal
          visible={UploadModalVisible}
          copyRight={copyRight}
          contentConfirmLoading={contentConfirmLoading}
          onlyVideo={false}
          formData={{}}
          // folderPath={folderPath}
          onClose={() => {
            setUploadModalVisible(false)
          }}
          resourceModalConfirm={(data: any) => onContentModalOk(data)}
          />}

      <Modal
        title={t('谁可以看')}
        visible={modalVisible}
        width={600}
        onCancel={() => {
          setModalVisible(false);
        }}
        onOk={changeVisiable}
      >
        <div>
          {/* 云大隐藏私有选项 */}
          <div style={{display:'flex',alignItems:'center'}}>
            课程直播：
            <Radio.Group onChange={(val) => updateAuthority('liveAuthority',val)}>
              <Radio value="public">{t('公开（全校师生可见）')}</Radio>
              <Tooltip title={"课程直播暂不支持设置为仅自己可见"}>
                  <Radio value="private" disabled>{t("私有（仅自己可见）")}</Radio>
                </Tooltip>
              <Radio value="custom">{t(parameterConfig.target_customer == CUSTOMER_YNU? "班级可见": "部分可见")}</Radio>
            </Radio.Group>
          </div>
          <div style={{display:'flex',alignItems:'center',marginTop: 20}}>
            课堂回看：
            <Radio.Group onChange={(val) => updateAuthority('authority',val)}>
              <Radio value="public">{t('公开（全校师生可见）')}</Radio>
              <Radio value="private">{t("私有（仅自己可见）")}</Radio>
              <Radio value="custom">{t(parameterConfig.target_customer == CUSTOMER_YNU? "班级可见": "部分可见")}</Radio>
            </Radio.Group>
          </div>
        </div>
      </Modal>
      </div>
    );
};
export default VideoItem;
