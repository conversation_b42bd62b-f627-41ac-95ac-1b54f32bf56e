import React, { <PERSON> } from "react";
import { useSelector } from "umi";
import perCfg from '@/permission/config';
import { Button, Checkbox, Dropdown, Menu, Radio, message } from "antd";
import { IconFont } from "@/components/iconFont";
import { EllipsisOutlined, EyeFilled } from "@ant-design/icons";
import "./index.less";
import useLocale from "@/hooks/useLocale";

interface TemplateCardProps {
  tpl: any; // 数据
  myOrShare: boolean; // 是否共享
  showBtn: boolean; // 是否显示操作按钮
  isMultiple?: boolean; // 是否多选
  onPublish?: (data: any) => void; // 发布
  onUnPublish?: (data: any) => void; // 下架
  onCopy?: (data: any) => void; // 复制
  onDelete?: (data: any) => void; // 删除
  onReference?: (data: any) => void; // 引用
  isPublish?: number; // 悬浮显示hover文本 切换
  onRemove?: (data: any) => void; //撤销
}

const TemplateCard: FC<TemplateCardProps> = ({onRemove, tpl, myOrShare, showBtn, isMultiple = true, onPublish, onUnPublish, onCopy, onDelete, onReference, isPublish }) => {
  const jurisdictionList = useSelector<{ jurisdiction: any; }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.jurisdictionList;
    });

  const { mobileFlag } = useSelector<{ config: any; }, any>(
    ({ config }) => config);
  const { t } = useLocale();

  const getMenu = (item: any) => {
    return (
      <Menu>
        {jurisdictionList.includes(perCfg.my_tpl_publish) && (
          item.release_type != "public" ?
            <Menu.Item
              onClick={(e: any) => {
                onPublish?.(item);
                e.domEvent.stopPropagation();
              }}>

              <IconFont type='iconrelease' />{t("共享")}

            </Menu.Item> :

            <Menu.Item
              onClick={(e: any) => {
                onUnPublish?.(item);
                e.domEvent.stopPropagation();
              }}>

              <IconFont type='iconoffShelf' />{t("取消共享")}

            </Menu.Item>)}

        {jurisdictionList.includes(perCfg.my_tpl_mg) &&
          <>
            <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                if (mobileFlag) {
                  message.info(t('暂不支持手机端，请前往电脑端操作'));
                } else {
                  window.open(
                    `/learn/workbench/#/tempatedetail/courseInfo?id=${item.contentId_}&type=edit&myOrShare=${myOrShare}`);

                }
              }}>

              <IconFont type='iconedit' />{t("编辑")}

            </Menu.Item>
            <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                if (mobileFlag) {
                  message.info(t('暂不支持手机端，请前往电脑端操作'));
                } else {
                  window.open(
                    `/learn/workbench/#/tempatedetail/courseInfo?id=${item.contentId_}&type=see&myOrShare=${myOrShare}`);

                }
              }}>

              <IconFont type='iconviews' />{t("预览")}

            </Menu.Item>
            <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                onCopy?.(item);
              }}>

              <IconFont type='iconcopy' />{t("复制")}

            </Menu.Item>

            {item.release_type != "public" && <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                onDelete?.(item);
              }}>

              <IconFont type='icondelete' />{t("删除")}

            </Menu.Item>}
          </>}

      </Menu>);

  };
  const handleStopPropagation = (e: any) => {
    if (e.stopPropagation) {}
    // e.domEvent.stopPropagation();
    e.stopPropagation();
  }
  const getMenus = () => {
    const btnList = [
      {
        label: t("预览"),
        key: 1,
        func: (e: any) => {
          handleStopPropagation(e);
          if (isPublish == 1) {
            if (tpl.courseType == 1) {
              window.open(`#/editcourse/moocbaseinfo?id=${tpl.contentId_}&type=mooc`);
            }
            if (tpl.courseType == 2) {
              window.open(`#/editcourse/baseInfo?id=${tpl.contentId_}&type=spoc`);
            }
          } else {
            window.open(
              `/learn/workbench/#/tempatedetail/courseInfo?id=${tpl.contentId_}&type=see&myOrShare=${myOrShare}`);
          }

        },
        icon:  <IconFont type='iconviews' />,
        show: jurisdictionList.includes(perCfg.my_tpl_mg)
      },
      {
        label: t("编辑"),
        key: 2,
        func: (e: any) => {
          handleStopPropagation(e);
          window.open(
            `/learn/workbench/#/tempatedetail/courseInfo?id=${tpl.contentId_}&type=edit&myOrShare=${myOrShare}`);

        },
        icon:  <IconFont type='iconedit' />,
        show: jurisdictionList.includes(perCfg.my_tpl_mg) && myOrShare && showBtn
      },
      {
        label: t("共享"),
        key: 3,
        func: (e: any) => {
          handleStopPropagation(e);
          onPublish?.(tpl);
        },
        icon:  <IconFont type='iconrelease' />,
        show: !tpl.process_instance && jurisdictionList.includes(perCfg.my_tpl_publish) && tpl.release_type !=="public" && myOrShare && showBtn,
      },
      {
        label: t("撤回"),
        key: 3,
        func: (e: any) => {
          handleStopPropagation(e);
          onRemove?.(tpl);
        },
        icon:  <IconFont type='iconrelease' />,
        show:  tpl.process_instance && tpl.approvalStatus === 0 && myOrShare && showBtn,
      },
      {
        label: t("取消共享"),
        key: 4,
        func: (e: any) => {
          handleStopPropagation(e);
          onUnPublish?.(tpl);
        },
        icon:  <IconFont type='iconoffShelf' />,
        show: jurisdictionList.includes(perCfg.my_tpl_publish) && tpl.release_type === "public" && myOrShare && showBtn,
      },
      {
        label: t("复制"),
        key: 5,
        func: (e: any) => {
          handleStopPropagation(e);
          onCopy?.(tpl);
        },
        icon:  <IconFont type='iconcopy' />,
        show: jurisdictionList.includes(perCfg.my_tpl_publish) && myOrShare && showBtn,
      },
      {
        label: t("删除"),
        key: 6,
        func: (e: any) => {
          handleStopPropagation(e);
          onDelete?.(tpl);
        },
        icon:  <IconFont type='icondelete' />,
        show: jurisdictionList.includes(perCfg.my_tpl_publish) && tpl.release_type !== "public" && myOrShare && showBtn,
      },
      {
        label: t("引用"),
        key: 7,
        func: (e: any) => {
          onReference?.(tpl);
          e.stopPropagation();
        },
        icon:  <IconFont type='iconcopy' />,
        show: jurisdictionList.includes(perCfg.public_tpl_refer) && !myOrShare && showBtn,
      },
    ].filter(item => item.show)
    const length = btnList.length;
    const showLength = length > 5 ? 4 : 5;
    return (<div className='bottom_btn' onClick={(e: any) => {e.stopPropagation();}}>
    {btnList.slice(0, showLength).map(item =>
      <div onClick={item.func} key={item.label}>
      {item.icon}
      <span className='btn-name'>{t(item.label)}</span>
    </div>)}
    {length > 5 && <Dropdown
      menu={{
        items: btnList.slice(showLength, length),
        onClick: ({key, domEvent}: any) => {
          btnList.find(item => item.key === Number(key))?.func?.(domEvent)
        },
      }}
      overlayClassName="overlaystyal">
     <div>
        <EllipsisOutlined />
        <span className='btn-name'>更多</span>
     </div>
    </Dropdown>}

  </div>);
  }
  return (
    <div
      className="thumbnail_item"
      key={tpl.contentId_}
      onClick={() => {
        if (!showBtn) return;
        if (
          !myOrShare &&
          jurisdictionList.includes(perCfg.public_tpl_browse)) {
          if (mobileFlag) {
            message.info(t('暂不支持手机端，请前往电脑端操作'));
          } else {
            window.open(
              `#/tempatedetail/courseInfo?id=${tpl.contentId_}&type=see&myOrShare=${myOrShare}`);
          }
        }
      }}>
      <div className="img_box" onClick={() => {
        if (myOrShare) {
          if (mobileFlag) {
            message.info(t('暂不支持手机端，请前往电脑端操作'));
          } else {
            window.open(`#/tempatedetail/courseInfo?id=${tpl.contentId_}&type=edit&myOrShare=${myOrShare}&isSh=true`);
          }
        }
      }}>
        {isMultiple ? <Checkbox value={tpl.contentId_} onClick={(e) => e.stopPropagation()} /> :
          <Radio value={tpl.contentId_} onClick={(e) => e.stopPropagation()} />}
        <div className='icon-wrp'>
          <div className="icon">
            <IconFont type="iconlink" />
            <span>{tpl.quoteCount}</span>
          </div>
        </div>
        <img src={tpl.cover}></img>
        {!mobileFlag && getMenus()}
        {/* {!showBtn && <div className="cover-modal" onClick={() => {
          if (!mobileFlag) {
            if (isPublish == 1) {
              if (tpl.courseType == 1) {
                window.open(`#/editcourse/moocbaseinfo?id=${tpl.contentId_}&type=mooc`);
              }
              if (tpl.courseType == 2) {
                window.open(`#/editcourse/baseInfo?id=${tpl.contentId_}&type=spoc`);
              }
            } else {
              window.open(`#/tempatedetail/courseInfo?id=${tpl.contentId_}&type=${tpl.release_type === "public" ? 'see' : 'edit'}&myOrShare=${tpl.release_type === "private"}`);
            }
          }
        }}><EyeFilled />{isPublish == 1 ? t("预览课程") : t("预览课程资源包")}</div>} */}
        {myOrShare &&
          tpl.release_type === 'public' &&
          <div className="label_item2">{t("已共享")}
          </div>
        }{
        tpl.process_instance && tpl.approvalStatus === 0 && myOrShare && <div className="label_item2">
          {t("待审核")}
        </div>
      }
        {tpl.release_type === "public" && !showBtn && <div className="share-btn">{t("共享")}</div>}

      </div>
      <div className="detail_box">
        <div className="title_box">
          <span className="title" title={tpl.name_}>{tpl.name_}</span>
        </div>
        <div className="teacher">
          {tpl.editorial_team && tpl.editorial_team?.map((item: any, index: number) => {
            return <span key={index}>{item.value?.length ? item.value.split(',')[0] : ''}</span>;
          })}
          {!tpl.editorial_team && tpl.teacher_names?.length > 0 && <span>{tpl.teacher_names[0]}</span>}
        </div>
        <div
          className="collage"
          style={myOrShare ? { width: '80%' } : { width: '64%' }}>

          <span>
            {tpl.collegeName?.join("，")}
          </span>
          {/* <span
            title={tpl.majorName?.join("，")}>
            {t("专业：")}

            {tpl.majorName?.join("，")}
          </span> */}
        </div>
        {mobileFlag && (myOrShare && showBtn ?
          <div className="action_icon">
            <Dropdown
              // getPopupContainer={(e:any)=>e.parentElement}
              overlay={getMenu(tpl)}
              overlayClassName="overlaystyal">
              <EllipsisOutlined />
            </Dropdown>
          </div> :
          showBtn &&
          <div className={`action_icon${mobileFlag ? ' mobile_' : ''}`}>
            {jurisdictionList.includes(perCfg.public_tpl_refer) && (
              mobileFlag ?
                <Button
                  icon={<IconFont type="iconyinyong" />}
                  type="text"
                  onClick={(e: any) => {
                    onReference?.(tpl);
                    e.stopPropagation();
                  }}>

                </Button> :

                <Button
                  onClick={(e: any) => {
                    onReference?.(tpl);
                    e.stopPropagation();
                  }}>
                  {t("引用")}
                </Button>)}

          </div>)}

      </div>
    </div>);

};

export default TemplateCard;
