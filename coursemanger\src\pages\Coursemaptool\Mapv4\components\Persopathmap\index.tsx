import baseInfo from '@/api/baseInfo';
import {
  getKnowledgeStudyRate,
  querymapbyid,
  recordCurrentNode,
  getKnowledgeStudyRecord
} from '@/api/coursemap';
import statisticsApi from '@/api/statistics';
import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import Teachingmodule from '@/pages/Coursemaptool/components/Teachingmodule';
import { Drawer, Input, Select, message } from 'antd';
import debounce from 'lodash/debounce';
import React, { useEffect, useRef, useState } from 'react';
import { useListener } from 'react-bus';
import { useDispatch, useLocation } from 'umi';
import { defaultNode, defaultNodeData, tranListToTreeData,getKnowledgeBymouldes,checkshowmessage,computingKnowledge} from '../../../Editmap/util';
import Rdrawer from '../../../Rdrawer';
import Search from '../../../Search';
import WholeRdrawer from '../../../WholeDrawer';
import ProgressBox from '../ProgressBox';
import GridMap from '../gridMap';
import './index.less';
const { Option } = Select;
import { Markup } from '@antv/x6'
import { Utils } from '@/utils/utils';
import { CheckService } from '@/api/check';
import { getLabels } from '@/api/labelConfig';

const Persopathmap: React.FC<any> = ({ mapid, courseid, showrete = true, isMain = false /** 是否是教师端 */ ,coursename='',couresSyllabusCode=''}) => {
  const { t } = useLocale();
  const location: any = useLocation();
  // 获取url参数
  const { query }: any = useLocation();
  const dispatch = useDispatch();
  const searchref = useRef<any>();
  const [mapdata, setMapData] = useState<any>(null);
  const [mapinfo, setMapinfo] = useState<any>(null);
  // 图谱
  const [graph, setGraph] = useState<any>(null);
  // 异常情况展示
  const [errorinfo, setErrorinfo] = useState<any>(0); //0是正常  1参数错误  2当前专业暂无课程数据 3暂无专业图谱数据  4当前课程地图已下架  5当前课程地图已被删除
  // 节点的完成率 和 掌握率
  const [noderate, setNoderate] = useState<any>(null);
  // 当前所有节点的字典表
  const [allnodes, setAllnodes] = useState<any>(null);
  // 当前是按照完成率 还是 掌握率 显示
  const [showtype, setShowtype] = useState<any>(1); //1是完成率  2是掌握率
  const [selecttype, setSelecttype] = useState<string>('0'); //选择的筛选节点类型
  const [inputtext, setInputtext] = useState<string>(''); //搜索框内容
  // 0是关闭状态  1显示知识节点弹窗  2显示对比辨析弹窗  3关联节点   4知识节点对比辨析  5绑定管理  6搜素   7 excel word 导入  8 对比辨析详情页面  9课程大钢  10课程地图按课件生成  11 地图保存记录 12 知识点达成度 13选择添加的课程  14课程详情
  // 15是导入xmind  16是引用地图  17是添加地图关系  18是展示跨课关系  19是导入大纲
  const [visible, setVisible] = useState<number>(0);
  // 查询类型
  const [querytype, setQuerytype] = useState<string>('0');
  // 详情的参数
  const [drawerdata, setDrawerdata] = useState<any>(null);
  // 所有知识点
  const [knownodeIdList, setKnowNodeIdList] = useState<any>([]);
  const [wholeDrawer, setWholeDrawer] = useState<boolean>(false);
  const [previewEntity, setPreviewEntity] = useState<any>({});
  const [totalTime, setTotalTime] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  // 教学模块的ref
      // 绑定大纲后的大纲id
  const [bindCourseCode, setBindCourseCode] = useState('');
  const TeachingmoduleRef = useRef<any>(null);
  // 模块下所有知识点信息 包含  所有后修连线
  const moudelNodedata = useRef<any>({
    allhouxiuedegs:[],
    modulenodeList:{},
    notNeedToStudy:[]
  });
  // 当前节点是否要记录学习进度
  const [notrecord, setNotrecord] = useState<boolean>(false);
  // 当前要置灰的点
  const [grayedoutarr, setGrayedoutarr] = useState<any>([]);
  // 添加当前学习节点状态
  const [currentLearnNode, setCurrentLearnNode] = useState<string>('');
  // 绑定的大纲id
  const [bindCourseId, setBindCourseId] = useState('');
  // 默认筛选项
  const [filteroptions, setFilteroptions] = React.useState<any[]>([
    {
      value: '0',
      label: t("全部节点")
    },
    {
      value: '2',
      label: t("有课程资源")
    },
    {
      value: '9',
      label: t("有试题")
    },
    {
      value: '10',
      label: t("有讲解")
    },
    {
      value: '1',
      label: t("重难点")
    },
    {
      value: '5',
      label: t("核心知识点")
    },
    {
      value: '6',
      label: t("拓展知识点")
    },
    {
      value: '7',
      label: t("案例")
    },{
      value: '8',
      label: t("实验")
    },
    {
      value: '3',
      label: t("对比辨析")
    },
    {
      value: '4',
      label: t("跨课")
    },
    {
      value: '11',
      label: t("无讲解")
    },{
      value: '12',
      label: t("无课程资源")
    }
  ]);


  useListener('nodelearning', debounce(({ id, finishRate, masterRate }) => {
    if (showrete) {
      refresh();
    }
  }, 1000))

  /**
   * 获取所有标签
   */
  const getalllabels = () => {
    getLabels({
      type: 3,
      page: 1,
      size:1000
    }).then((res:any) => {
      if(res.results?.length>0){
        setFilteroptions([...filteroptions, ...res.results?.map((item:any)=>{
          return {
            value: "labels_"+item.id,
            label: item.name,
          }
        })]);
      }
    })
  };

  useEffect(() => {
    getalllabels();
  }, [])

  async function getInfo() {
    const [err, res] = await Utils.tryitRequestDefaultFn(
      CheckService.infoInstalments,
    )({
      mapId: mapid,
    });
    if (err) {
      // message.error(err.message ?? '获取信息失败');
    } else {
      setBindCourseId(res?.data?.data?.courseCode || '');
    }
  }

  useEffect(() => {
    // 图谱的编辑页面和课程里的图谱编辑
    if (!mapid) return
    getInfo()
  }, [mapid])

  // 刷新页面统计
  const refresh = () => {
    getTeacherOrStudentRate(isMain ? 0 : 1)
    // getKnowledgeStudyRate({
    //   courseId: query.id,
    //   mapId: mapid,
    //   nodeIdList: [],
    //   semester: query.sm,
    // }).then((res: any) => {
    //   if (res.status == 200) {
    //     setNoderate({
    //       achievingRateTotal:res.data.achievingRateTotal,
    //       finishRateTotal: res.data.finishRateTotal,
    //       masterRateTotal: res.data.masterRateTotal,
    //     });
    //   }
    // });
  }

  //查询课程地图的节点完成率和掌握率
  // const getknowlerate = (nodeIdList: any[]) => {
  //   getKnowledgeStudyRate({
  //     courseId: query.id,
  //     mapId: mapid,
  //     nodeIdList: nodeIdList,
  //     semester: query.sm,
  //   }).then((res: any) => {
  //     if (res.status == 200) {
  //       // if (!isMain) {
  //       //   setNoderate({
  //       //     achievingRateTotal:res.data.achievingRateTotal,
  //       //     finishRateTotal: res.data.finishRateTotal,
  //       //     masterRateTotal: res.data.masterRateTotal,
  //       //   });
  //       // }
  //       let obj: any = {};
  //       res.data.detailDTOList.forEach((item: any) => {
  //         obj[item.nodeId] = {
  //           finishRate: item.finishRate,
  //           masterRate: item.masterRate,
  //         };
  //       });
  //       // 计算出来要置灰的点
  //       const grayedoutarr = computingKnowledge(moudelNodedata.current,res.data.detailDTOList);
  //       setGrayedoutarr(grayedoutarr);
  //       setAllnodes(obj);
  //     }
  //   });
  // };

    // 获取知识点掌握率
    const getTeacherOrStudentRate = (type: number) => {
      statisticsApi.getTeacherOrStudentAllRate({
        courseId: courseid,
        mapId: mapid,
        /** 0 教师端, 1 学生端 */
        type: type
      }).then((res: any) => {
        if (res.data.status == 200) {
          setNoderate({
            achievingRateTotal: res.data.data.nodeAttainment || 0,
            finishRateTotal: res.data.data.totalFinishRate || 0,
            masterRateTotal: res.data.data.totalMasterRate || 0,
          });
        }
      });
    };

  //查询课程关联的地图
  const initcoursemap = () => {
    // querymapbycourse({
    //   courseId: query.id,
    //   isShow: 2,
    //   courseSemester: query.sm,
    // }).then(({ data }: any) => {
    //   if (data.length == 0) {
    //     message.info('当前课程暂未关联图谱！');
    //     return;
    //   }
    //   setMapinfo(data[0]);
    //   // 查询课程里面的第一个地图

    // });
    querymapbyid({
      mapId: mapid,
      courseId:courseid,
      courseSemester: query.sm || null,
      statistics:1,
      isTe: isMain ? 0 : 1
    }).then((res: any) => {
      if (res.status == 200) {
        let newmapdata: any = null;
        // 如果地图有数据
        if (res.data.nodesVos.length) {
          // 构建模块数据
          if(query.type == 'microMajor'){
            let allllmodulearr: any = [];
            res.data.nodesVos.forEach((element:any) => {
              let {data} = JSON.parse(element.valueMap);
              if(data.type == 5){
                allllmodulearr.push({ label: data.label, value: element.nodeId });
              }
            });
            moudelNodedata.current = getKnowledgeBymouldes(allllmodulearr, {
              nodes:res.data.nodesVos.map((item:any)=>{
                const { data } = JSON.parse(item.valueMap);
                return{
                  ...item,
                  id: item.nodeId,
                  data: {
                    ...data,
                    isCollapsed:true,
                    visible:true
                  }
                }
              }),
              edges:res.data.relationVos.map((item:any)=>{
                return{
                  ...item,
                  source:{
                    cell:item.source
                  },
                  target:{
                    cell:item.target
                  },
                  data: JSON.parse(item.data)
                }
              }),
            });
          }else{
            // 教学模块详情要查看先修和后修的节点
            const alledegs = res.data.relationVos.map((item:any)=>{
              return{
                ...item,
                source:{
                  cell:item.source
                },
                target:{
                  cell:item.target
                },
                data: JSON.parse(item.data)
              }
            }).filter((edg:any)=>edg.data.type == 5);
            moudelNodedata.current = {
              allhouxiuedegs:alledegs,
              modulenodeList:{},
              notNeedToStudy:[]
            }
          }
          newmapdata = makedata(
            res.data.nodesVos,
            res.data.relationVos
          );
        } else {
          // 如果没有数据 就显示默认的节点
          const defaultNodes: any = defaultNode();
          newmapdata = makedata(
            defaultNodes.nodesVos,
            defaultNodes.relationVos
          );
        }

        let arr = makeedges(newmapdata.nodes, newmapdata.children_number);
        setMapData({
          ...arr,
          relationVos: res.data.relationVos
        });
      } else if (res.status == 400) {
        // 已经删除
        setErrorinfo(5);
        // message.info('当前课程地图已被删除！')
      } else {
        setErrorinfo(6);
        message.error(res.message);
      }
    });
  };

  // 生成数据  大概得意思是找到二级节点 然后再递归找到二级节点下面的所有知识点
  const makedata = (nodes: any, edges: any) => {
    // 最终生成的数据
    let newnodes: any = [];
    // 子节点数量 字典
    let children_number: any = {};
    let leafids: any = []
    const getchil = (treeData: any) => {
      const list: any = [];
      const tran = (data: any) => {
        data.forEach((item: any) => {
          let data1 = JSON.parse(item.valueMap)
          if (Number(data1.data.type) == 2) {
            list.push(item);
          }
          if (item.children) {
            tran(item.children);
          }
        })
      }
      tran(treeData);
      return list;
    }

    let newedeg = edges.filter((item: any) => {
      let data = JSON.parse(item.data);
      return (data.isnew == false || data.isnew == undefined)
    })

    let newedegs3: any = newedeg.map((item: any) => {
      return {
        ...item,
        source: {
          cell: item.source
        },
        target: {
          cell: item.target
        }
      }
    })
    let newnodes3: any = nodes.map((item: any) => {
      let data = JSON.parse(item.valueMap);
      if (Number(data.data.type) == 2) {
        leafids.push(item.nodeId);
      }
      return {
        ...item,
        id: item.nodeId,
        data:data
      }
    })

    let treearr = tranListToTreeData(newnodes3, newedegs3);
    console.log(treearr);

    if (treearr.length) {
      for (let index = 0; index < treearr[0].children.length; index++) {
        const element = treearr[0].children[index];
        let newarr1 = getchil(element.children);
        children_number[element.id] = newarr1.length || 0
        newnodes = [...newnodes, element, ...newarr1];
      }
    }
    if(showrete){
      // if (isMain) {
      //   getTeacherOrStudentRate()
      // }
      getTeacherOrStudentRate(isMain ? 0 : 1)
      // getknowlerate(leafids);
    } else {
      let obj: any = {};
      leafids.forEach((item: any) => {
        obj[item] = {
          finishRate: 0,
          masterRate: 0
        };
      });
      // setAllnodes(obj);
    }
    setKnowNodeIdList(leafids);
    return {
      nodes: newnodes,
      children_number: children_number,
    };
  };

  // 构造连线数据
  const makeedges = (nodesVos: any, children_number: any) => {
    let edges: any = [];
    let nodes: any = [];
    // 是否翻转
    let isReverse = false;
    
    nodesVos.forEach((item: any, index: number) => {
      let data = JSON.parse(item.valueMap);
      // 动态设置宽度 因为加了教学模块
      let nodewidth = 70;
      // if(data.data.type == 5){
      //   // nodewidth = getTextSize(data.data.label,18) + 100;
      //   nodewidth = 210;
      // }
      let obj = {
        ...item,
        id: item.nodeId,
        data: {
          ...defaultNodeData,
          ...data.data,
          mapId: item.mapId || null,
          children_number: children_number[item.nodeId] || 0,
          iscopy: false,
          visible:true
        },
        zIndex: 3,
        // shape:'circle',
        shape: 'react-shape',
        primer: 'circle', //节点形状
        component: 'react-node-v4',
        type: data.data.type,
        visible: true,
        width: nodewidth,
        height: 70,
        label: data.data.label,
      };
      if (index < nodesVos.length - 1) {
        if((index + 1) % 5 === 0){
          isReverse = !isReverse;
        }
        edges.push({
          data: { visible: true, type: 1, isnew: false,index: index },
          source: item.nodeId,
          target: nodesVos[index + 1].nodeId,
          type: 1,
          defaultLabel: {
            markup: Markup.getForeignObjectMarkup(),
            attrs: {
              fo: {
                width: 80,  // 根据实际图片尺寸调整
                height: 30, // 根据实际图片尺寸调整
                x: -40,     // 水平居中
                y: -15      // 垂直居中
              },
            },
          },
          attrs: {
            line: {
              stroke: '#FBFDFF', // 指定 path 元素的填充色,
              sourceMarker: null,
              targetMarker: null,
              strokeWidth: 10,
            },
          },
          label: {
            position: {
              distance: (index + 1) % 5 === 0? 0.35: 0.5,
              angle: (index + 1) % 5 === 0 ?  270 : isReverse ?180 : 0
            },
            
          }          
          
        });
      }
      nodes.push(obj);
    });

    return {
      nodes,
      edges,
    };
  };

  // 获取课程设置相关内容
  const getCode = () => {
    const courseId = location.query.id;
    const courseSemester = location.query.sm;

    baseInfo
      .getCourseDetails(
        courseId,
        courseSemester,
      )
      .then((res: any) => {
        if (res && res.message === 'OK') {
          dispatch({
            type: 'moocCourse/updateState',
            payload: {
              courseDetail: res.data,
            },
          });
        }
      });
  };

  // 添加查询当前学习节点方法
  const getCurrentLearnNode = () => {
    getKnowledgeStudyRecord({
      courseId: courseid,
      mapId: mapid,
      semester: query.sm,
    }).then((res: any) => {
      if (res.status === 200 && res.data?.nodeId) {
        setCurrentLearnNode(res.data.nodeId);
      }
    });
  };

  // 在初始化地图时查询学习节点
  useEffect(() => {
    if (mapid && !isMain) { // 学生端才需要查询
      getCurrentLearnNode();
    }
  }, [mapid]);

  useEffect(() => {
    if (mapid) {
      getCode();
      initcoursemap();
    }
  }, [mapid]);

  // 根据 节点是不是  0 是查询全部  1是查询重难点  2是查询有详解的节点
  const typeonselect = (LabeledValue: any) => {
    setQuerytype(LabeledValue);
    setVisible(6);
    searchref.current.querynode();
  };

  // 节点居中
  const centerednode = (id?: string) => {
    if (graph) {
      let cell = graph.getCellById(id);
      if (cell) {
        graph.centerCell(cell, { animation: { duration: 400 } });
      }
    }
  };

  // 设置状态 这里用防抖是因为 点击跨课 标签的点击事件 和 图谱 节点点击事件 会同时触发 取最后一次的
  const setVisiblestate = debounce((e: any) => {
    setVisible(e);
  }, 100);

  // 监听当前学习节点 如果存在 则居中 
  useEffect(()=>{
    if(currentLearnNode!=='' && graph){
      centerednode(currentLearnNode);
      // 居中后 清空当前学习节点
      setCurrentLearnNode('');  
    }
  },[currentLearnNode,graph])

  return (
    <div className="Persopathmap_view">
      <div className="render_view">
        {showrete && <div className="jindu_box">
          <div style={{ width: '200px', height: '100%' }}>
            <ProgressBox
              label="目标达成度"
              data={noderate?.achievingRateTotal}
              color="#549CFF"
            ></ProgressBox>
          </div>
          <div style={{ width: '200px', height: '100%' }}>
            <ProgressBox
              label="完成率"
              data={noderate?.finishRateTotal}
              color="#44D6E5"
            ></ProgressBox>
          </div>
          <div style={{ width: '200px', height: '100%' }}>
            <ProgressBox
              label="掌握率"
              data={noderate?.masterRateTotal}
              color="#F3B764"
            ></ProgressBox>
          </div>
        </div>}
        <div className="right_box">
          <Input.Group compact style={{ width: '300px' }}>
            <Select
              value={selecttype}
              defaultValue="0"
              style={{ width: '30%' }}
              onSelect={(e: any) => {
                setSelecttype(e);
              }}
            >
              <Option value="0">{t('全部')}</Option>
              <Option value="1">{t('分类节点')}</Option>
              <Option value="2">{t('知识节点')}</Option>
              {query.type == 'micromajor' || query.type == 'microMajor' &&
              <>
                <Option value="3">{t('课程节点')}</Option>
                <Option value="4">{t('专业节点')}</Option>
                <Option value="5">{t('教学模块')}</Option>
              </>}
            </Select>
            <Input
              value={inputtext}
              allowClear
              onPressEnter={() => {
                setVisible(6);
                setQuerytype('0');
                searchref.current.querynode();
              }}
              style={{ width: '70%' }}
              placeholder={t('请输入关键词')}
              onChange={e => setInputtext(e.target.value)}
              suffix={
                <IconFont
                  style={{ color: '#333333', fontSize: '14px', opacity: '0.7' }}
                  type="iconsousuo2"
                  onClick={() => {
                    setVisible(6);
                    setQuerytype('0');
                    searchref.current.querynode();
                  }}
                />
              }
            />
          </Input.Group>
          <div>
            <Select
              value={querytype}
              style={{ width: 120 }}
              onSelect={typeonselect}
            >
              {
                filteroptions.map((item,index)=>{
                  return <Option key={index} value={item.value}>{item.label}</Option>
                })
              }
            </Select>
          </div>
          <div>
            <Select
              defaultValue={showtype}
              style={{ width: 140 }}
              onChange={e => setShowtype(e)}
              options={[
                {
                  value: 1,
                  label: '按完成率展示',
                },
                {
                  value: 2,
                  label: '按掌握率展示',
                },
              ]}
            />
          </div>
        </div>
        <GridMap
          mapdata={mapdata}
          moudelNodedata={moudelNodedata.current}
          nodeClick={(node: any) => {
            const data = node.getData();
            if (data.type == 2) {
              setDrawerdata(node);
              recordCurrentNode({
                courseId: courseid,
                mapId: mapid,
                nodeId: node.id,
                semester: query.sm || null,
              });
              if(query.type == 'microMajor'){
                checkshowmessage(node,moudelNodedata.current,query.id,mapid,query.sm).then((res:any)=>{
                  if(!res){
                    setNotrecord(true);
                    message.warning('现在学习该节点将不计掌握率完成率，请先完成先修模块的学习！')
                  }
                  setVisiblestate(1);
                })
              }else{
                setVisiblestate(1);
              }

            } else if(data.type == 5){
              if (TeachingmoduleRef.current) {
                TeachingmoduleRef.current.setaddForm({
                  nodeId: node.id,
                  ...data
                })
              }
              setVisiblestate(20);
            }else{
              console.log('其他类型的节点不处理');
            }
          }}
          currentLearnNode={currentLearnNode}
          allnodes={allnodes}
          showtype={showtype}
          laytype={2}
          initover={(e: any) => {
            setGraph(e);
            if(grayedoutarr.length){
              grayedoutarr.forEach((item:any) => {
                let element = e.getCellById(item);
                if(element){
                  const data: any = element.getData();
                  element.updateData({
                    ...data,
                    showgrey: true
                  });
                }
              });
            }
          }}
        />
        <Search
          ref={searchref}
          graph={graph}
          querytype={querytype}
          centerednode={(e: any) => centerednode(e)}
          selecttype={selecttype}
          inputtext={inputtext}
          visible={visible}
          setVisible={(e: number) => setVisible(e)}
        ></Search>
        {/* 教学模块展示 */}
        <Teachingmodule
            visible={visible}
            setVisible={setVisible}
            titlemodel={3}
            graph={graph}
            moudelNodedata={moudelNodedata.current}
            ref={TeachingmoduleRef}></Teachingmodule>
        {/* 节点详情 */}
        <Drawer
          placement="right"
          mask={false}
          title={
            <div
              style={{ position: 'relative', width: '100%', height: '100%' }}
            >
              {wholeDrawer ? <img style={{ width: '100%', height: '46px' }} src={require('@/assets/imgs/coursemap/bg_header.png')}></img> : <>
                <img style={{ width: '100%', height: '46px' }} src={require('@/assets/imgs/coursemap/v4/bg7.png')}></img>
                <img onClick={() => {
                  setVisible(0);
                  // 获取学习进度
                }} style={{ position: 'absolute', right: '20px', width: '15px', top: '22px', cursor: 'pointer' }} src={require('@/assets/imgs/coursemap/v3/close.png')} alt="" />
              </>
              }
            </div>
          }
          closable={false}
          onClose={() => {
            setVisible(0);
            setDrawerdata(null);
          }}
          visible={visible == 1}
          getContainer={false}
          style={{ position: 'absolute' }}
          width={wholeDrawer ? '100%' : '600px'}
          className={
            wholeDrawer
              ? 'custom_drawer node_detail_view'
              : 'drawer node_detail_view'
          }
        >
          {wholeDrawer ? (
            <WholeRdrawer
              graph={graph}
              x6node={drawerdata}
              previewEntity={previewEntity}
              setTotalTime={setTotalTime}
              totalTime={totalTime}
              setCurrentTime={setCurrentTime}
              currentTime={currentTime}
              setPreviewEntity={setPreviewEntity}
              visible={visible}
              onback={() => setDrawerdata(null)}
              centerednode={(e: any) => centerednode(e)}
              perviewtype={query.perviewtype || 2}
              mapid={mapid}
              courseid={courseid}
              coursename={coursename || ''}
              isedit={false}
              bindCourseCode={bindCourseCode}
              isv3={true}
              isMain={isMain}
              isMicroMajor={query?.type === 'microMajor'}
              setVisible={(e: number) => setVisible(e)}
              courseCode={couresSyllabusCode || bindCourseId || ''}
            ></WholeRdrawer>
          ) : (
            <Rdrawer
              graph={graph}
              x6node={drawerdata}
              visible={visible}
              onback={() => setDrawerdata(null)}
              // updatanodecompare={updatanodecompare}
              centerednode={centerednode}
              setBindCourseCode={setBindCourseCode}
              setTotalTime={setTotalTime}
              totalTime={totalTime}
              setCurrentTime={setCurrentTime}
              currentTime={currentTime}
              previewEntity={previewEntity}
              setPreviewEntity={setPreviewEntity}
              // updatanode={updatanode}
              perviewtype={query.perviewtype || 2}
              bindCourseCode={bindCourseCode}
              mapid={mapid}
              courseid={courseid}
              coursename={coursename || ''}
              isedit={false}
              isv3={true}
              ismapcourse={true}
              isMain={isMain}
              setVisible={(e: number) => setVisible(e)}
              isMicroMajor={query?.type === 'microMajor'}
              notrecord={notrecord}
              courseCode={couresSyllabusCode || bindCourseId || ''}
            ></Rdrawer>
          )}
          {wholeDrawer ? (
            <img
              onClick={() => setWholeDrawer(false)}
              className="arrow"
              src={require('@/assets/imgs/coursemap/right_arrow.png')}
            />
          ) : (
            <img
              onClick={() => setWholeDrawer(true)}
              className="arrow"
              src={require('@/assets/imgs/coursemap/left_arrow.png')}
            />
          )}
        </Drawer>
      </div>
    </div>
  );
};

export default Persopathmap;
