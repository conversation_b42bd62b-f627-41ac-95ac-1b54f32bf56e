import React, { useEffect } from 'react';
import { Menu, Dropdown, message } from 'antd';
import { CodepenOutlined, OrderedListOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/iconFont';
import { DataUri } from '@antv/x6';
import useLocale from '@/hooks/useLocale';
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import { insertlabel, getLabels, deleteLabel, updateLabel } from '@/api/labelConfig';
interface MapToolbarProps {
  perviewtype: number;
  selectnode: any[];
  parameterConfig: any;
  userroles: string[];
  query: any;
  graph: any;
  restore?: boolean;
  newmapid: string;
  kuangxuan: boolean;
  setVisible: (value: number) => void;
  setConversionVisible: (value: boolean) => void;
  addChild: (node: any, type: number) => void;
  updatType: (node: any, type: number) => void;
  addlink: (item: any) => void;
  uodatayinandian: (node: any, value: boolean) => void;
  uodatabykey: (node: any, value: boolean, key: string) => void;
  deletenode: () => void;
  multipleSelection: (value: boolean) => void;
  setGraphmap: (graph: any) => void;
  setDrawerdata: (data: any) => void;
  setExpandnode: (node: any) => void;
  setTemptime: (time: number) => void;
  exportmaptoexcel: () => void;
  relationarr: any[];
  updatalabel: (node: any, flage: boolean) => void;
  allLabels:any,
  setAddtype:any
}

const MapToolbar: React.FC<MapToolbarProps> = ({
  perviewtype,
  selectnode,
  parameterConfig,
  userroles,
  query,
  graph,
  restore,
  newmapid,
  kuangxuan,
  setVisible,
  setConversionVisible,
  addChild,
  updatType,
  addlink,
  uodatayinandian,
  uodatabykey,
  deletenode,
  multipleSelection,
  setGraphmap,
  setDrawerdata,
  setExpandnode,
  setTemptime,
  exportmaptoexcel,
  relationarr,
  updatalabel,
  allLabels,
  setAddtype
}) => {
  const { t } = useLocale();

  if (![0, 1, 3].includes(perviewtype)) {
    return null;
  }

  return (
    <div className="options_box">
      <Dropdown trigger={["click","hover"]}
        overlay={
          <Menu>
            <Menu.Item
              key="1"
              onClick={() => {
                setVisible(7);
              }}
            >
              {t('导入模板')}
            </Menu.Item>
            {parameterConfig?.Importoutline === 'true' ? (
              <Menu.Item
                key="2"
                onClick={() => {
                  setVisible(19);
                }}
              >
                {t('导入大纲')}
              </Menu.Item>
            ) : null}
            <Menu.Item key="3" onClick={() => setVisible(15)}>
              {t('导入xmind')}
            </Menu.Item>
            {parameterConfig.show_import_recourse == 'true' &&  <Menu.SubMenu key="8" title={t('按视频生成')}>
              <Menu.Item key="roucese" onClick={() => {setVisible(24);setAddtype(0);}}>
                {t('资源库')}
              </Menu.Item>
              <Menu.Item key="linecourse" onClick={() => {setVisible(25);setAddtype(0);}}>
                {t('在线课堂')}
              </Menu.Item>
            </Menu.SubMenu>}
            <Menu.Item
              key="4"
              onClick={() => {
                setVisible(10);
              }}
            >
              {t('按教材生成')}
            </Menu.Item>
            {parameterConfig.generate_chapter == 'true' && <Menu.Item key="5" onClick={() => setConversionVisible(true)}>
              {t('按章节转换')}
            </Menu.Item>}
            {(userroles.includes('admin_S1') ||
              userroles.includes('r_teacher') ||
              userroles.includes('r_sys_manager') ||
              userroles.includes('r_course_manager')) && (
              <Menu.Item key="6" onClick={() => setVisible(16)}>
                {t('引用地图')}
              </Menu.Item>
            )}
            {parameterConfig?.synchronize_outline_content === 'true' && (
              <Menu.Item
                key="7"
                onClick={() => {
                  setVisible(22);
                }}
              >
                {t('同步大纲教学内容')}
              </Menu.Item>
            )}
          </Menu>
        }
      >
        <div className="item_ops">
          <img
            style={{ width: '20px' }}
            src={require('@/assets/imgs/coursemap/editmap.png')}
          />
          <div className="op_name">{t("地图导入")}</div>
        </div>
      </Dropdown>
      <div
        className="item_ops"
        onClick={() => {
          if (selectnode.length == 1) {
            const data = selectnode[0].getData();
            // 如果选择的是子节点 允许新建  知识节点 不允许新建
            if (data.type == 1 || data.type == 5) {
              addChild(selectnode[0], 1);
              return;
            }
          }
          message.error(t('请选择一个分类节点'));
        }}
      >
        <img
          style={{ width: '18px' }}
          src={require('@/assets/imgs/coursemap/fenleijiedian.png')}
        />
        <div className="op_name">+{t("分类节点")}</div>
      </div>
      <div
        className="item_ops"
        onClick={() => {
          if (selectnode.length == 1) {
            addChild(selectnode[0], 2);
          } else {
            message.error(t('请选择一个节点'));
          }
        }}
      >
        <img
          style={{ width: '18px' }}
          src={require('@/assets/imgs/coursemap/zhishidian.png')}
        />
        <div className="op_name">+{t("知识节点")}</div>
      </div>
      <Dropdown
        overlay={
          <Menu key="relation">
            {relationarr.map((item: any) => {
              if (item.show) {
                return (
                  <Menu.Item key={item.key} onClick={() => addlink(item)}>
                    {item.name}
                  </Menu.Item>
                );
              }
            })}
          </Menu>
        }
      >
        <div className="item_ops" style={{width:'85px'}}>
          <img
            style={{ width: '18px' }}
            src={require('@/assets/imgs/coursemap/lianxian.png')}
          />
          <div className="op_name">{t("知识点关联")}</div>
        </div>
      </Dropdown>
      <div
          className="item_ops"
          onClick={() => {
          if (selectnode.length == 1) {
              const data = selectnode[0].getData();
              // 如果选择的是子节点 允许新建  知识节点 不允许新建
              if (Number(data.type) == 2) {
              setVisible(1);
              setDrawerdata(selectnode[0]);
              setGraphmap(graph);
              }
          }else{
              message.error(t('请选择一个')+t('知识节点'));
          }
          }}
      >
          <IconFont type="iconpicixiangqing" style={{fontSize: '18px'}} />
          <div className="op_name">{t("查看详情")}</div>
      </div>
      <Dropdown
        overlay={
          <Menu>
            <Menu.Item
              onClick={() => {
                // 批量修改节点类型
                if (selectnode.length) {
                  selectnode.forEach((item: any) => {
                    updatType(item, 1);
                  });
                } else {
                  message.error(t('请选择一个节点'));
                }
              }}
            >
              {t('分类节点')}
            </Menu.Item>
            <Menu.Item
              onClick={() => {
                // 批量修改节点类型
                if (selectnode.length) {
                  selectnode.forEach((item: any) => {
                    updatType(item, 2);
                  });
                } else {
                  message.error(t('请选择一个节点'));
                }
              }}
            >
              {t('知识节点')}
            </Menu.Item>
            {query.type == 'micromajor' ||
              (query.type == 'microMajor' && (
                <Menu.Item
                  onClick={() => {
                    // 批量修改节点类型
                    if (selectnode.length) {
                      selectnode.forEach((item: any) => {
                        updatType(item, 5);
                      });
                    } else {
                      message.error(t('请选择一个节点'));
                    }
                  }}
                >
                  {t('教学模块')}
                </Menu.Item>
              ))}
          </Menu>
        }
      >
        <div className="item_ops">
          <img
            style={{ width: '20px' }}
            src={require('@/assets/imgs/coursemap/qiehuan.png')}
          />
          <div className="op_name">{t("修改类型")}</div>
        </div>
      </Dropdown>
      <Dropdown
        overlay={
          <Menu>
            <Menu.SubMenu key="submenu-difficulty" title={t('重难点')}>
              <Menu.Item onClick={() => uodatayinandian(null, true)}>
                {t('设置')}
              </Menu.Item>
              <Menu.Item onClick={() => uodatayinandian(null, false)}>
                {t('取消')}
              </Menu.Item>
            </Menu.SubMenu>
            <Menu.SubMenu key="submenu-core" title={t('核心知识点')}>
              <Menu.Item
                onClick={() => uodatabykey(null, true, 'iscoreknowledge')}
              >
                {t('设置')}
              </Menu.Item>
              <Menu.Item
                onClick={() => uodatabykey(null, false, 'iscoreknowledge')}
              >
                {t('取消')}
              </Menu.Item>
            </Menu.SubMenu>
            <Menu.SubMenu key="submenu-expand" title={t('拓展知识点')}>
              <Menu.Item
                onClick={() => uodatabykey(null, true, 'isexpandknowledge')}
              >
                {t('设置')}
              </Menu.Item>
              <Menu.Item
                onClick={() => uodatabykey(null, false, 'isexpandknowledge')}
              >
                {t('取消')}
              </Menu.Item>
            </Menu.SubMenu>
            <Menu.SubMenu key="submenu-case" title={t('案例')}>
              <Menu.Item onClick={() => uodatabykey(null, true, 'iscase')}>
                {t('设置')}
              </Menu.Item>
              <Menu.Item onClick={() => uodatabykey(null, false, 'iscase')}>
                {t('取消')}
              </Menu.Item>
            </Menu.SubMenu>
            <Menu.SubMenu key="submenu-experiment" title={t('实验')}>
              <Menu.Item
                onClick={() => uodatabykey(null, true, 'isexperiment')}
              >
                {t('设置')}
              </Menu.Item>
              <Menu.Item
                onClick={() => uodatabykey(null, false, 'isexperiment')}
              >
                {t('取消')}
              </Menu.Item>              
            </Menu.SubMenu>
            {allLabels.length && <Menu.SubMenu key="submenu-custom-labels" title={t('自定义标签')}>
            {
                allLabels.map((item:any,index:number) => {
                  return (
                    <React.Fragment key={`label-${index}`}>
                      <Menu.Item key={'set'+index}
                        onClick={() => updatalabel(item,true)}
                      >
                        {t('设为')+item.name}
                      </Menu.Item>
                      <Menu.Item key={'cancel'+index}
                        onClick={() => updatalabel(item,false)}
                      >
                        {t('取消')+item.name}
                      </Menu.Item>              
                    </React.Fragment>
                  );
                })
              }
            </Menu.SubMenu>}
          </Menu>
        }
      >
        <div className="item_ops">
          <img
            style={{ width: '20px' }}
            src={require('@/assets/imgs/coursemap/zhongnandian.png')}
          />
          <div className="op_name">{t('标签')}</div>
        </div>
      </Dropdown>
      <div
        className="item_ops"
        onClick={() => {
          if(!restore){
            setDrawerdata(null);
            setGraphmap(graph);
            setVisible(4);
          }else{
            message.info(t('请先点击保存，再进行对比辨析操作'));
          }
        }}
      >
        <img
          style={{ width: '20px' }}
          src={require('@/assets/imgs/coursemap/duibibianxi.png')}
        />
        <div className="op_name">{t('对比辨析')}</div>
      </div>
      <div
        className="item_ops"
        onClick={() => {
          if (selectnode.length) {
            deletenode();
          } else {
            message.error(t('请选择一个节点'));
          }
        }}
      >
        <img
          style={{ width: '20px' }}
          src={require('@/assets/imgs/coursemap/shanchu.png')}
        />
        <div className="op_name">{t("删除")}</div>
      </div>
      <div
        className={kuangxuan ? 'item_ops active' : 'item_ops'}
        onClick={() => {
          multipleSelection(kuangxuan);
        }}
      >
        <IconFont type="iconkuangxuan" style={{ fontSize: '16px' }} />
        <div className="op_name">{t("框选")}</div>
      </div>
      <div
        className="item_ops"
        onClick={() => {
          setVisible(5);
          setGraphmap(graph);
        }}
      >
        {/* <img
          style={{ width: '18px' }}
          src={require('@/assets/imgs/coursemap/link.png')}
        /> */}
        <CodepenOutlined style={{ fontSize: '18px' }} />
        <div className="op_name">{parameterConfig.target_customer === CUSTOMER_NPU ? t("资源配置") : t("绑定管理")}</div>
      </div>
      <div
        className="item_ops"
        onClick={() => {
          setVisible(23);
          setGraphmap(graph);
        }}
      >
        <img
          style={{ width: '18px' }}
          src={require('@/assets/imgs/coursemap/link.png')}
        />
        <div className="op_name">{t("一键绑定")}</div>
      </div>
      <div
        className="item_ops"
        onClick={() => {
          setVisible(9);
          setExpandnode(null);
          setGraphmap(graph);
          setTemptime(new Date().getTime());
        }}
      >
        <img
          style={{ width: '18px' }}
          src={require('@/assets/imgs/coursemap/dagang.png')}
        />
        <div className="op_name">{t("大纲视图")}</div>
      </div>
      <Dropdown
        overlay={
          <Menu>
            {/* <Menu.Item
                            onClick={() => {
                              graph.toSVG((dataUri: string) => {
                                // 下载
                                DataUri.downloadDataUri(DataUri.svgToDataUrl(dataUri), '图谱图片.svg')
                              })
                            }}
                          >
                            {t('svg 图片')}
                          </Menu.Item> */}
            <Menu.Item
              onClick={() => {
                graph.toPNG(
                  (dataUri: string) => {
                    // 下载
                    DataUri.downloadDataUri(dataUri, '图谱图片.png');
                  },
                  {
                    padding: {
                      top: 20,
                      right: 30,
                      bottom: 40,
                      left: 50,
                    },
                  },
                );
              }}
            >
              {t('png 图片')}
            </Menu.Item>
            <Menu.Item
              onClick={() => {
                exportmaptoexcel();
              }}
            >
              {t('excel 文件')}
            </Menu.Item>
            {parameterConfig.knowledgebase_display == 'true' && <Menu.Item
              onClick={() => {
                window.open(
                  '/learn/m1/knowledge/course/export/entity?mapId=' + newmapid,
                );
              }}
            >
              {t('知识库文件')}
            </Menu.Item>}
            <Menu.Item
              onClick={() => {
                window.open(
                  '/learn/m1/knowledge/course/export/xmind?mapId=' + newmapid,
                );
              }}
            >
              {t('xmind文件')}
            </Menu.Item>
            <Menu.Item
              onClick={() => {
                window.open(
                  '/learn/m1/knowledge/course/export/map/doc?mapId=' + newmapid,
                );
              }}
            >
              {t('word文件')}
            </Menu.Item>
          </Menu>
        }
      >
        <div className="item_ops">
          <img
            style={{ width: '18px' }}
            src={require('@/assets/imgs/coursemap/fenxiang.png')}
          />
          <div className="op_name">{t("导出")}</div>
        </div>
      </Dropdown>
      <div
        className="item_ops"
        onClick={() => {
          setVisible(11);
        }}
      >
        <OrderedListOutlined style={{ fontSize: '16px' }} />
        <div className="op_name">{t("历史记录")}</div>
      </div>
    </div>
  );
};

export default MapToolbar;
