import React, { FC, useRef, useState } from 'react';
import { Modal, Upload, Button, message } from 'antd';
import Icon, { DeleteOutlined } from '@ant-design/icons';
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import chapterApis from "@/api/chapter";
import { useLocation } from "umi";
import "./WordImportModal.less";
import useLocale from '@/hooks/useLocale';

const { Dragger } = Upload;

interface IWordImport {
  visible: boolean;
  onClose: () => void;
  uploadType: string
}
const uploadTypeArr = ['zip'];
const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;

const WordImportModal: FC<IWordImport> = ({ visible, onClose,uploadType = 'one' }) => {
  console.log('uploadType', uploadType);
  const { t } = useLocale();
  const location: any = useLocation();
  const [fileData, setFileData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const props = {
    name: 'file',
    showUploadList: false,
    // action: '/rman/v1/upload/reference/material/import',
    beforeUpload(file: any) {
      const nameArr = file.name.split('.');
      console.log(nameArr[nameArr.length - 1])
      const name = nameArr[nameArr.length - 1];
      if (uploadType === 'one') {
        if (name === 'docx') {
          setFileData(file);
        } else {
          message.warning("只能上传word文件！");
        }
      } else if (uploadType === 'two') {
        if (uploadTypeArr.includes(name)) {
          setFileData(file);
        } else {
          message.warning("只能上传zip格式文件！");
        }
      }

      return false;
    },
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    }
  };
  const handleImport = () => {
    if (!fileData.name) {
      message.warning(t("请先上传文件再导入！"));
      return;
    }
    setLoading(true);
    const formData = new FormData();
    formData.append("file", fileData);
    const api = uploadType === 'one' ? chapterApis.importChapter : chapterApis.importChapterAll;
    api({ contentId: location.query.id, courseSemester: location.query.sm ?? 1 }, formData).then((res: any) => {
      console.log(res,'res')
      if (res.status === 200) {
        message.success(t("导入成功"));
      } else {
        message.error(t("导入失败,请按照模板填写"));
      }
    }).finally(() => {
      handleClose();
      setLoading(false);
    });
  };
  const handleClose = () => {
    setFileData(null);
    onClose();
  };
  const handleDelete = () => {
    setFileData(null);
  };
  const handleDownload = (e: any) => {
    e.stopPropagation();
    const api = uploadType === 'one' ? chapterApis.downloadChapterModal() : chapterApis.downloadChapterModalAll();
    api.then((res) => {
      const blobURL = window.URL.createObjectURL(res);
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a');
      tempLink.style.display = 'none';
      tempLink.href = blobURL;
      const fileName = uploadType === 'one' ? '导入模板.docx' : '章节、资源同步导入模板.zip';
      tempLink.setAttribute('download', fileName);
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank');
      }
      // 挂载a标签
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      // 释放blob URL地址
      window.URL.revokeObjectURL(blobURL);
    });
  };
  return <Modal title={ uploadType === 'one' ?   t("章节导入") : t("同时导入章节、资源")} visible={visible} footer={null} onCancel={handleClose}>
    <div className="import-chapter">
      {
        fileData?.name ? <div className='file-name'>{fileData?.name}<DeleteOutlined onClick={handleDelete} /></div> : <Dragger {...props}>
          <p className="ant-upload-drag-icon">
            <PlusIcon />
          </p>
          <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
          {/* <p className="ant-upload-text">上传成功的文件可在个人资源-课程上传资源文件夹里找到</p> */}
          <div>
            { uploadType === 'two'  &&
            <span>仅支持导入zip格式文件</span>
            }
            <a className='download-text' onClick={handleDownload}>{t("下载导入模板")}</a>
          </div>
        </Dragger>}

      <div className="btn-group">
        <Button type="primary" loading={loading} onClick={handleImport}>{loading ? t("导入中") : t("开始导入")}</Button>
      </div>
    </div>

  </Modal>;
};

export default WordImportModal;
