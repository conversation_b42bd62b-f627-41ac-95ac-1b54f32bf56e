import React, { useState, useEffect, FC } from 'react';
import {
  login,
  userInfo,
  metaData,
  teacherInfo,
  getUserJurisdictionV2
} from
  '@/api/course';
import './index.less';
import '@/assets/styles/index.css';

import {Layout, message, Menu, Tabs, Badge} from 'antd';
import { Link, useHistory } from 'react-router-dom';

import { useDispatch, useLocation } from 'umi';
import { HashRouter } from 'react-router-dom';
import courseTemplate from '@/api/courseTemplate';
import Loading from '@/components/loading/loading';
import { useSelector } from '@@/plugin-dva/exports';
import ModuleCfg, { CUSTOMER_NPU, CUSTOMER_CXD } from '@/permission/moduleCfg';
import { ModuleCfg2 } from '@/permission/moduleCfg';
import perCfg from '@/permission/config';
import Header from '@/components/Header';
import LeftMenu from '@/components/LeftMenu';
import NPUHeader from '@/components/NPUHeader';
import useLocale from '@/hooks/useLocale';
import {IconFont} from "@/components/iconFont";

const { Content, Sider } = Layout;

interface ICatalogue {
  contentId: string;
  description: string;
  name: string;
  parentPath: string;
  type: string;
  path: string;
}

const App: FC = ({ children }) => {
  const isOut = window.location.hash.includes("out")
  const { t } = useLocale();
  let history: any = useHistory();
  const [selectedKey, setSelectedKey] = useState<string>(
    '/coursetemplate/mytemplate');
  const { userInfo } = useSelector<any, any>(state => state.global);
  // const [catalogueList, setCatalogueList] = useState<ICatalogue[]>([]);
  const [hash, setHash] = useState(window.location.hash);
  const dispatch = useDispatch();
  const permissionModules = useSelector<any, any>(
    ({ jurisdiction }) => jurisdiction.modules);
  //移动端适配
  const { mobileFlag, leftRightVisible } = useSelector<{ config: any; }, any>(
    ({ config }) => config);
   const [reviewCount, setReviewCount] = useState(0);
  const { parameterConfig, buttonPermission, permission } = useSelector<
    { global: any; },
    { buttonPermission: string[]; parameterConfig: any; permission: any; }>(
      (state) => state.global);
  console.log(parameterConfig,'parameterConfig')
  console.info(useLocation().pathname);
  let path = useLocation().pathname;
  const active = useLocation().pathname ?
    useLocation().pathname.split('/')[1] :
    '';
  useEffect(() => {
    // ...原有代码
    const handleRefreshReview = () => {
      console.log('888888')
      fetchDataList();
    };
    window.addEventListener('refreshReviewCount', handleRefreshReview);
    return () => {
      window.removeEventListener('refreshReviewCount', handleRefreshReview);
    };
  }, []);
  useEffect(() => {
    // getLogin()
    fetchDataList();
    getTemplateCatalogueList();
    getJurisdiction();
    screenResize();
    window.addEventListener('resize', function () {
      //移动端判定
      screenResize();
      return () => {
        window.removeEventListener('resize', screenResize);
      };
    });
  }, []);
  const [query, setQuery] = useState<any>({
    page: 1,
    size: 24,
    courseType: -1,
    // publishStatus: 2
    approvalStatus: null,
  });
  const fetchDataList = () => {
    console.log(query,'query')
    courseTemplate.getMyReview({
      ...query,
      teacher: query.teacher ? [query.teacher] : null,
      subjectId: query.subjectId ? [query.subjectId] : null,
      // classificationId: query.classificationId ? [query.classificationId] : null
      // approvalStatus: query.approvalStatus ?? 0
      approvalStatus: 1,
      modelKey: 'course_template_publish'
    })
      .then(res => {
        if (res.message === 'OK') {
          const {  total } = res.data;
          setReviewCount(total);
        }
      })
      .catch((err) => {
        console.log(err);
      } );
  };
  const screenResize = () => {
    //移动端判定
    if (
      navigator.userAgent.match(/Mobi/i) ||
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/iPhone/i) ||
      window.innerWidth < 768) {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: true,
          menuShowChange: false
        }
      });
    } else {
      dispatch({
        type: 'config/updateState',
        payload: {
          mobileFlag: false,
          menuShowChange: true
        }
      });
    }
  };
  const getHash = () => {
    setHash(window.location.hash);
  };

  window.onhashchange = () => {
    getHash();
  };
  const getTemplateCatalogueList = () => {
    // courseTemplate.getTemplateCatalogue().then(res => {
    //   if (res && res.errorCode === "course_0000_0000") {
    //     let list = res.extendMessage.results
    //     list.forEach((item: ICatalogue) => {
    //       if (item.name === '共享模板') {
    //         item.path = `/coursetemplate/sharetemplate`
    //       } else {
    //         item.path = `/coursetemplate/mytemplate`
    //       }
    //     })
    //     setCatalogueList(list)
    //     history.push(`${list[0].path}?cataloguepath=${list[0].parentPath}/${list[0].name}`)
    //   }
    // })
  }; const getJurisdiction = () => {
    getUserJurisdictionV2().then((res) => {
      if (res && res.errorCode === 'success' && typeof res.extendMessage.moduleFeatures === 'object') {
        const permission = res.extendMessage.moduleFeatures; dispatch({
          type: 'jurisdiction/updateState', payload: {
            jurisdictionList: Object.keys(permission).map((key) => permission[key]).
              flat(2),
            modules: res.extendMessage.modules || []
          }
        });
      } else {
        message.error(t('权限信息获取失败！'));
      }
    }).
      catch((error) => {
        message.error(t('权限信息获取失败！'));
      });
  };
  const sidebarClick = () => {
    if (mobileFlag) {
      dispatch({
        type: 'config/updateState',
        payload: {
          menuShow: false,
          leftRightVisible: false
        }
      });
    }
  };
  const tabChange = (e: any) => {
    console.log(e);
    if (e == 'myCourseResource') {
      history.push(`/coursetemplate${isOut ? '/out' : ''}/mytemplate?cataloguepath=/coursetemplate${isOut ? '/out' : ''}/mytemplate`);
    } else {
      history.push(`/coursetemplate${isOut ? '/out' : ''}/sharetemplate?cataloguepath=/coursetemplate${isOut ? '/out' : ''}/sharetemplate`);
    }
  };
  return (
    <HashRouter>
      <div className={`App${mobileFlag ? ' mobileContainer' : ''} ${isOut ? 'out-page' : ''}`}>
        <Layout>
          {(!isOut && (parameterConfig.target_customer === CUSTOMER_NPU ? <NPUHeader /> : <Header />))}
          <div className={`content-box`}>
            {parameterConfig.target_customer != null && parameterConfig.target_customer !== CUSTOMER_NPU && parameterConfig.target_customer !== CUSTOMER_CXD && <LeftMenu height={isOut ? '100vh' : undefined} />}
            <Layout>
              <Sider width={180} theme="light" onClick={sidebarClick} className={`site-layout-background none`}>
                <Menu defaultSelectedKeys={[path]} mode="inline" theme="light">
                  {permissionModules.includes(ModuleCfg.tpl_mine) &&
                    <Menu.Item key={'/coursetemplate/mytemplate'}>
                      <Link
                        to={`/coursetemplate${isOut ? '/out' : ''}/mytemplate?cataloguepath=/coursetemplate${isOut ? '/out' : ''}/mytemplate`}>
                        {t("我的课程资源")}

                      </Link>
                    </Menu.Item>}

                  {permissionModules.includes(ModuleCfg.tpl_share) &&
                    <Menu.Item key={'/coursetemplate/sharetemplate'}>
                      <Link
                        to={`/coursetemplate${isOut ? '/out' : ''}/sharetemplate?cataloguepath=/coursetemplate${isOut ? '/out' : ''}/sharetemplate`}>
                        {t("共享课程资源")}
                      </Link>
                    </Menu.Item>}
                  {(userInfo.roles
                      ?.map((item: any) => item.roleCode)
                      ?.includes('r_sys_manager') || //是否是系统管理员
                    userInfo.roles
                      ?.map((item: any) => item.roleCode)
                      ?.includes('r_course_manager') || //是否是课程管理员
                    userInfo.roles
                      ?.map((item: any) => item.roleCode)
                      ?.includes('r_second_manager') || //第二权限
                    userInfo.roles
                      ?.map((item: any) => item.roleCode)
                      ?.includes('admin_S1')) && parameterConfig.template_course_release_review == 'true' &&

                    <Menu.Item key={'/coursetemplate/myReview'}>
                      <Link to="/coursetemplate/myReview">
                        <Badge count={reviewCount} offset={[20, 8]}>
                          <span style={{ fontSize: '16px' }}>
                          {t('我的审核')}
                          </span>
                        </Badge>
                      </Link>
                    </Menu.Item>
                  }
                </Menu>
              </Sider>
              <Layout
                // style={{ padding: '15px 15px 0px 15px', background: '#F7F9FA' }}
                className={mobileFlag ? 'mobile_right' : ''}>

                {
                  mobileFlag ?
                    <div className='mobile_container'>
                      <div className='tabs'>
                        <Tabs defaultActiveKey='myCourseResource' onChange={tabChange}>
                          <Tabs.TabPane key={'myCourseResource'} tab={t("我的课程资源")}></Tabs.TabPane>
                          <Tabs.TabPane key={'shareCourseResource'} tab={t("共享课程资源")}></Tabs.TabPane>
                        </Tabs>
                      </div>
                      <Content
                        className="site-layout-background"
                        style={{
                          margin: '0 0 0 1px',
                          minHeight: 280,
                          overflowX: 'hidden',
                          overflowY: 'auto'
                        }}>

                        {children}
                      </Content>
                    </div> :

                    <Content
                      className="site-layout-background"
                      style={{
                        margin: '0 0 0 1px',
                        minHeight: 280,
                        overflowX: 'hidden',
                        overflowY: 'auto'
                      }}>

                      {children}
                    </Content>}


                <Loading />
              </Layout>
            </Layout>
          </div>

        </Layout>
      </div>
    </HashRouter>);

};

export default App;
