.map-search {
  width: 100%;
  display: flex;
  align-items: center;
  
  .reset-wrp {
    cursor: pointer;
    height: 32px;
    display: flex;
    align-items: center;
    color: #525252;
    // margin: 6px 14px 0 -6px;
    .anticon {
      margin-left: 5px;
    }
  }
  //移动端
  .pubilc_form {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    width: 100%;

    .ant-input-group-wrapper {
      border-radius: 0;
      margin: 0 3% 0 0;

      .ant-input-affix-wrapper {
        border-radius: 0;
      }
    }
  }
  .ant-modal-mask {
    transition: all 0.5s;
  }
  .moreSearch {
    &.hide {
      top: -100%;
    }

    position: fixed;
    top: 0;
    left: 0;
    transition: top 0.5s;
    background: white;
    z-index: 1000;
    width: 100%;
    // height: 60%;
    padding: 5px 5%;

    .head {
      margin-top: 20px;
      text-align: center;

      > span:first-child {
        font-size: 18px;
      }

      .anticon {
        position: absolute;
        right: 7%;
        line-height: 30px;
      }
    }

    .body {
      margin-top: 20px;

      .ant-form-item {
        margin-bottom: 16px !important;

        .ant-select {
          width: 100% !important;
        }
      }

      > .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;

        > div {
          width: 100%;
        }
      }

      .ant-picker-time-panel {
        display: none;
      }
    }

    .btns {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      flex-wrap: nowrap;
      margin-bottom: 10px;
    }
  }
}
