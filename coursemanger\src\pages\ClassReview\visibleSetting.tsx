import {
  // studentsSynchronization,
  fetchClassmates,
  queryCourseReviewSetting,
  updateAuthoritySingle,
} from '@/api/course';
import AddStudentModal from '@/components/AddStudentModal';
import useLocale from '@/hooks/useLocale';
import { LeftCircleFilled, QuestionCircleOutlined } from '@ant-design/icons';
import DeleteOutlined from '@ant-design/icons/lib/icons/DeleteOutlined';
import ExclamationCircleOutlined from '@ant-design/icons/lib/icons/ExclamationCircleOutlined';
import { CUSTOMER_SHTECH, CUSTOMER_YNU } from '@/permission/moduleCfg';

import UserOutlined from '@ant-design/icons/lib/icons/UserOutlined';
import {
  Avatar,
  Button,
  Checkbox,
  message,
  Modal,
  Radio,
  Switch,
  Table,
  Tooltip,
} from 'antd';
import React, { ReactText, useEffect, useState } from 'react';
import './visibleSetting.less';
import { useSelector } from 'umi';

function VisibleSetting(props: any) {
  const { t } = useLocale();
  const [updateParams, setUpdateParams] = useState<any>('private');
  const [liveVisiblePermission, setLiveVisiblePermission] = useState<string>("");
  const [settingDataList, setSettingDataList] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [addStudentVis, setAddStudentVis] = useState<boolean>(false);
  const [courseLive, setCourseLive] = useState<boolean>(false);
  const [courseLiveProps, setCourseLiveProps] = useState<any>([]);
  const [videoMark, setVideoMark] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<any>([]);
  const [settingInfo, setSettingInfo] = useState<any>({});
  const [pager, setPager] = useState<any>({
    page: 1,
    size: 10,
  });
  const { parameterConfig } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any }
  >(state => state.global);
  console.log(parameterConfig)
  const addZero = (num: any) => {
    if (num < 10) return '0' + num;
    return num;
  };
  const date = new Date(Number(props.location.query.date));
  const courseIdProps = props.location.query.courseId;
  // const serialNumberProps = props.location.query.serialNumber;
  // const courseNumProps = props.location.query.courseNum;
  // const courseNameProps = props.location.query.courseName;
  const datetime =
    date.getFullYear() +
    '-' +
    addZero(date.getMonth() + 1) +
    '-' +
    addZero(date.getDate());
  useEffect(() => {
    // if (props.location.query.authorityType === 'custom') {
    //   getQueryList('all');
    // }
    const arr = settingDataList.slice(
      pager.size * (pager.page - 1),
      pager.size * pager.page,
    );
    setDataSource(arr);
  }, [pager, settingDataList]);
  useEffect(() => {
    if (Object.keys(parameterConfig).length > 0) {
      getCourseReviewInfo();
    }

  }, [parameterConfig]);
  const getCourseReviewInfo = () => {
    queryCourseReviewSetting({
      courseId: courseIdProps,
      authority: 'custom',
    }).then((res: any) => {
      if (res.status === 200) {
        setVideoMark(res.data.watermark);
        setCourseLive(res.data.liveFlag);
        const arr = [];
        if (res.data.isVoiceIdentify) {
          arr.push('isVoiceIdentify');
        }
        if (res.data.isVoiceTranslation) {
          arr.push('isVoiceTranslation');
        }
        setCourseLiveProps(arr);
        setUpdateParams(res.data.courseAuthority);
        if (parameterConfig.target_customer === CUSTOMER_SHTECH) {
          setLiveVisiblePermission(res.data.liveAuthority ??"custom");
        }
        setSettingDataList(res.data.courseStudentPageVO);
        setTotal(res.data.courseStudentPageVO.length);
        localStorage.setItem(
          'visibleSettingUserCode',
          JSON.stringify(res.data.courseStudentPageVO),
        );
        const temp = res.data.courseStudentPageVO.slice(
          pager.size * (pager.page - 1),
          pager.size * pager.page,
        );
        setDataSource(temp);
        setSettingInfo(res.data);
      }
    });
  };
  const rowSelection = {
    onChange: (newSelectedRowKeys: ReactText[], item: any) => {
      setSelectedRowKeys(item);
    },
    getCheckboxProps: (item: any) => {
      // return {disabled:updateParams!=='custom'||item.userType===1}
      return { disabled: updateParams !== 'custom' && liveVisiblePermission !== "custom" };
    }
  };
  /**
   * 列表columns
   */
  const columns: any = [
    {
      title: t('头像'),
      dataIndex: 'photo',
      key: 'photo',
      align: 'center',
      width: 100,
      render: (text: any, record: any) => {
        if (record.avatarUrl) {
          return (
            <Avatar
              shape="square"
              size={64}
              src={record.avatarUrl}
              icon={<UserOutlined />}
            />
          );
        } else {
          return <Avatar shape="square" size={64} icon={<UserOutlined />} />;
        }
      },
    },
    {
      title: t('姓名'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('性别'),
      dataIndex: 'sex',
      key: 'sex',
      align: 'center',
    },
    {
      title: t('工号'),
      dataIndex: 'jobNumber',
      key: 'jobNumber',
      align: 'center',
    },
    {
      title: t('学院'),
      dataIndex: 'college',
      key: 'college',
      align: 'center',
    },
    {
      title: t('专业'),
      dataIndex: 'major',
      key: 'major',
      align: 'center',
    },
    {
      title: t('类型'),
      dataIndex: 'userType',
      key: 'userType',
      align: 'center',
      render: (text: any, record: any) => (
        <span title={record}>
          {text === 0 ? t('非本班同学') : t('本班同学')}
        </span>
      ),
    },
    {
      title: t("添加方式"),
      dataIndex: 'addType',
      key: 'addType',
      align: 'center',
      render: (text: number) => {
        return text === 0 ? '手动添加' : '系统添加'
      }
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (text: any, record: any) => (
        <Button
          className="action"
          disabled={updateParams !== 'custom' && liveVisiblePermission !== "custom"}
          onClick={() => {
            handleDeleteOk(record);
          }}
          title={t('删除')}
        >
          <DeleteOutlined />
        </Button>
      ),
    },
  ];

  const [total, setTotal] = useState(0);
  const removeRepeat = (arr1: any, arr2: any) => {
    const arr = [...arr1, ...arr2];
    let newArr: any = [];
    let arrId: any = [];
    for (let item of arr) {
      if (!arrId.includes(item.userCode)) {
        arrId.push(item.userCode);
        newArr.push(item);
      }
    }
    console.log('去重后', newArr);
    return newArr;
  };
  const getQueryList = () => {
    // 同步本班同学并导入
    let params = {
      courseId: courseIdProps,
      ifInsert: false,
    };
    // let syncParams = {
    //   courseId: courseIdProps,
    //   courseName: courseNameProps,
    //   courseNumber: courseNumProps,
    //   serialNumber: serialNumberProps,
    //   datetime: datetime,
    // };
    // studentsSynchronization(syncParams).then((res: any) => {
    // if (res.errorCode === 'course_0000_0000') {
    fetchClassmates(params).then((res: any) => {
      console.log(res);
      if (res.status === 200) {
        if (res.data.length === 0) {
          message.info(t('暂无本班同学'));
        } else {
          let newStudents = removeRepeat(settingDataList, res.data);
          setSettingDataList(newStudents);
          setTotal(newStudents.length);
        }
      }
    });
    // }
    // });
  };
  const updateAuthority = (item: any) => {
    console.log(item.target?.value);
    setUpdateParams(item.target?.value);
  };
  const refresh = () => {
    const arr: any = localStorage.getItem('visibleSettingUserCode');
    // setSettingDataList([...settingDataList,...JSON.parse(arr)])
    const temp = removeRepeat(settingDataList, JSON.parse(arr));
    setSettingDataList(temp);
    setTotal(temp.length);
    message.success(t('添加成功'));
  };
  const handleDeleteOk = (record: any) => {
    if (!Array.isArray(record)) {
      record = [record];
    }
    if (record.length === 0) {
      message.info(t('请至少选择一个学生！'));
      return;
    }
    Modal.confirm({
      title: t('确认删除该学生吗?'),
      icon: <ExclamationCircleOutlined />,
      content: '',
      okText: t('是'),
      okType: 'danger',
      cancelText: t('否'),
      onOk: () => {
        removeLocalStorageRecord(record);
      },
    });
  };
  const handleSave = (record: any) => {
    const defaultData = {
      liveFlag: courseLive,
      isVoiceIdentify: courseLiveProps.includes('isVoiceIdentify'),
      isVoiceTranslation: courseLiveProps.includes('isVoiceTranslation'),
      authority: updateParams,
      liveAuthority: liveVisiblePermission,
      courseId: courseIdProps,
      watermark: videoMark,
    };
    if (updateParams === 'custom' || liveVisiblePermission === "custom") {
      if (!Array.isArray(record)) {
        record = [record];
      }
      if (record.length === 0) {
        message.info(t('请至少选择一个学生！'));
        return;
      } else {
        const data = {
          ...defaultData,
          users: record.map((item: any) => ({
            userCode: item.userCode,
            userName: item.name,
            userType: item.userType,
          })),
        };
        updateAuthoritySingle(data).then((res: any) => {
          if (res.message === 'OK') {
            message.success(t('修改成功'));
            props.location.query.authorityType ? history.back() : props?.back();
          } else {
            message.error(res.message || t('修改失败'));
          }
        });
      }
    } else {
      if (updateParams === 'public') {
        Modal.confirm({
          content: t('公开则全校师生均可观看该课堂回看，是否确认公开？'),
          onOk() {
            updateAuthoritySingle({
              ...defaultData,
            }).then((res: any) => {
              if (res.message === 'OK') {
                message.success(t('修改成功'));
                props.location.query.authorityType
                  ? history.back()
                  : props?.back();
              } else {
                message.error(res.message || t('修改失败'));
              }
            });
          },
        });
        return;
      } else {
        updateAuthoritySingle({
          ...defaultData,
        }).then((res: any) => {
          if (res.message === 'OK') {
            message.success(t('修改成功'));
            props.location.query.authorityType ? history.back() : props?.back();
          } else {
            message.error(res.message || t('修改失败'));
          }
        });
      }
    }
  };
  const removeLocalStorageRecord = (record: any) => {
    const newRecord = settingDataList.filter((item: any) => {
      //非本班学生取剩下的
      // if(!record.includes(item) && item.userType === 0){
      if (!record.includes(item)) {
        return item;
      }
    });
    console.log(newRecord);
    setSettingDataList(newRecord);
    setPager({ ...pager, page: 1 });
    setTotal(newRecord.length);
    localStorage.setItem('visibleSettingUserCode', JSON.stringify(newRecord));
    message.success(t('删除成功'));
  };
  return (
    <div className="setting_container">
      <div className="setting_top">
        <div className="setting_top_left">
          <a
            onClick={() => {
              props.location.query.authorityType
                ? history.back()
                : props?.back();
            }}
          >
            <LeftCircleFilled />
            <span className='back-text'>{t('返回上一级')}{' '}</span>
          </a>
        </div>
        <Button
          onClick={() => handleSave(settingDataList)}
          type="primary"
          className="handleSstting"
        >
          {t('保存设置')}
        </Button>
      </div>
      <div className="setting-content-container">
        <div className="setting-item">
          <span>
            {t('课程直播')}
            <Tooltip
              title={`${t('开启则学生在上课时间可直接通过')}${t('在线课堂')}${t(
                '观看课程直播',
              )}`}
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
          <Switch
            checked={courseLive}
            onChange={(checked: boolean) => setCourseLive(checked)}
          ></Switch>
          &nbsp;{courseLive ? t('开启') : t('关闭')}
          {courseLive && (
            <Checkbox.Group
              value={courseLiveProps}
              onChange={(checked: any) => setCourseLiveProps(checked)}
            >
              { parameterConfig.online_course_asr_switch == 'true' &&    <Checkbox value="isVoiceIdentify">{t('语音识别')}</Checkbox> }
              { parameterConfig.online_course_vtv_switch == 'true' &&
              <Checkbox value="isVoiceTranslation">{t('翻译转写')}</Checkbox>
              }
            </Checkbox.Group>
          )}
        </div>
        <div className="setting-item">
          <span>
            {t('视频水印')}
            <Tooltip
              title={t(
                '开启则学生在观看视频时，视频上会显示学生的姓名、学号水印，以防录屏',
              )}
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </span>
          <Switch
            checked={videoMark}
            onChange={(checked: boolean) => setVideoMark(checked)}
          ></Switch>
          &nbsp;{videoMark ? t('开启') : t('关闭')}
        </div>
        <div className="setting-item">
          <span>{t('公开画面设置')}</span>
          <a
            href={`/learn/videoreview/${courseIdProps}?schoolYear=${settingInfo.schoolYear}&semester=${settingInfo.semester}&edit=true`}
          >
            {t('前往设置')}
          </a>
        </div>
        <div className='setting-item'>
          <span>{t("谁可以看")}</span>
          <div>
            <div style={{ marginBottom: "10px" }}>
              <span>课程直播：</span>
              <Radio.Group
                onChange={(e: any) => setLiveVisiblePermission(e.target.value)}
                value={liveVisiblePermission}>
                <Radio value="public">{t("公开（全校师生可见）")}</Radio>
                <Tooltip title={"课程直播暂不支持设置为仅自己可见"}>
                  <Radio value="private" disabled>{t("私有（仅自己可见）")}</Radio>
                </Tooltip>
                <Radio value="custom">{t("部分可见")}</Radio>
              </Radio.Group>
            </div>
            <div>
              <span>课堂回看：</span>
              <Radio.Group
              onChange={updateAuthority}
              value={updateParams}>

              <Radio value="public">{t("公开（全校师生可见）")}</Radio>
              {/* 云大隐藏私有选项 */}
              {parameterConfig.target_customer != CUSTOMER_YNU && <Radio value="private">{t("私有（仅自己可见）")}</Radio>}
              <Radio value="custom">{t(parameterConfig.target_customer == CUSTOMER_YNU? "班级可见": "部分可见")}</Radio>
            </Radio.Group>
            </div>
          </div>

        </div>
      </div>
      <div>
        <div className="setting_btn">
          <Button
            type="primary"
            className="classmates"
            shape="round"
            onClick={() => getQueryList()}
            disabled={updateParams !== 'custom' && liveVisiblePermission !== "custom"}>
            {t("添加本班同学")}

          </Button>
          <Button
            shape="round"
            onClick={() => setAddStudentVis(true)}
            disabled={updateParams !== 'custom' && liveVisiblePermission !== "custom"}>
            {t("手动添加")}

          </Button>
          <Button
            shape='round'
            onClick={() => handleDeleteOk(selectedRowKeys)}
            disabled={updateParams !== 'custom' && liveVisiblePermission !== "custom"}>
            {t("批量删除")}

          </Button>
        </div>

        <Table
          size="middle"
          dataSource={liveVisiblePermission === "custom" || updateParams === 'custom' ? dataSource : []}
          columns={columns}
          rowKey="userCode"
          pagination={updateParams === 'custom' || liveVisiblePermission === "custom" ? {
            total: total,
            pageSize: pager.size,
            current: pager.page,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total: number) => `共${total}条`,
            onChange: (page, pageSize) => {
              setPager({
                page: page,
                size: pageSize ?? 0
              });
            }
          }: false}
          rowSelection={{
            ...rowSelection as any,
          }}
          scroll={{ y: 'calc(100vh - 500px)' }}
        />

        {/* <div className='footerContainer'>
           <Button
             onClick={() => handleSave(settingDataList)}
             type='primary'
             className='handleSstting'
           >
             保存设置
           </Button>
          </div> */}
      </div>

      <AddStudentModal
        modalVisible={addStudentVis}
        modalClose={() => setAddStudentVis(false)}
        refresh={refresh}
        specialFlag={true}
      />
    </div>
  );
}

export default VisibleSetting;
