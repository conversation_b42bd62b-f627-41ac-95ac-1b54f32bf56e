import React, { FC, useRef, useState, useEffect } from 'react';
import { Modal, Upload, Button, message, Select } from 'antd';
import Icon, { DeleteOutlined } from '@ant-design/icons';
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import chapterApis from "@/api/chapter";
import { downloadtemplate, unlockmap, autoRecognizeKnowledge, importDemoData } from '@/api/coursemap';
import { useLocation, useSelector } from "umi";
import "./index.less";
import ResourceModal from '../../../components/ResourceModal';
import ResourcePreviewModal from '../../../components/ResourcePreviewModal';
import { getTreebylevel } from '@/api/addCourse';
import useLocale from '@/hooks/useLocale';
import UploadFile from '../components/Uploadfile';
const { Dragger } = Upload;
const { confirm } = Modal;
interface IWordImport {
  visible: number;
  onClose: () => void;
  onSuccess: () => void;
  perviewtype: number;
  mapid: any;
  courseid: string;
  coursename: string;
}

const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;

const UploadModal: FC<IWordImport> = ({ visible, onClose, onSuccess, perviewtype, mapid, courseid, coursename }) => {
  const { t } = useLocale();
  const [fileData, setFileData] = useState<any>(null);
  const [waitingTimer, setWaitingTimer] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(5);
  const [isLocalUpload, setIsLocalUpload] = useState<boolean>(false);
  //  文件后缀
  const [fileType, setFileType] = useState<string>("");
  const { query }: any = useLocation();
  const [loading, setLoading] = useState<boolean>(false);
  const [modalLoading, setModalLoading] = useState<boolean>(false); //选择资源弹窗控制

  //   选择资源弹窗的开关
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  // 选择资源弹窗的数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]);

  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);
  const [generationmode, setGenerationmode] = useState<string>('1');

  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
  );

  const props = {
    name: 'file',
    showUploadList: false,
    beforeUpload(file: any) {
      const nameArr = file.name.split('.');
      setFileData(file);
      setFileType(nameArr[nameArr.length - 1]);
      setIsLocalUpload(true);
      return false;
    },
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    }
  };


  const handleImport = () => {
    confirm({
      title: t(`此操作会覆盖当前编辑的内容，是否确定？`),
      onOk() {
        setLoading(true);
        autoRecognizeKnowledge(fileData.contentId, mapid, generationmode).then((res: any) => {
          if (res.success) {
            message.success(t("发起转换流程成功!"));
            handleClose();
            setLoading(false);
            unlockmap({
              mapId: mapid,
              type: 0
            }).then((res) => {
              console.log(res);
              onSuccess();
            });
          } else {
            message.error(t("发起转换流程失败!"));
            setLoading(false);
          }
        }).catch((err: any) => {
          console.log(err);
          setLoading(false);
        });
      },
      onCancel() { }
    });
  };

  const handleClose = () => {
    setFileData(null);
    onClose();
  };
  const handleDelete = () => {
    setFileData(null);
  };

  const handleDownload = (e: any, type: number) => {
    e.stopPropagation();
    downloadtemplate(type).then((res) => {
      const blobURL = window.URL.createObjectURL(res);
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a');
      tempLink.style.display = 'none';
      tempLink.href = blobURL;
      if (type === 0) {
        tempLink.setAttribute('download', '图谱导入模板.xls');
      } else {
        tempLink.setAttribute('download', '图谱导入模板.doc');
      }
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank');
      }
      // 挂载a标签
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      // 释放blob URL地址
      window.URL.revokeObjectURL(blobURL);
    });
  };

  //显示Modal
  const showModal = () => {
    setModalLoading(true);
    const getTreeData = () => {
      getTreebylevel().then((res) => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalLoading(false);
            setModalVisible(true);
          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children ?
            forTree(item.children, [...parentsKeys, item.code]) :
            []
        };
      });
    };
    getTreeData();
  };

  useEffect(() => {
    if (fileData && isLocalUpload) {
      setWaitingTimer(true);
      setCountdown(5);
      const timer = setInterval(() => {
        setCountdown((prevCount) => {
          if (prevCount <= 1) {
            clearInterval(timer);
            setWaitingTimer(false);
            return 0;
          }
          return prevCount - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [fileData, isLocalUpload]);

  return <Modal title={t("按课程资源生成图谱")} open={visible == 10} footer={null} width={550} onCancel={handleClose}>
    <div className="import-map-chapter">
      {fileData?.name ? null :
        <div className="title">
          <div>
            <span>{t("资源：")}</span>
            <Button loading={modalLoading} onClick={() => showModal()}>{t("从资源库选择")}</Button>
          </div>
          <div>
            <span>{t("没有需要的资源?")}</span>
            {/* 用新标签打开 */}
            <a target="_blank" href='/rman/#/basic/rmanCenterList'>{t("前往资源库上传")}</a>
          </div>
        </div>}
      {fileData?.name ? null : <div className='tipbox'>
        <span className='tip'>{t("（目前仅支持PDF格式）")}</span>
      </div>}
      {fileData?.name ? <div className='file-name'><span>选择文件：</span>{fileData?.name}<DeleteOutlined onClick={handleDelete} /></div> : null}
      {/* {
        fileData?.name ? <>
          <span>转换模式：</span>
          <Select
            defaultValue="auto"
            value={generationmode}
            style={{ width: 120 }}
            onChange={(e) => setGenerationmode(e)}
            options={[
              {
                value: '1',
                label: t("分类节点")
              },
              {
                value: '2',
                label: t("知识节点")
              }]} />
          <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)', marginTop: '10px' }}>{t('分类节点')}：{t("文件中的节点全部转换为分类节点")}</p>
          <p style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)' }}>{t('知识节点')}：{t("文件中的节点全部转换为知识节点")}</p>
        </> : null
      } */}
      {(fileData == null || fileData.length == 0) && <UploadFile
        onlyVideo={false}
        formData={null}
        mapid={mapid}
        mapname={mapinfo.mapName}
        courseid={courseid}
        coursename={coursename}
        onSave={(e: any) => {
          if (e && e.length > 0) {
            setFileData({
              ...e[0],
              contentId: e[0].resourseId
            });
            setIsLocalUpload(true);
            setModalVisible(false);
          }
        }}
        showRecommend={false}
        isMultiple={false}
        accept=".pdf"
      />}
      <div className="btn-group">
        <Button
          type="primary"
          loading={loading}
          disabled={!fileData || waitingTimer}
          onClick={handleImport}
        >
          {waitingTimer ? `${t("入库中，请稍等")}${countdown}${t("秒")}` : t("确认")}
        </Button>
      </div>
    </div>
    <ResourceModal
      // currentname={''}
      treeData={modalTreeData}
      visible={modalVisible}
      onConfirm={(e: any) => {
        if (e.length) {
          setFileData(e[0]);
          setIsLocalUpload(false);
          setModalVisible(false);
        }
      }}
      onCancel={() => setModalVisible(false)}
      onShowDetail={(id, detail) => {
        setEntityPreview({
          id: id,
          name: detail.name,
          type: detail.type
        });
        setEntityModalVisible(true);
      }}
      fileType={['biz_sobey_document']}
      multi={true}
      isconvert={true} />

    {/* 资源预览modal */}
    <ResourcePreviewModal
      modalVisible={entityModalVisible}
      modalClose={() => setEntityModalVisible(false)}
      resource={entityPreview} />

  </Modal>;
};

export default UploadModal;
