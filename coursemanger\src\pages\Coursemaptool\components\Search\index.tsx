import React, { useEffect, useState } from 'react';
import './index.less';
import { Button, Form, FormInstance, Input, Select, TreeSelect } from 'antd';
import { IconFont } from '@/components/iconFont';
import { ReloadOutlined } from '@ant-design/icons';
import TeacherSelect from '@/components/formItemBox/teacherSelect';
import useLocale from '@/hooks/useLocale';
import { queryColleges } from '@/api/course';
import { useSelector } from 'umi';
import SchoolSelect from '@/components/formItemBox/SchoolSelect';
const Option = Select.Option;
const Search: React.FC<SearchProps> = ({
  isSuper=false,
  form,
  onSearch,
  onUpRole,
  mobileFlag,
  page = 'marjor'
}) => {
  const [moreSearch, setMoreSearch] = useState<boolean>(false);
  const { t } = useLocale();
  // 专业
  const [major, setMajor] = useState<any>([]);
  const [courseSubjectList, setcourseSubjectList] = useState<any>([]);
  const [supermode, setSupermode] = useState<boolean>(isSuper);
  useEffect(()=>{
    queryCourseSearch();
  },[])

  const reset = () => {
    form.resetFields();
    onSearch?.(form.getFieldsValue());
  };

  // 课程分类
  const queryCourseSearch = () => {
    queryColleges().then((res) => {
      if (res.status === 200) {
        setcourseSubjectList(res.data?.subject ?? []);
        setMajor(setTreeData(res.data?.subjectEducation) ?? []);
      }
    });
  };

  const setTreeData = (root: any[], parentName: any = null, parentCode: any = null): any =>
    root.map((item) => {
      if (item.children && item.children.length > 0) {
        return {
          title: item.categoryName,
          value: item.categoryCode,
          key: item.categoryCode,
          parentName: parentName,
          parentCode: parentCode,
          children: setTreeData(item.children, item.categoryName, item.categoryCode)
        };
      } else {
        return {
          value: item.categoryCode,
          title: item.categoryName,
          key: item.categoryCode,
          parentName: parentName,
          parentCode: parentCode

        };
      }
    });

  const onProfessionChange = (value: any, label: any, extra: any, fieldName: any) => {
    console.log(value, label, extra, fieldName);
    if (fieldName === 'major') {
      form.setFieldsValue({
        major: value
      });
    }
  };

  return (
    <div className="map-search">
      {mobileFlag ?
        <Form
          name="basic"
          form={form}
          layout="inline"
          // onFieldsChange={(values: any) => setSearchfrom(values)}
          onFinish={onSearch}
          style={{ width: '100%', height: '100%' }}>

          <div className="pubilc_form">
            {/* <Button className='back' onClick={goBack}>返回</Button> */}
            {
              page == 'marjor' ?
                <TeacherSelect
                  message={t("请选择专业")}
                  type={2}
                  name="marjorId"
                  label=""
                  multiple={false} /> :

                <Form.Item name="search_name" noStyle>
                  <Input
                    placeholder={(()=>{
                      if(page == 'micromajor'){
                        return t("输入微专业名称")
                      }else if(page == 'module'){
                        return t("输入教学模块名称")
                      }else{
                        return t("输入地图名称")
                      }
                    })()}
                    allowClear
                    autoComplete={'off'}
                    addonAfter={<IconFont onClick={() => form.submit()} type="iconsousuo2" />}
                    onPressEnter={() => form.submit()} />

                </Form.Item>}

            {
              <>
                <div className={moreSearch ? 'ant-modal-mask' : ''}></div>
                {
                  <div className={`moreSearch${moreSearch ? '' : ' hide'}`}>
                    <div className="head">
                      <span>{t("更多筛选")}</span>
                      <IconFont
                        type="iconguanbi2"
                        onClick={() => setMoreSearch(false)} />

                    </div>
                    <div className="body">
                      {
                        page == 'marjor' &&
                        <TeacherSelect
                          message={t("请选择学院")}
                          type={1}
                          name="school"
                          label=""
                          multiple={false} />}



                      {(page == 'mine' || page == 'module') &&
                        <>
                          <Form.Item name="search_status">
                            <Select
                              style={{ width: 180 }}
                              allowClear
                              placeholder={t("请选择状态")}>

                              <Option value="1">{t("未共享")}</Option>
                              <Option value="3">{t("已共享")}</Option>
                            </Select>
                          </Form.Item>
                        </>}


                      {
                        page == 'share' &&
                        <>
                          {/* <TeacherSelect
                            message={t("请选择专业")}
                            type={2}
                            name="major"
                            label=""
                            multiple={false} /> */}
                          <Form.Item
                              label={t("学科专业")}
                              name="major"
                            >
                             <TreeSelect
                                treeData={major}
                                onChange={(value: any, label: any, extra: any) => {
                                  onProfessionChange(value, label, extra, 'major');
                                }}
                                treeCheckable={courseSubjectList?.isMultiSelect}
                                allowClear={true}
                                treeNodeFilterProp="title"
                                placeholder={t(`请选择学科专业`)}
                                // defaultValue={[]}
                                getPopupContainer={(triggerNode) =>
                                triggerNode.parentNode}
                              />
                            </Form.Item>

                          <TeacherSelect
                            message={t("请选择教师")}
                            type={0}
                            name="teacher"
                            label=""
                            multiple={false} />

                        </>}


                    </div>
                    <div className="btns">
                      <Button onClick={reset}>{t("重置")}</Button>
                      <Button
                        type="primary"
                        htmlType="submit"
                        onClick={() => {
                          setMoreSearch(false);
                        }}>
                        {t("确认")}

                      </Button>
                    </div>
                  </div>}

                <Button
                  onClick={() => setMoreSearch(true)}
                  icon={<IconFont type="iconshaixuan" />}>
                  {t("筛选")}

                </Button>
              </>}

          </div>
        </Form> :

        <Form
          name="basic"
          form={form}
          layout="inline"
          // onFieldsChange={(values: any) => setSearchfrom(values)}
          onFinish={onSearch}
          style={{ width: '100%', height: '100%' }}>

          {
            page == 'marjor'&&
              <Form.Item name="marjor_name" initialValue={null}>
                <Input placeholder="请输入名称" allowClear />
              </Form.Item>
          }

          {
            page == 'marjor' ?
            <SchoolSelect
              message={t("请选择学院或专业")}
              name="marjorId"
              label=""
              multiple={false}
            />
              // <TeacherSelect
              //   message={t("请选择专业")}
              //   type={2}
              //   name="marjorId"
              //   label=""
              //   multiple={false} />
                :
              <Form.Item name="search_name" initialValue={null}>
                <Input style={{ width: 200 }} placeholder={(()=>{
                  if(page == 'micromajor'){
                    return t("输入微专业名称")
                  }else if(page == 'module'){
                    return t("输入教学模块名称")
                  }else{
                    return t("输入地图名称")
                  }
                })()} allowClear />
              </Form.Item>}

          {
          page == 'micromajor' &&
            <TeacherSelect
              message={t("请选择学院")}
              type={1}
              name="school"
              label=""
              multiple={false} />
          }


          {(page == 'mine' || page == 'module' )&&
            <Form.Item name="search_status" initialValue={null}>
              <Select
                style={{ width: 200 }}
                placeholder={t("请选择状态")}
                allowClear>

                <Option value="1">{t("未共享")}</Option>
                <Option value="3">{t("已共享")}</Option>
              </Select>
            </Form.Item>}


          {page == 'share' &&
            <>
              {/* <TeacherSelect
                message={t("请选择专业")}
                type={2}
                name="major"
                label=""
                multiple={false} /> */}
              <Form.Item
                label={t("学科专业")}
                name="major"
              >
                <TreeSelect style={{width:'180px'}}
                  treeData={major}
                  onChange={(value: any, label: any, extra: any) => {
                    onProfessionChange(value, label, extra, 'major');
                  }}
                  treeCheckable={courseSubjectList?.isMultiSelect}
                  allowClear={true}
                  treeNodeFilterProp="title"
                  placeholder={t(`请选择学科专业`)}
                  // defaultValue={[]}
                  getPopupContainer={(triggerNode) =>
                  triggerNode.parentNode}
                />
              </Form.Item>

              <TeacherSelect
                message={t("请选择教师")}
                type={0}
                name="teacher"
                label=""
                multiple={false} />

            </>}


          <Form.Item>
            <div className="reset-wrp" onClick={reset}>
              <span>{t("清空")}</span>
              <ReloadOutlined />
            </div>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">{t("搜索")}

              <IconFont type="iconsousuo2" />
            </Button>
          </Form.Item>
        </Form>}
        {isSuper && <Button
          type="primary"
          shape="round"
          ghost
          onClick={()=>{
            setSupermode(!supermode);
            onUpRole && onUpRole(!supermode);
          }} >
          {supermode ? t('切换个人模式') : t('切换管理员模式')}
        </Button>}
    </div>);

};

interface SearchProps {
  isSuper?:boolean;
  form: FormInstance<any>;
  onSearch: (values: any) => void;
  onUpRole?: (values: any) => void;
  mobileFlag: boolean;
  page: 'marjor' | 'mine' | 'share' | 'micromajor' | 'module' | 'recyclebin';
}
export default Search;
Search.displayName = 'Search';
