export default [
  {
    path: '/',
    component: '@/layout/guideLayout/index',
    routes: [
      {
        path: '/',
        redirect: '/course',
      },
      {
        path: '/coursemap',
        // component: '@/pages/Coursemaptool/Teacherpreview',
        component: '@/pages/Coursemaptool/Mapv4/perviewmap',
      },
      {
        path: '/syllabus',
        component: '@/pages/SyllabusDisplay'
      },
      {
        path: '/map3d',
        component: '@/pages/Coursemaptool/3Dmap',
      },
      {
        path: '/offlinesignin',
        component: '@/pages/OfflineSignin',
      },
      {
        path: '/perviewemap',
        title: '知识地图',
        component: '@/pages/Coursemaptool/Perviewmap',
      },
      {
        path: '/perviewemap2',
        title: '知识地图',
        component: '@/pages/Perviewmap2',
      },
      {
        path: '/mapv3',
        // component: '@/pages/NewCourseMap',
        component: '@/pages/Coursemaptool/Mapv3',
      },
      {
        path: '/othereditmap',
        // component: '@/pages/NewCourseMap',
        component: '@/pages/Coursemaptool/Othereditmap',
      },
      {
        path: '/homework',
        component: '@/pages/HomeworkManagement/StudentHomeworkManagement',
      },
      {
        path: '/coursepractice',
        component: '@/pages/CoursePractice',
        title: '课程'
      },
      {
        path: '/courseData',
        component: '@/pages/CourseData',
        title: '课程'
      },
      {
        path: '/home',
        component: '@/pages/HomePage',
        title: '主页',
      },
      {
        path: '/newcoursemap',
        component: '@/pages/Coursemaptool/Teacherpreview',
      },
      {
        path: '/stuTextbook',
        component: '@/pages/TeachingAssistant/StudentTextBook',
      },
      {
        path: '/agent',
        component: '@/pages/Agent',
        title: 'AI Agent',
      },
      {
        path: '/myQuestionnaire',
        component: '@/pages/MyQuestionnaire',
      },
      {
        path: '/mapCourse',
        component: '@/pages/MapCourse',
        title: '图谱课-canvas',
      },
      {
        path: '/canvasmap',
        component: '@/layout/canvasLayout',
        title: 'Canvas图谱',
        routes: [
          {
            path: '/canvasmap',
            redirect: '/canvasmap/mapdetail',
          },
          {
            title: '图谱课详情',
            path: '/canvasmap/mapdetail',
            component: '@/pages/Coursemaptool/Mapv4/perviewmap',
          },
          {
            path: '/canvasmap/studentmap',
            component: '@/pages/MapCourse/components/StudentMap',
            title: '图谱课详情',
          },
          {
            path: '/canvasmap/courseqa',
            component: '@/pages/CourseQA',
            title: '问学',
          },
          {
            path: '/canvasmap/courseqa/detail',
            component: '@/pages/CourseQA/detail',
            title: '问学详情',
          },
          {
            path: '/canvasmap/statistics',
            component: '@/pages/templateDeatil/Studentstatistics',
            title: '图谱课统计',
          },
          {
            name: '统计',
            path: '/canvasmap/mapstatistics',
            component:'@/pages/templateDeatil/Newstatistics/atlasStatistics.tsx',
            title: '课程',
          },{
            name: '设置',
            path: '/canvasmap/setting',
            component:'@/pages/Coursemaptool/components/Setting',
            title: '课程',
          },
        ]
      },
      // 830版本知识图谱
      // {
      //   path: '/newcoursemap2',
      //   component: '@/pages/NewCourseMap',
      // },
      // {
      //   title: '客户信息收集表',
      //   exact: true,
      //   path: '/customer/list',
      //   component: '@/pages/CustomerInfoSheet',
      // },
      //大屏图谱，演示用
      {
        title: '大屏图谱',
        path: '/screen/map',
        component: '@/pages/Coursemaptool/Screen',
      },
      {
        title: '知识点图谱',
        path: '/knowledge/map',
        component: '@/pages/Coursemaptool/KnowledgeMap',
      },
      {
        title: '个人学习统计',
        path: '/personalstatistic',
        component: '@/pages/StudentPersonalStatistic',
      },
      {
        title: '机构管理',
        path: '/organization',
        routes: [
          {
            path: '/organization',
            redirect: '/organization/list',
          },
          {
            title: '列表',
            path: '/organization/list',
            component: '@/pages/OrganizationManagement/OrganizationList',
          },
          {
            title: '管理',
            path: '/organization/management',
            component: '@/pages/OrganizationManagement/Management',
          },
          {
            title: '充值',
            path: '/organization/recharge',
            component: '@/pages/OrganizationManagement/Recharge',
          },
        ],
      },
      {
        path: '/editcourse',
        redirect: '/pages/CourseSetting',
      },
      {
        path: '/editcourse',
        component: '@/layout/baseLayout',
        routes: [
          {
            path: '/editcourse/cultureprogram',
            component: '@/pages/CultureProgram',
            title: '课程',
          },
          {
            path: '/editcourse/learningsettings',
            component: '@/pages/CourseSetting',
            title: '课程',
          },
          {
            path: '/editcourse/mooclearningsettings',
            component: '@/pages/CourseSetting',
            title: '课程',
          },
          {
            path: '/editcourse/teachingAssistant',
            component: '@/pages/TeachingAssistant',
            title: '课程',
          },
          {
            path: '/editcourse/moocbaseinfo',
            component: '@/pages/CourseSetting',
            title: '课程',
          },
          {
            path: '/editcourse/studentlist',
            component: '@/pages/Studentlist',
            title: '课程',
          },
          {
            path: '/editcourse/moocteachteam',
            component: '@/pages/CourseSetting',
            title: '课程',
          },
          {
            path: '/editcourse/moochomework',
            component: '@/pages/HomeworkManagement',
            title: '课程',
          },
          // 统计
          {
            name: '统计',
            path: '/editcourse/moocstatistics',
            component: '@/pages/templateDeatil/Newstatistics',
            title: '课程',
          },
          {
            path: '/editcourse/homework',
            component: '@/pages/HomeworkManagement',
            title: '课程',
          },
          {
            path: '/editcourse/micromajor/homework',
            component: '@/pages/Micromajor/homework',
            title: '课程',
          },
          {
            name: '统计',
            path: '/editcourse/statistics',
            component: '@/pages/templateDeatil/Newstatistics',
            title: '课程',
          },
          {
            name: '统计',
            path: '/editcourse/mapstatistics',
            component:
              '@/pages/templateDeatil/Newstatistics/atlasStatistics.tsx',
            title: '课程',
          },
          {
            name: '统计',
            path: '/editcourse/completion',
            component: '@/pages/templateDeatil/Completion',
            title: '课程',
          },
          {
            name: '统计',
            path: '/editcourse/studentStatistics',
            component: '@/pages/templateDeatil/Studentstatistics',
            title: '课程',
          },
          {
            name: '统计',
            path: '/editcourse/mooccompletion',
            component: '@/pages/templateDeatil/Completion',
            title: '课程',
          },
          {
            name: '统计',
            path: '/editcourse/completion/detail',
            component: '@/pages/templateDeatil/Detail',
            title: '课程',
          },
          {
            path: '/editcourse/baseInfo',
            component: '@/pages/CourseSetting',
            title: '课程',
          },
          {
            path: '/editcourse/operation',
            component: '@/pages/CourseOperation',
            title: '课程',
          },
          {
            path: '/editcourse/moocoperation',
            component: '@/pages/CourseOperation',
            title: '课程',
          },
          {
            path: '/editcourse/moocChapter',
            component: '@/pages/Chapter',
            title: '课程',
          },
          {
            path: '/editcourse/moocdata',
            component: '@/pages/CourseData',
            title: '课程'
          },
          {
            path: '/editcourse/moocpractice',
            component: '@/pages/CoursePractice',
            title: '课程',
          },
          // 章节内容
          {
            path: '/editcourse/chapter',
            component: '@/pages/Chapter',
            title: '课程',
          },
          // 课堂回看
          {
            path: '/editcourse/spocdata',
            component: '@/pages/CourseData',
            title: '课程',
          },
          {
            path: '/editcourse/spocpractice',
            component: '@/pages/CoursePractice',
            title: '课程',
          },
          // 课堂回看
          {
            path: '/editcourse/coursereview',
            component: '@/pages/CourseReview',
            title: '课程',
          },
          // 问答
          {
            path: '/editcourse/courseqa',
            component: '@/pages/CourseQA',
            title: '课程',
          },
          // 问答
          {
            path: '/editcourse/mooccourseqa',
            component: '@/pages/CourseQA',
            title: '课程',
          },
          // 问答详情
          {
            path: '/editcourse/courseqa/detail',
            component: '@/pages/CourseQA/detail',
            title: '课程',
          },
          {
            path: '/editcourse/mooccourseqa/detail',
            component: '@/pages/CourseQA/detail',
            title: '课程',
          },
          {
            path: '/editcourse/teachingteam',
            component: '@/pages/CourseSetting',
            title: '课程',
          },
          {
            path: '/editcourse/studentmanagement',
            component: '@/pages/StudentManagement',
            title: '课程',
          },
          {
            path: '/editcourse/grade',
            component: '@/pages/Grade',
            title: '课程',
          },
          {
            path: '/editcourse/grademanagement',
            component: '@/pages/GradeManagement',
            title: '课程',
          },
          {
            path: '/editcourse/coursenotice',
            component: '@/pages/CourseNotice',
            title: '课程',
          },
          {
            path: '/editcourse/mooccoursenotice',
            component: '@/pages/CourseNotice',
            title: '课程',
          },
          // 问卷
          {
            path: '/editcourse/questionnaireManage',
            component: '@/pages/QuestionnaireManage',
            title: '课程',
          },
          // 互动
          {
            path: '/editcourse/interactive',
            component: '@/pages/Interactive/List',
            title: '课程',
          },
          // 互动统计--抢答和选人
          {
            path: '/editcourse/statisticspeople',
            component: '@/pages/Interactive/StatisticsPeople',
            title: '课程',
          },
          // 互动统计--题目
          {
            path: '/editcourse/statisticssubject',
            component: '@/pages/Interactive/StatisticsSubject',
            title: '课程',
          },
          {
            path: '/editcourse/moocmap',
            // component: '@/pages/Coursemaptool/Teacherpreview',
            component: '@/pages/Coursemaptool/Mapv4/perviewmap',
            title: '课程',
          },
          {
            path: '/editcourse/spocmap',
            // component: '@/pages/Coursemaptool/Teacherpreview',
            component: '@/pages/Coursemaptool/Mapv4/perviewmap',
            title: '课程',
          },
          {
            path: '/editcourse/coursemap',
            // component: '@/pages/Coursemaptool/Teacherpreview',
            component: '@/pages/Coursemaptool/Mapv4/perviewmap',
            title: '课程',
          },
          {
            path: '/editcourse/newcoursemap',
            // component: '@/pages/Coursemaptool/Teacherpreview',
            component: '@/pages/Coursemaptool/Mapv4/perviewmap',
            title: '课程',
          },
          {
            name: '微专业能力达成',
            path: '/editcourse/micromajorachievement',
            component: '@/pages/templateDeatil/MicroMajorAchievement',
            title: '微专业能力达成',
          },
          {
            name: '学生达成度详情',
            path: '/editcourse/studentachievement',
            component: '@/pages/templateDeatil/StudentAchievementDetail',
            title: '课程',
          },
          // 830版本知识图谱
          // {
          //   path: '/editcourse/coursemap2',
          //   component: '@/pages/NewCourseMap',
          //   title: "课程"
          // },
          // {
          //   path: '/editcourse/newcoursemap2',
          //   component: '@/pages/NewCourseMap',
          //   title: "课程"
          // },
        ],
      },
      {
        path: '/mapdetail',
        component: '@/layout/mapdetailLayout/index',
        routes: [
          {
            exact: true,
            path: '/mapdetail',
            component: '@/pages/Coursemaptool/Mapinfo',
            title: '课程地图',
          },
          {
            path: '/mapdetail/teachingteam',
            component: '@/pages/Coursemaptool/TeachingTeam',
            title: '课程地图',
          },
          // {
          //   path: '/mapdetail/editmap',
          //   component: '@/pages/Coursemaptool/Editmap',
          //   title: '课程地图',
          // },
        ],
      },
      {
        path: '/mapv4',
        component: '@/pages/Coursemaptool/Mapv4',
        title: '图谱课',
      },
      {
        path: '/atlasmap',
        component: '@/layout/atlasLayout/index',
        routes: [
          // {
          //   exact: true,
          //   path: '/atlasmap/mapv4',
          //   component: '@/pages/Coursemaptool/Mapv4',
          //   title: '图谱课',
          // },
          {
            path: '/atlasmap/mapdetail',
            component: '@/pages/Coursemaptool/Mapv4/mapdetail',
            title: '图谱课详情',
          },
          {
            path: '/atlasmap/announcement',
            component: '@/pages/Coursemaptool/Mapv4/announcement',
            title: '图谱课公告',
          },
          {
            path: '/atlasmap/courseqa',
            component: '@/pages/CourseQA',
            title: '图谱课问答',
          },
          {
            path: '/atlasmap/statistics',
            component: '@/pages/templateDeatil/Studentstatistics',
            title: '图谱课统计',
          },
          {
            path: '/atlasmap/teaching',
            component: '@/pages/SyllabusDisplay',
            title: '图谱课教学大纲',
          },
          {
            path: '/atlasmap/homework',
            component: '@/pages/HomeworkManagement/StudentHomeworkManagement',
            title: '图谱课作业',
          },
        ],
      },
      {
        path: '/microhome',
        component: '@/pages/Coursemaptool/microprofessional',
        title: '微专业',
      },
      {
        path: '/microprofessional',
        component: '@/layout/microprofessionaLayout/index',
        routes: [
          {
            path: '/microprofessional/mapdetail',
            component: '@/pages/Coursemaptool/Mapv4/mapdetail',
            title: '微专业详情',
          },
          {
            path: '/microprofessional/announcement',
            component: '@/pages/Coursemaptool/Mapv4/announcement',
            title: '微专业公告',
          },
          {
            path: '/microprofessional/courseqa',
            component: '@/pages/CourseQA',
            title: '微专业问答',
          },
          {
            path: '/microprofessional/offlineqa',
            component: '@/pages/Coursemaptool/microprofessional/components/OfflineQA',
            title: '微专业线下答疑',
          },
          {
            path: '/microprofessional/statistics',
            component: '@/pages/Micromajor/StudentAchievement',
            title: '微专业能力达成',
          },
          {
            path: '/microprofessional/teaching',
            component: '@/pages/SyllabusDisplay',
            title: '微专业教学大纲',
          },
          {
            path: '/microprofessional/homework',
            component: '@/pages/HomeworkManagement/StudentHomeworkManagement',
            title: '微专业作业',
          },
        ],
      },
      {
        path: '/capabilitymap',
        component: '@/pages/Coursemaptool/Mapv3/components/CapabilityGraph',
        title: '能力图谱',
      },
      {
        path: '/mapv4',
        component: '@/layout/mapv4Layout/index',
        routes: [
          {
            exact: true,
            path: '/mapv4/editmap',
            component: '@/pages/Coursemaptool/Mapv4/components/Editmap',
            title: '课程地图',
          },
          {
            exact: true,
            path: '/mapv4/problemap',
            component: '@/pages/Coursemaptool/Mapv4/components/Problemap',
            title: '课程地图',
          },
          {
            path: '/mapv4/teachingteam',
            component: '@/pages/Coursemaptool/TeachingTeam',
            title: '权限管理',
          },
          {
            path: '/mapv4/mapdetail',
            component: '@/pages/Coursemaptool/Mapinfo',
            title: '基本信息',
          },
          {
            path: 'bind',
            component: '@/pages/Coursemaptool/Bind',
            title: '绑定课程',
          },
        ],
      },
      {
        path: '/outcourse',
        component: '@/layout/outcrouselayout/index',
        routes: [
          {
            exact: true,
            path: '/outcourse',
            component: '@/pages/MicroList',
            title: '微课管理',
          },
          {
            path: '/outcourse/resource',
            component: '@/pages/AddCourse/AddCourse',
            title: '课程编辑',
          },
          {
            // exact: true,
            path: '/outcourse/mooccourse',
            component: '@/pages/MoocTabs',
            title: '公开课管理',
          },
          {
            path: '/outcourse/spoccourse',
            component: '@/pages/SpocList',
            title: '班级课管理',
          },
          {
            path: '/outcourse/dockcourse/:type',
            component: '@/pages/DockCourse',
            title: '爱课堂',
          },
          {
            path: '/outcourse/schoolMooc',
            component: '@/pages/DockCourse',
            title: '中国大学MOOC',
          },
          {
            path: '/outcourse/rainCourse',
            component: '@/pages/DockCourse',
            title: '雨课堂',
          },
          {
            path: '/outcourse/superstarCourse',
            component: '@/pages/DockCourse',
            title: '超星',
          },
          {
            path: '/outcourse/pmphmoocCourse',
            component: '@/pages/DockCourse',
            title: '人卫慕课',
          },
          {
            path: '/outcourse/umoocCourse',
            component: '@/pages/DockCourse',
            title: '优慕课',
          },
          {
            path: '/outcourse/zhihuishuCourse',
            component: '@/pages/DockCourse',
            title: '智慧树',
          },
          {
            path: '/outcourse/classreview',
            component: '@/pages/ClassReview',
            title: '课堂回看',
          },
          {
            path: '/outcourse/myReview',
            component: '@/pages/MyReview',
            title: '我的审核',
          },
          {
            path: '/outcourse/classreview/visibleSetting',
            component: '@/pages/ClassReview/visibleSetting.tsx',
          },
          {
            path: '/outcourse/space',
            component: '@/pages/MicroList/space',
            title: '我的教学',
          },
          {
            path: '/outcourse/liveCourse',
            component: '@/pages/LiveCourse',
            title: '直播课程信息',
          },
          {
            path: '/outcourse/schoolOnline',
            component: '@/pages/DockCourse',
            title: '学堂在线',
          },
          {
            path: '/outcourse/silverLearning',
            component: '@/pages/DockCourse',
            title: '学银在线',
          },
        ],
      },
      {
        path: '/course',
        component: '@/layout/microLayout/index',
        routes: [
          {
            // exact: true,
            path: '/course',
            component: '@/pages/SpocList',
            title: '班级课',
          },
          {
            // exact: true,
            path: '/course/recycle',
            component: '@/pages/Recycle',
            title: '回收站',
          },
          {
            path: '/course/resource',
            component: '@/pages/AddCourse/AddCourse',
            title: '课程',
          },
          {
            path: '/course/video',
            component: '@/pages/VideoPage',
            title: '课程',
          },
          {
            path: '/course/microData',
            component: '@/pages/MicroData',
            title: '课程',
          },
          {
            path: '/course/videolist',
            component: '@/pages/VideoList',
            title: '课程',
          },
          {
            exact: true,
            path: '/course/mooccourse',
            component: '@/pages/MoocTabs',
            title: '课程',
          },
          {
            path: '/course/mapCourse',
            component: '@/pages/MoocTabs',
            title: '课程',
          },
          {
            path: '/course/microcourse',
            component: '@/pages/MicroList',
            title: '课程',
          },
          {
            path: '/course/trainingCourse',
            component: '@/pages/SpocList',
            title: '课程',
          },
          {
            path: '/course/minemap',
            component: '@/pages/Coursemaptool/Minemap',
            title: '我的地图',
          },
          {
            path: '/course/map_recycle',
            component: '@/pages/Coursemaptool/Recyclebin',
            title: '地图回收站',
          },
          {
            path: '/course/sharetemap',
            component: '@/pages/Coursemaptool/Sharetemap',
            title: '共享地图',
          },
          {
            path: '/course/agent',
            component: '@/pages/Agent/AgentInner.tsx',
            title: 'AI Agent',
          },
          {
            path: '/course/dockcourse/:type',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/schoolMooc',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/rainCourse',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/superstarCourse',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/pmphmoocCourse',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/umoocCourse',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/schoolOnline',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/silverLearning',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/zhihuishuCourse',
            component: '@/pages/DockCourse',
            title: '课程',
          },
          {
            path: '/course/classreview',
            component: '@/pages/ClassReview',
            title: '在线课堂',
          },
          {
            path: '/course/myReview',
            component: '@/pages/MyReview',
            title: '我的审核',
          },
          {
            path: '/course/myLive',
            component: '@/pages/MyLive',
            title: '我的直播',
          },
          {
            path: '/course/classreview/visibleSetting',
            component: '@/pages/ClassReview/visibleSetting.tsx',
            title: '课程',
          },
          {
            path: '/course/space',
            component: '@/pages/MicroList/space',
            title: '课程',
          },
          {
            path: '/course/liveCourse',
            component: '@/pages/LiveCourse',
            title: '直播课程信息',
          },
        ],
      },
      // 外部系统访问
      {
        path: '/out',
        component: '@/layout/outLayout/outLayout',
        routes: [
          {
            exact: true,
            path: '/out/course',
            component: '@/pages/MicroList',
          },
          {
            exact: true,
            path: '/out/mooccourse',
            component: '@/pages/MoocTabs',
          },
          {
            exact: true,
            path: '/out/portalconfiguration',
            component: '@/pages/BannerConfiguration',
          },

          {
            exact: true,
            path: '/out/classifiedconfiguration',
            component: '@/pages/ClassifiedConfiguration',
          },
          {
            path: '/out/coursereview',
            component: '@/pages/CourseReview',
          },
          // 问答
          {
            path: '/out/courseqa',
            component: '@/pages/CourseQA',
          },
          // 问答详情
          {
            path: '/out/courseqa/detail',
            component: '@/pages/CourseQA/detail',
          },
          // {
          //   exact: true,
          //   path: '/out/coursetemplate',
          //   component: '@/pages/CourseTemplate',
          // },
        ],
      },
      {
        path: '/coursetemplate',
        component: '@/layout/templateLayout/index',
        routes: [
          {
            exact: true,
            path: '/coursetemplate/mytemplate',
            component: '@/pages/CourseTemplate',
            title: '课程包',
          },
          {
            path: '/coursetemplate/sharetemplate',
            component: '@/pages/CourseTemplate',
            title: '课程包',
          },
          {
            path: '/coursetemplate/myReview',
            component: '@/pages/CourseTemplateReview',
            title: '课程包',
          },
          {
            path: '/coursetemplate/out/mytemplate',
            component: '@/pages/CourseTemplate',
            title: '课程包',
          },
          {
            path: '/coursetemplate/out/sharetemplate',
            component: '@/pages/CourseTemplate',
            title: '课程包',
          },

          // {
          //   path: '/course/resource',
          //   component: '@/pages/AddCourse/AddCourse',
          // },
          // {
          //   // exact: true,
          //   path: '/course/mooccourse',
          //   component: '@/pages/MoocTabs',
          // },
        ],
      },
      {
        path: '/coursemap',
        component: '@/layout/coursemapLayout/index',
        routes: [
          {
            exact: true,
            path: '/coursemap/majormap',
            component: '@/pages/Coursemaptool/Majormap',
            title: '专业地图',
          },
          {
            exact: true,
            path: '/coursemap/minemap',
            component: '@/pages/Coursemaptool/Minemap',
            title: '课程地图',
          },
          {
            path: '/coursemap/sharetemap',
            component: '@/pages/Coursemaptool/Sharetemap',
            title: '课程地图',
          },
          {
            path: '/coursemap/recyclebin',
            component: '@/pages/Coursemaptool/Recyclebin',
            title: '课程地图回收站',
          },
          {
            path: '/coursemap/labelsconfig',
            component: '@/pages/Coursemaptool/labelsconfig',
            title: '自定义标签',
          },
        ],
      },
      {
        path: '/micromajor',
        component: '@/layout/micromajorLayout/index',
        routes: [
          {
            exact: true,
            path: '/micromajor/list',
            component: '@/pages/Micromajor',
            title: '微专业',
          },
          {
            exact: true,
            path: '/micromajor/course',
            component: '@/pages/Micromajor/micromajorCourse',
            title: '微专业开课',
          },
        ],
      },
      {
        path: '/editmicromajor',
        component: '@/layout/editmicromajorLayout/index',
        routes: [
          {
            exact: true,
            path: '/editmicromajor/pyfa',
            component: '@/pages/Micromajor/components/pyfa',
            title: '培养方案',
          },
          {
            exact: true,
            path: '/editmicromajor/editmap',
            component: '@/pages/Micromajor/components/editmap',
            title: '微专业图谱',
          },
        ],
      },
      {
        path: '/tempatedetail',
        component: '@/layout/templateDeatilLayout',
        routes: [
          {
            path: '/tempatedetail/courseInfo',
            component: '@/pages/templateDeatil/CourseInfo',
            title: '课程包',
          },
          {
            path: '/tempatedetail/chapter',
            component: '@/pages/templateDeatil/Chapter',
            title: '课程包',
          },
          {
            path: '/tempatedetail/teachingteam',
            component: '@/pages/templateDeatil/TeachTeam',
            title: '课程包',
          },
          {
            path: '/tempatedetail/resources',
            component: '@/pages/templateDeatil/Resources',
            title: '课程包',
          },
          {
            path: '/tempatedetail/usage',
            component: '@/pages/templateDeatil/Usage',
            title: '课程包',
          },
          {
            path: '/tempatedetail/homework',
            component: '@/pages/HomeworkManagement/HomeworkList',
            title: '课程包',
          },
        ],
      },
      {
        path: '/tccaselibrary',
        component: '@/layout/libraryLayout/index',
        title: '案例列表',
      },
      {
        path: '/tccaselibrary/:id',
        component: '@/pages/TeachingCase/index',
        title: '案例编辑',
      },
      {
        path: '/announcement',
        component: '@/layout/announcementLayout/index',
        title: '公告',
      },
      {
        path: '/login',
        component: '@/pages/index',
      },
      {
        path: '/reviewcenter',
        component: '@/layout/reviewcenterLayout/index',
        routes: [
          {
            path: '/reviewcenter/import',
            component: '@/pages/ReviewCenter/Share',
            title: '审核',
          },
          {
            path: '/reviewcenter/share',
            component: '@/pages/ReviewCenter/Share',
            title: '审核',
          },
          {
            path: '/reviewcenter/process',
            component: '@/pages/ReviewCenter/Bpmn',
            title: '审核',
          },
          {
            path: '/reviewcenter/course',
            component: '@/pages/MyReview',
            title: '审核',
          },
          {
            path: '/reviewcenter/call',
            component: '@/pages/ReviewCenter/Call',
            title: '审核',
          },
          {
            path: '/reviewcenter/syllabus',
            component: '@/pages/ReviewCenter/Syllabus',
            title: '审核',
          },
        ],
      },
    ],
  },
];
