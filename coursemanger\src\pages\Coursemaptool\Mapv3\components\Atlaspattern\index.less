.Atlaspattern_view{
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #000000;
    overflow: hidden;
    // .whole_drawer{
    //     background: #1E2734;
    //     >div{
    //         background-color: #202d3e;
    //         box-shadow: none;
    //         border: none;
    //     }
    //     .content_box {
    //         .title {
    //             .text {
    //                 color: #fff;
    //             }
    //             .number_view {
    //                 span{
    //                     color: #BDC0C5;
    //                 }
    //             }
    //         }
    //     }
    // }
    // .custom_drawer{
    //     .ant-drawer-header{
    //         // display: none;
    //     }
    //     .ant-drawer-body{
    //         padding: 0 !important;
    //         padding-left: 20px !important;
    //     }

    // }
    // .ant-drawer .arrow{
    //     transform: translateY(-50%);
    //     top: 50%;
    //     left: 0;
    //     position: absolute;
    //     cursor: pointer;
    // }
    .message_view{
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
        
        span{
            color: #FFFFFF;
            font-size: 20px;
        }
    }
    
    .jindu_box{
        position: absolute;
        width: 614px;
        height: 77px;
        background: #122035;
        border-radius: 10px;
        top:10px;
        left: 30px;
        z-index: 1;
        display: flex;
        justify-content: space-evenly;

        .top_view{
            span{
                color: #fff;
            }
        }
        
    }

    .message_view{
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
        
        span{
            color: #FFFFFF;
            font-size: 20px;
        }
    }
    
    .jindu_box{
        position: absolute;
        width: 614px;
        height: 77px;
        background: #122035;
        border-radius: 10px;
        top:10px;
        left: 30px;
        z-index: 1;
        display: flex;
        justify-content: space-evenly;

        .top_view{
            span{
                color: #fff;
            }
        }
        
    }

    .message_view{
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
        
        span{
            color: #FFFFFF;
            font-size: 20px;
        }
    }
    
    .map_content_view{
        position: relative;
        width: 100%;
        height: calc(100% - 70px);
        margin-top: 70px;

        .ant-drawer-header{
            padding: 0;
            margin: 0;
            background-color: #000000;
            border-bottom: 1px solid #1E2734;
        }

        .ant-drawer-body{
            background-color: #1E2734;
        }
        .course_info_item{
            background-color: #202D3E;
            margin-bottom: 10px;
            justify-content: space-between;
        }

        .course_info_item_label{
            color: #ABAEB3;
            border-left: 4px solid #5BCDFF;
            padding-left: 10px;
            margin-left: 10px;
            font-weight: 600;           
        }

        .course_info_item_value{
            color: #ABAEB3;
            margin-right: 10px;
        }

       
    }


    .node_detail_view{
        .Comparativeanalysis_box{
            color: #ABAEB3 !important;

            .ant-input-affix-wrapper{
                background-color: #1E2734 !important;
                .anticon{
                    color: #ABAEB3 !important;
                }
            }
        }

        .other_title,.rate_title,.title_span,.content_name,.ant-list-empty-text,.ant-page-header-heading-title,.ant-table-cell,.detail{
            color: #ABAEB3 !important;
        }
        .title .span1{
            color: #fff;
            font-size: 18px;
        }
        .ant-input{
            background-color: #1E2734;
            color: #ABAEB3;
        }
        
        .ant-tabs-tab,.ant-empty-description{
            color: #ABAEB3;
        }

        .ant-input-search-button,.ant-table-container{
            background-color: #1E2734;
        }
        
        .anticon-search{
            color: #ABAEB3 !important;
        }
        
        .ant-table-tbody > tr.ant-table-placeholder:hover > td{
                background-color: #1E2734 !important;
        }
        
        .ant-table-thead > tr > th{
            background-color: #2B3546;
        }

        .ant-table-thead > tr{
            background-color: #2B3546;
        }
        td.ant-table-column-sort{
            background-color: #1E2734;
        }
        .ant-pagination-item-link , .ant-pagination-item, .ant-pagination{
            background-color: #1E2734;
            border-color: #5D5D5D;
            color: #fff;
        }
        .ant-pagination-item a{
            color: #fff;
        }
        .ant-pagination-item-active a{
            color: #549CFF ;
        }
        .ant-table-thead > tr > th{
            border-color: #5D5D5D;
        }
        .ant-table-tbody > tr > td{
            border-color: #5D5D5D;
        }
        .divider_dashed{
            border-bottom: 1px solid #1E2734 !important;
        }

        .ant-tabs-top > .ant-tabs-nav::before{
            border-bottom: 1px solid #404E62;
        }

        .entity-preview .video-wrap{
            border: 0;
        }

        .ant-list-bordered{
            border: 1px solid #404E62; 
        }

        .ant-drawer-body{
            // 滚动条颜色变成暗黑系
            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 3px;
                background-color: #404F65;
            }
            &::-webkit-scrollbar-track {
                border-radius: 3px;
                background-color: #1E2734;
            }
        
        }
        
        .serial-number,.homework-sub-item .title-container,.answer-item,.ant-radio-wrapper,.ant-checkbox-wrapper,.ant-select-arrow,.ant-select-clear{
            color: #ABAEB3;
        }

        .ant-input{
            border-color: #797979;
        }

        .homework-sub-item{
            border-bottom: 1px dashed #5D5D5D;
        }

        .ant-list-split .ant-list-item{
            border-bottom: 1px solid #404E62;
            &:last-child{
                border-bottom: 0;
            }
        }
        
        .ant-table-tbody > tr.ant-table-row:hover > td, .ant-table-tbody > tr > td.ant-table-cell-row-hover{
            background-color: #1E2734 !important;
        }
        
        .anticon-arrow-left{
            color: #5BCDFF;
        }
        .ant-page-header-heading-sub-title{
            color: #5BCDFF;
        }

        .ant-btn-link{
            color: #ABAEB3;
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector{
            background-color: transparent;
        }

        .ant-select-selection-item{
            color: #ABAEB3;
        }
        .ant-select-selection-item-content{
            color:#ABAEB3;
        }

        .right_box2{
            color: #ABAEB3;
        }
    }

     // 适配问答样式黑色皮肤

    #basic{
        .from_item_name span{
            color: #ABAEB3 !important
          }
    }   

    .rdrawer{
        .ant-typography{
            color:#fff;
        }
    }

    .drawer_view{

        .knowledge_name{
            background-color: transparent;
            border: 1px solid #404E62;
            
            >span{
                color: #549CFF;
            }
        }
        
        .detail{
            p{
                color: #ABAEB3 !important;
            }
        }
        
        .title{
            span{
                color: #ABAEB3 !important;
            }
        }

        .left_view{
            span{
                color: #ABAEB3 !important;
            }
        }

        .node_createuser,.node_status{
            color: #ABAEB3 !important;
        }


        .courseqa_detail{
            background-color: #1E2734;
            border: none !important;
            padding: 15px;

            .ant-tag-blue{
                color: #549cff;
                background: #1e2734;       
            }

            .courseqa_container{
                background-color: #1E2734;
            }

            .topic_title span, .ant-space-item , .content_info,.from_item_name span,.reply_count,.bold,.ant-space-item .ant-btn-text,.comment_main{
                color: #ABAEB3 !important
            }   

            .topic_container{
                border-bottom: 1px solid #404E62;

                .flex-sb{
                    flex-wrap: wrap;
                }
                
                // .info_knowledge{
                //     margin-bottom: 20px;
                // }
                .info_msg{
                    display: flex;
                    flex-wrap: wrap;
                    .ant-space-item-split{
                        display: none;
                    }

                    .ant-space{
                        width: 100%;
                    }
                }
            }
            .ant-input-affix-wrapper{
                background-color: #1E2734 !important;
                .anticon{
                    color: #ABAEB3 !important;
                }
            }
        }

        .topic_container{
            border-bottom: 1px solid #404E62;

            .comment_wrapper{
                background-color: #404F65;

                .comment_main{
                    p{
                        color: #fff;
                    }
                }

                .ant-tag-blue{
                    color: #549cff !important;
                    background-color: #404E62;
                }
            }
            .comment_wrapper::before{
                border-bottom-color: #404F65;
            }
        }
        
        .ant-input-affix-wrapper{
            border: 1px solid #404E62;
        }

        .ant-select:not(.ant-select-customize-input) .ant-select-selector{
            border: 1px solid #404E62;
        }

        .detail_view{
            background-color: #1E2734;
            border: none !important;
    
            .courseqa_container{
                background-color: #1E2734;

                .new_reply_tag::after{
                    display: none;
                }
            }
    
            span , .ant-space-item , .content_info,.from_item_name span{
              color: #ABAEB3 !important
            }     
                        
        }
    }
    
    .map_heard_view{
        .ant-select-arrow{
            color: #FFFFFF !important;
        }
    }

    .Teachingmodule_view{
        .title_view span{
            color: #ABAEB3 !important;
        }

        .teacher_viwe{
            padding-left: 20px;
            font-weight: 500;
            font-size: 14px;
            color: #FFFFFF;
    
            span{
                color: #fff;
            }
        }
    
        .score{
            color: #fff;
        }
    
        .time{
            color: #fff;
        }
    }
}

.Atlaspattern_view_dark{
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, #EAFAFF, #F3F8FE);
    overflow: hidden;

    .jindu_box{
        position: absolute;
        width: 614px;
        height: 77px;
        background: #ffffff;
        border-radius: 10px;
        top:10px;
        left: 30px;
        z-index: 1;
        display: flex;
        justify-content: space-evenly;

        .top_view{
            span{
                color: #000;
            }
        }
        
    }

    .message_view{
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
        
        span{
            color: #000000;
            font-size: 20px;
        }
    }
    

    .map_content_view{
        position: relative;
        width: 100%;
        height: calc(100% - 70px);
        margin-top: 70px;

        .ant-drawer-header{
            padding: 0;
            margin: 0;
            background-color: #EEF5FF;
        }

        .ant-drawer-body{
            background-color: #fff;
        }
       
    }
}