import React, { FC, useEffect, useState, useRef } from 'react';
import {
  Input,
  Button,
  Table,
  Pagination,
  Select, Space
} from
    'antd';
import './index.less';
import { useHistory, history, useLocation, useSelector } from 'umi';
import statisticsApi from '@/api/statistics';
import useLocale from '@/hooks/useLocale';
const { Search } = Input;

const Achievementdegree: FC<any> = ({ personalInfo }) => {
  const { t } = useLocale();
  const location: any = useLocation();
  // 当前的课程id
  const courseId = location.query.id;
  const [data, setData] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [courseinfo, setCourseinfo] = useState<any>([]);
  const courseName = useRef<string>('');
  const isQuestion = useRef<any>(null); // 是否提问
  const nodeName = useRef<string>(''); // 知识点名称
  // 分页信息
  const [pageinfo, setPageinfo] = useState<any>({
    pageIndex: 1,
    pageSize: 6,
    total: 0,
    // sortOrder: '',
    sortField: '',
    isDesc: true //是否逆序
  });


  useEffect(() => {
    initData(
      pageinfo.pageIndex,
      pageinfo.pageSize,
      pageinfo.sortField,
      pageinfo.isDesc);

  }, []);



  // 查询列表
  const initData = (
    page: number,
    size: number,
    sortField: string,
    isDesc: boolean) => {
    setLoading(true);
    let obj = {
      courseId: courseId,
      courseName: courseName.current,
      nodeName: nodeName.current,
      page: page,
      size: size,
      isDesc: isDesc,
      field: sortField,
      isQuestion: isQuestion.current, //是否提问： false true
      userCode: personalInfo.code
    };
    statisticsApi.getStudentKnowledgestatistics(obj).then((res: any) => {
      if (res.status == 200) {
        setPageinfo({
          ...pageinfo,
          pageIndex: res.data.data.page,
          pageSize: res.data.data.size,
          total: res.data.data.total
        });
        setData(res.data.data.results);
      } else {
        setPageinfo({
          ...pageinfo,
          pageIndex: res.data.data.page,
          pageSize: res.data.data.size,
          total: res.data.data.total
        });
        setData([]);
      }
      setLoading(false);
    }).finally(() => {
      setLoading(false);
    });
  };

  // 知识点
  const columns: any = [
    {
      title: t("知识点"),
      dataIndex: 'nodeName',
      key: 'nodeName',
      align: 'center'
    },
    {
      title: t("上级节点"),
      dataIndex: 'parentNodeName',
      key: 'parentNodeName',
      align: 'center'
    },
    {
      title: t('关联课程目标'),
      dataIndex: 'courseTargetBeans',
      className: 'table-header-cell',
      key: 'courseTargetBeans',
      align: 'center',
      ellipsis: true,
      render: (courseTargetBeans: any) => (
        <Space wrap>
          {courseTargetBeans?.map((member:{ name: string }) => member.name)}
        </Space>
      ),
    },
    {
      title: t("完成进度"),
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      render(text: any, record: any) {
        return (
          <span>
            {Number(record.studyResourceCount)}/{Number(record.resourceTotal)}
          </span>);

      }
    },
    {
      title: t("完成率"),
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      }
    },
    {
      title: t("掌握率"),
      dataIndex: 'masterRate',
      key: 'masterRate',
      align: 'center',
      sorter: true,
      render(text: any, record: any) {
        if (text == '-') {
          return '-';
        } else {
          return Number(text) + '%';
        }
      }
    },
    {
      title: t("是否提问"),
      dataIndex: 'isQuestion',
      key: 'isQuestion',
      align: 'center',
      render: (text: any, record: any) => {
        return <span>{text == 0 ? t("否") : t("是")}</span>;
      }
    }];

  return (
    <div className="Achievementdegree">
      <div className="search_box">
        <div>
          {/* <Select
             placeholder="请选择"
             style={{ width: 220 }}
             options={courseinfo}
             allowClear
             onChange={(value:any)=>{
               courseName.current = value;
               initData(pageinfo.pageIndex, pageinfo.pageSize,pageinfo.sortField,pageinfo.isDesc);
             }}
            /> */}
          <Select
            placeholder={t("是否提问")}
            style={{ width: 120, marginLeft: 30 }}
            allowClear
            options={[
              {
                value: true,
                label: t("是")
              },
              {
                value: false,
                label: t("否")
              }]}

            onChange={(value: any) => {
              isQuestion.current = value;
              initData(pageinfo.pageIndex, pageinfo.pageSize, pageinfo.sortField, pageinfo.isDesc);
            }} />

          <Search
            allowClear
            style={{ width: 220, marginLeft: 30 }}
            placeholder={t("请输入知识点名称")}
            onSearch={(e: any) => {
              nodeName.current = e;
              initData(pageinfo.pageIndex, pageinfo.pageSize, pageinfo.sortField, pageinfo.isDesc);
            }} />

        </div>
      </div>
      <Table
        className="statistics_table"
        columns={columns}
        dataSource={data}
        pagination={false}
        // scroll={{ x: true }}
        loading={loading}
        rowKey={'nodeId'}
        onChange={(page: any, filters: any, sorter: any) => {
          let isDesc: any = sorter.order ?
            sorter.order == 'descend' ?
              true :
              false :
            null;
          let sortField: any = isDesc != null ? sorter.field : null;
          setPageinfo({
            ...pageinfo,
            sortField: sortField,
            isDesc: isDesc
          });
          initData(page.current, page.pageSize, sortField, isDesc);
        }} />

      <div className="cd_course_pagination">
        <Pagination
          size="small"
          showQuickJumper
          current={pageinfo.pageIndex}
          pageSize={pageinfo.pageSize}
          total={pageinfo.total}
          showSizeChanger={true}
          pageSizeOptions={[6, 12, 24, 48, 60, 90]}
          showTotal={(total) => {
            return t("共{name}条", String(total));
          }}
          onChange={(pageIndex, pageSize: any) => {
            initData(pageIndex, pageSize, pageinfo.sortField, pageinfo.isDesc);
          }} />

      </div>
    </div>);

};

export default Achievementdegree;
