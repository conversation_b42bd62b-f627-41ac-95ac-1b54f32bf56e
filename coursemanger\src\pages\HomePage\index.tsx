import { courseList, getSemesters, getUserJurisdictionV2 } from '@/api/course';
import {
  getAssistant,
  getCourse,
  getFolderFile,
  getLatestResources,
  getMyResLink,
  getOpenResources,
  getResouceTree,
  getWelcome,
  reqNoticeCourseId
} from '@/api/home';
import { getSpocCourse } from '@/api/mooclist';
import { BpmnHeaders, getAssistantTsak, getHiveToken } from '@/api/review';
import { ReactComponent as home1 } from '@/assets/imgs/icon/home1.svg';
import { ReactComponent as home2 } from '@/assets/imgs/icon/home2.svg';
import gif from '@/assets/imgs/myHome/gif.png';
import { ReactComponent as home3 } from '@/assets/imgs/icon/home3.svg';
import { ReactComponent as home4 } from '@/assets/imgs/icon/home4.svg';
import { ReactComponent as home5 } from '@/assets/imgs/icon/home5.svg';
import { ReactComponent as ai_tools } from '@/assets/imgs/icon/ai_tools.svg';
import { ReactComponent as course_map } from '@/assets/imgs/icon/course_map.svg';

import college_service from '@/assets/imgs/myHome/college_service.png';
import useCkeckRoles from '@/hooks/useCkeckRoles';
import document_search from '@/assets/imgs/myHome/document_search.png';
import document_summary from '@/assets/imgs/myHome/document_summary.png';
import jianji from '@/assets/imgs/myHome/jianji.png';
import knowledge_answer from '@/assets/imgs/myHome/knowledge_answer.png';
import resolve_picture from '@/assets/imgs/myHome/resolve_picture.png';
import shenhe from '@/assets/imgs/myHome/shenhe.png';
import shipin from '@/assets/imgs/myHome/spjianji.png';
import translate_assistant from '@/assets/imgs/myHome/translate_assistant.png';
import translate_assistant2 from '@/assets/imgs/myHome/translate_assistant2.png';
import wenda from '@/assets/imgs/myHome/wenda.png';
import yuyin from '@/assets/imgs/myHome/yuyin.png';
import zuoye from '@/assets/imgs/myHome/zuoye.png';
import Calendar from '@/components/Calendar';
import ClassSchedule from '@/components/ClassSchedule';
import Header from '@/components/Header';
import LeftMenu from '@/components/LeftMenu';
import NPUHeader from '@/components/NPUHeader';
import { IconFont } from '@/components/iconFont';
import timecodeconvert from '@/components/time-code/timeCode';
import useLocale from '@/hooks/useLocale';
import perCfg from '@/permission/config';
import { CUSTOMER_NPU,ModuleCfg2,CUSTOMER_HNLG,CUSTOMER_CXD } from '@/permission/moduleCfg';
import Icon, {
  CloudUploadOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusCircleOutlined,
  RightCircleOutlined,
  RightOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Drawer,
  Empty,
  message,
  Select,
  Spin,
  Tooltip,
} from 'antd';
import VirtualList from 'rc-virtual-list';
import React, { FC, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import CourseCard from './components/CourseCard';
import Panel from './components/Panel';
import './index.less';
import _ from 'lodash';
import HomeNav from './components/HomeNav';
interface IAddIcon {
  link: string;
  type?: 'course' | 'resource';
  text: string;
}

interface IOpenResource {
  data: any;
}
// const addCourseList = [
//   { link: "#/course/microcourse", text: "建微课", key: "micro", showKey: perCfg.microcourse_new },
//   { link: "#/course", text: "建班级课", key: "spoc", showKey: perCfg.spoc_new },
//   { link: "#/course/spoccourse", text: "建公开课", key: "mooc", showKey: perCfg.mooc_new },
// ];

interface Tools {
  width: number;
  height: number;
  alt: string;
  name: string;
  openUrl: string;
  iconSrc: any;
  bgColor: string;
  isShow: boolean;
}

const OpenResource: FC<IOpenResource> = ({ data }) => {
  const handleClick = () => {
    window.open(`/rman/#/basic/rmanDetail/${data.contentId_}`);
  };
  return (
    <div className="open-resource" onClick={handleClick}>
      <div className="resource-img">
        <img src={data.keyframe_} alt="" />
      </div>
      <div className="resource-title">{data.name_}</div>
    </div>
  );
};

const AddIcon: FC<IAddIcon> = ({ link, type = 'course', text }) => {
  const handleClick = () => {
    window.open(link);
  };
  return (
    <div className="add-icon" onClick={handleClick}>
      {type === 'course' ? <PlusCircleOutlined /> : <CloudUploadOutlined />}
      <div className="icon-text">{text}</div>
    </div>
  );
};

const HomePage: FC = () => {
  const { t } = useLocale();
  const { permission, parameterConfig, userInfo } = useSelector<
    { global: any },
    any
  >(state => state.global);
  const addCourseList = [
    {
      link: '#/course/microcourse',
      text: `${'建'}${t('微课')}`,
      key: 'micro',
      showKey: perCfg.microcourse_new,
    },
    {
      link: '#/course',
      text: `${t('建班级课')}`,
      key: 'spoc',
      showKey: perCfg.spoc_new,
    },
    {
      link: '#/course/mooccourse',
      text: t('建公开课'),
      key: 'mooc',
      showKey: perCfg.mooc_new,
    },
  ];
  const ShortcutModule = [
    {
      icon: home1,
      text: t('上传资源'),
      link: '/rman/#/basic/rmanCenterList?isAdd=true',
    },
    {
      icon: home2,
      text: t('新建班级课'),
      link: '#/course?openShow=true',
    },
    {
      icon: home3,
      text: t('新建微课'),
      link: '#/course/microcourse?openShow=true',
    },
    {
      icon: home4,
      text: t('新建知识地图'),
      link: '#/coursemap/minemap?openShow=true',
    },
    {
      icon: home5,
      text: t('导入试题'),
      link: '/exam/#/exam/topicManage?openShow=true',
    },
    {
      icon: 'kuaiji6',
      text: t('更多') + '>>',
      link: '#/basic/rman',
    },
  ];
  const ShortcutModuleCqsf = [
    {
      icon: home1,
      text: '登录数',
      num: 146,
    },
    {
      icon: home2,
      text: '在线课堂数',
      num: 24,
    },
    {
      icon: home3,
      text: '新建课程数',
      num: 36,
    },
    {
      icon: home4,
      text: '录播资源数',
      num: 7543,
    },
    {
      icon: home5,
      text: '个人资源数',
      num: 24,
    },
  ];
  const SHTechShortcutModule = [
    {
      icon: home1,
      text: t('上传资源'),
      link: '/rman/#/basic/rmanCenterList?isAdd=true',
    },
    {
      icon: course_map,
      text: t('新建知识地图'),
      link: '#/coursemap/minemap?openShow=true',
    },
    {
      icon: ai_tools,
      text: t('创建AI Agent'),
      link: '/learn/workbench/#/agent',
    },
    {
      icon: home5,
      text: t('导入试题'),
      link: '/exam/#/exam/topicManage?openShow=true',
    },
  ];

  const [loading, setLoading] = useState(false); //消息加载
  const [getLoadingList, setLoadingList] = useState(false); //消息加载
  const [data, setData] = useState<any>([]); //当前消息数据
  const [pageNum, getPageNum] = useState<number>(1); //当前消息数据
  const [getRecordTotal, setRecordTotal] = useState<number>(0); //当前消息数据
  const [getTotal, setTotal] = useState<number>(0); //当前审核消息数据
  const [welcome, setWelcome] = useState<any>({});
  const [courseAssistant, setCourseAssistant] = useState<any>({});
  const [openResources, setOpenResources] = useState<any>([]);
  const [openResourcesNew, setOpenResourcesNew] = useState<any>([]);
  const [latestResources, setLatestResources] = useState<any>([]);
  const [courses, setCourses] = useState<any[]>([]);
  const [getIndex, setIndex] = useState<number>(0);
  const [jurisdictionList, setJurisdictionList] = useState<any>([]);
  const [showCardCount, setShowCardCount] = useState<number>(5);
  const [containerHeight, messageHeight] = useState<number>(150);
  const [showSourceCount, setShowSourceCount] = useState<number>(5);
  const [showToolsCount, setShowToolsCount] = useState<number>(3);

  const [noticeCourseId, setNoticeCourseId] = useState<string>('');
  const [calendarShow, setCalendarShow] = useState<boolean>(false);
  const [headers, setHeaders] = useState<BpmnHeaders>({
    Authorization: '',
    'sobeycloud-site': 'S1',
  });
  const [isSHTech, setIsSHTech] = useState<boolean>(false); // 是否是上海科技大学
  const [myToolsList, setMyToolsList] = useState<Tools[]>([]);
  const [myResList, setMyResList] = useState<any[]>([]); // 我的资源
  const [isFold, setIsFold] = useState<boolean>(false); // 课表是否收起
  const [courseSemester, setCourseSemester] = useState<any[]>([]);
  const [curSemester, setCurSemester] = useState<string>('');
  const [resLoading, setResLoading] = useState<boolean>(false);
  const notSupportMessage = '暂不支持手机端，请前往电脑端操作';
  const [isFirstGetCourseList, setIsFirstGetCourseList] = useState<boolean>(true)
  const [isSuper, setIsSuper] = useState(false);
  //控制是否显示首页导航，后期要根据环境来判断，目前只针对成信大,判断是否是成信大。成信大的判断标志是kczx,把这里改成false下面useEffect中判断isCxd的判断放开就行，现在本地开发是写死的
  // todo :需要其他模块的开发配合来控制 <LeftMenu/>是否显示 {parameterConfig.target_customer == 'kczx' && <LeftMenu/>}
  //是不是超级管理员
  const [isSuperVal,setIsSuperVal] = useState(false)
  const [isCxd, setIsCxd] = useState<boolean>(false)
  const [isHaveTeacher,setIsHaveTeacher] = useState<boolean>(false);

  console.log('isCxs',isCxd)
  useEffect(() => {
    if (userInfo.roles) {
      let roles = userInfo.roles?.map((item: any) => item.roleCode) || [];
      console.log('roles',roles)
      setIsHaveTeacher(roles.includes('r_teacher'))
      console.log(roles.includes('r_sys_manager'))
      setIsSuperVal(roles.includes('r_sys_manager'))
      useCkeckRoles(roles);
    }
  }, [userInfo.roles])
  useEffect(() => {
    //
    console.log(isSuperVal)
    setIsCxd(parameterConfig.target_customer == 'kczx' && (isSuperVal || isHaveTeacher ))
    console.log('isCxd--->', isCxd)
  }, [parameterConfig]);
  useEffect(() => {
    setIsSHTech(parameterConfig.target_customer == 'shangHaiTech');
  }, [parameterConfig]);
  useEffect(() => {
    setIsCxd((isSuperVal || isHaveTeacher ) && parameterConfig.target_customer == 'kczx')
  }, [isHaveTeacher]);
  useEffect(() => {
    if (isSHTech) {
      hideRight()
    }
  }, [isSHTech])

  useEffect(() => {
    const tools: Tools[] = [
      {
        width: 60,
        height: 60,
        alt: '视频剪辑',
        name: '视频剪辑',
        isShow: permission.includes(ModuleCfg2.spjj),
        openUrl: '/aitools/#/clip',
        iconSrc: shipin,
        bgColor: '#fceded',
      },
      {
        width: 60,
        height: 60,
        alt: '在线剪辑',
        name: '在线剪辑',
        isShow: permission.includes(ModuleCfg2.jove),
        openUrl: '/joveone/#',
        iconSrc: jianji,
        bgColor: '#f4f6fd',
      },
      {
        width: 60,
        height: 60,
        alt: 'GIF制作',
        name: 'GIF制作',
        isShow: permission.includes(ModuleCfg2.gif),
        openUrl: '/aitools/#/toGif',
        iconSrc: gif,
        bgColor: '#f0ebfd',
      },
    ];
    const shTechTools: Tools[] = [
      {
        width: 60,
        height: 60,
        alt: '语音剪辑',
        name: '语音剪辑',
        openUrl: '/textclip/#/clip/myTextClip',
        iconSrc: yuyin,
        bgColor: '#e5feff',
        isShow: true,
      },
      {
        width: 60,
        height: 60,
        alt: '视频剪辑',
        name: '视频剪辑',
        openUrl: '/aitools/#/clip',
        iconSrc: shipin,
        bgColor: '#fceded',
        isShow: true,
      },
      {
        width: 60,
        height: 60,
        alt: 'GIF制作',
        name: 'GIF制作',
        openUrl: '/aitools/#/toGif',
        iconSrc: gif,
        bgColor: '#f0ebfd',
        isShow: true,
      },
      {
        width: 60,
        height: 60,
        alt: '智能识图工具',
        name: '智能识图工具',
        openUrl: '/aitools/#/intelligentImage',
        iconSrc: resolve_picture,
        bgColor: '#edf5fe',
        isShow: true,
      },
      {
        width: 60,
        height: 60,
        alt: '中英翻译工具',
        name: '中英翻译工具',
        openUrl: '/aitools/#/translate',
        iconSrc: translate_assistant2,
        bgColor: '#edf5fe',
        isShow: true,
      },
      {
        width: 60,
        height: 60,
        alt: '在线剪辑',
        name: '在线剪辑',
        openUrl: '/joveone/#',
        iconSrc: jianji,
        bgColor: '#f4f6fd',
        isShow: true,
      },
    ]
    setMyToolsList(isSHTech ? shTechTools : tools)
  }, [permission, isSHTech]);
  useEffect(() => {
    reqWelcome();
    reqAssistant();
    reqLatestResources();
    // reqOpenResources();
    getCoursePermissions();
    getNoticeCourseId();
    loadMoreData();
    handleChangeSize(window.innerWidth);
    handleChangeHeightSize(window.innerHeight);
    window.addEventListener('resize', onResize);
    const timer = setInterval(reqAssistant, 10000);
    return () => {
      clearInterval(timer);
    };
  }, []);

  useEffect(() => {
    if (userInfo.roles) {
      let roles = userInfo.roles?.map((item: any) => item.roleCode) || [];
      useCkeckRoles(roles);
    }
  }, [userInfo?.roles])

  useEffect(() => {
    if (userInfo?.roles) {
      const newIsSuper = userInfo.roles
        ?.map((item: any) => item.roleCode)
        ?.includes('r_sys_manager') ||
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_course_manager') ||
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_second_manager') ||
        userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1');
      setIsSuper(newIsSuper);
    } else {
      setIsSuper(false);
    }
  }, [userInfo?.roles]);

  const getMyResList = async () => {
    setResLoading(true);
    try {
      const res1 = await getResouceTree();
      const info = res1.data[0];
      const sendData = {
        folderId: info.id,
        folderPath: '/',
        conditions: [],
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
        pageIndex: 1,
        pageSize: 150,
        anonymous: true
      };
      const res2 = await getFolderFile(sendData);
      setMyResList(res2.data.data);
    } catch (error) {
      message.error('获取我的资源失败');
    } finally {
      setResLoading(false);
    }
  };

  const querySemester = async () => {
    try {
      const res = await getSemesters();
      if (res.status === 200) {
        const list = res.data.semesterDataList;
        if (isSHTech) {
          list.unshift({
            id: '全部',
            name: '',
            showSemester: '全部'
          })
        }
        setCourseSemester(res.data.semesterDataList);
        setCurSemester(res.data.currentSemester?.name || '');
        // 使用 await 等待获取学期数据完成后再获取列表数据
        let sem = res.data.currentSemester?.name
        await getListData(sem);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error('获取学期数据失败');
    }
  };

  const getProcessToken = () => {
    getHiveToken().then((res: any) => {
      if (res && res.status === 200) {
        setHeaders({
          Authorization: res.data,
          'sobeycloud-site': 'S1',
        });
      }
    });
  };

  useEffect(() => {
    getProcessToken();
  }, []);

  useEffect(() => {
    if (headers.Authorization) {
      getAssistantTsak(headers).then(res => {
        if (res?.data) {
          setTotal(res.data.total);
        }
      });
    }
  }, [headers]);
  const getType = (type: string) => {
    let ziyuan = [
      'resource_upload',
      'resource_comments',
      'resource_analysis',
      'resource_share_review',
      'resource_share',
    ];
    let kecheng = [
      'course_question',
      'course_homework_correct',
      'course_release_review',
      'course_release_fans',
      'course_homework_reminder',
    ];
    let zhibo = ['course_homework_reminder'];
    if (ziyuan.includes(type)) {
      return '资源';
    } else if (zhibo.includes(type)) {
      return '直播';
    } else if (kecheng.includes(type)) {
      return '课程';
    } else {
      return '课程';
    }
  };

  useEffect(() => {
    if (isSHTech) {
      handleChangeHeightSize(window.innerHeight)
      getMyResList()
      querySemester()
      setIndex(2)
    }
  }, [isSHTech, isSuper]);

  const listReqAbort = useRef<any>(null);
  const getListData = (sem?: string) => {
    const isSH = parameterConfig.target_customer == 'shangHaiTech';
    // if (!isFirstGetCourseList) return
    // 取消之前的请求
    if (getIndex == 1 || getIndex == 2 || isSH) {
      setLoadingList(true);
      // 取消之前的请求
      listReqAbort.current && listReqAbort.current()
      getSpocCourse({
        pageIndex: 1,
        pageSize: 20,
        courseType: isSH ? 2 : getIndex,
        personalMode: isSuper,
        semester_teaching_courses: isSH ? sem : '',
      }, (c:any) => listReqAbort.current = c).then((res: any) => {
        if (res && res.status === 200) {
          let list = res.data.results
            ?.map((item: any) => item.entityData)
            ?.sort((a: any, b: any) => b.publishStatus - a.publishStatus)
            .sort((a: any, b: any) => (b.publishStatus != 1 ? -1 : 0)); //不为1的状态放到最后
          // list = list?.slice(0, list.length > 5 ? 5 : list.length);

          setCourses(list);
          setLoadingList(false);
        }
      }).finally(() => {
        // setIsFirstGetCourseList(false)
      })
    } else if (getIndex == 3) {
      setLoadingList(true);
      let param = {
        keyword: '',
        publishStatus: null,
        isTop: null,
        courseType: 0,
        order: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
        page: 1,
        size: 24,
      };
      courseList(param).then(res => {
        if (res && res.message === 'OK') {
          let list = res.data.results
            ?.sort((a: any, b: any) => b.publishStatus - a.publishStatus)
            .sort((a: any, b: any) => (b.publishStatus != 1 ? -1 : 0)); //不为1的状态放到最后
          // list = list?.slice(0, list.length > 5 ? 5 : list.length);

          setCourses(list);
          setLoadingList(false);
        }
      });
    } else if (getIndex == 0) {
      reqCourses();
    }
  }

  useEffect(() => {
    getListData();
  }, [getIndex, curSemester]);

  useEffect(() => {
    handleChangeHeightSize(window.innerHeight);
  }, [courses]);

  const onResize = (e: any) => {
    handleChangeSize(e.currentTarget.innerWidth);
    handleChangeHeightSize(e.currentTarget.innerHeight);
  };

  const loadMoreData = () => {
    setLoading(true);
    fetch(
      `/unifiedplatform/v1/message/notify/self/list?size=50&page=${pageNum}&keyword=&isRead`,
    )
      .then(res => res.json())
      .then(body => {
        setData([...data, ...body.extendMessage.results]);
        setRecordTotal(body.extendMessage.recordTotal);
        setLoading(false);
        getPageNum(pageNum + 1);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  // const ContainerHeight = 150;

  const handleChangeHeightSize = (height: number) => {
    try {
      const leftNode = document.querySelector(
        '.content-left',
      ) as HTMLDivElement;
      const childNodes = leftNode.childNodes;
      let leftNodeHeight =
        100 + (childNodes[childNodes.length - 1] as any).clientHeight;
      childNodes.forEach((item: any, index) => {
        if (index != childNodes.length - 1) {
          leftNodeHeight += item.clientHeight;
        }
      });
      console.log('leftNodeHeight', leftNodeHeight);
      if (height >= 919) {
        // (document.querySelector(
        //   '.messageBox',
        // ) as any).style.height = `${100}px`;
        // messageHeight(100);
        (document.querySelector(
          '.messageBox',
        ) as any).style.height = `calc(${height}px - 772px)`;
        messageHeight(height - 772);
        (document.querySelector(
          '.content-container .content-right',
        ) as any).style.height = `calc(${height}px - 78px)`;
      }
      if (isSHTech) {
        (document.querySelector(
          '.content-container .content-right',
        ) as any).style.height = `${leftNodeHeight}px`;
      }
      setShowToolsCount(Math.floor(leftNode.clientWidth / 104));
    } catch (error) { }
  };
  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (
      e.currentTarget.scrollHeight - e.currentTarget.scrollTop ===
      containerHeight
    ) {
      loadMoreData();
    }
  };
  const handleChangeSize = (width: number) => {
    try {
      if (width >= 1800) {
        (document.querySelector(
          '.content-container .content-right',
        ) as any).style.flexGrow = 1;
        (document.querySelector(
          '.content-container .content-left',
        ) as any).style.width = '62%';
        (document.querySelector('.welcome-container') as any).style.width =
          '680px';
        (document.querySelector(
          '.welcome-teach .panel-container',
        ) as any).style.width = '49%';
        setShowCardCount(5);
        setShowSourceCount(5);
      } else if (width < 1800 && width >= 1660) {
        (document.querySelector(
          '.content-container .content-right',
        ) as any).style.flexGrow = 1;
        (document.querySelector(
          '.content-container .content-left',
        ) as any).style.width = '55%';
        (document.querySelector('.welcome-container') as any).style.width =
          '630px';
        (document.querySelector(
          '.welcome-teach .panel-container',
        ) as any).style.width = '49%';
        setShowCardCount(4);
        setShowSourceCount(5);
      } else if (width < 1660 && width >= 1410) {
        (document.querySelector(
          '.content-container .content-right',
        ) as any).style.flexGrow = 1;
        (document.querySelector(
          '.content-container .content-left',
        ) as any).style.width = '51%';
        (document.querySelector('.welcome-container') as any).style.width =
          '630px';
        (document.querySelector(
          '.welcome-teach .panel-container',
        ) as any).style.width = '49%';
        setShowCardCount(4);
        setShowSourceCount(5);
      } else if (width > 768) {
        (document.querySelector(
          '.content-container .content-right',
        ) as any).style.flexGrow = 1;
        (document.querySelector(
          '.content-container .content-left',
        ) as any).style.width = '50%';
        (document.querySelector('.welcome-container') as any).style.width =
          '580px';
        (document.querySelector(
          '.welcome-teach .panel-container',
        ) as any).style.width = '49%';
        setShowCardCount(3);
        setShowSourceCount(5);
      } else {
        (document.querySelector(
          '.content-container .content-left',
        ) as any).style.width = 'calc(100vw - 30px)';
        (document.querySelector('.welcome-container') as any).style.width =
          'calc(100vw - 30px)';
        (document.querySelector(
          '.welcome-teach .panel-container',
        ) as any).style.width = 'calc(100vw - 30px)';
        setShowCardCount(3);
        setShowSourceCount(4);
      }
    } catch (error) { }
  };

  const getNoticeCourseId = () => {
    reqNoticeCourseId().then(res => {
      if (res?.status === 200) {
        setNoticeCourseId(res.data ?? {});
      }
      //  else {
      //   message.error(res.message);
      // }
    });
  };

  const reqWelcome = () => {
    getWelcome().then(res => {
      if (res?.status === 200) {
        setWelcome(res?.data);
      }
      //  else {
      //   message.error(res.message);
      // }
    });
  };
  const reqAssistant = () => {
    getAssistant().then(res => {
      if (res?.status === 200) {
        setCourseAssistant(res.data);
      }
      // else {
      //   message.error(res.message);
      // }
    });
  };
  const reqOpenResources = () => {
    getOpenResources().then(res => {
      if (res.status === 200) {
        let list = res.data ?? [];
        list = list.slice(0, list.length > 4 ? 4 : list.length);
        let listNew = res.data ?? [];
        listNew = listNew.slice(4, 8);
        setOpenResourcesNew(listNew);
        setOpenResources(list);
      }
      //  else {
      //   message.error(res.message);
      // }
    });
  };
  const reqLatestResources = () => {
    getLatestResources().then(res => {
      if (res?.status === 200) {
        let list = res.data.map((item: any) => item.entityData) ?? [];
        // list = list.slice(0, list.length > 5 ? 5 : list.length);
        setLatestResources(list);
      }
      //  else {
      //   message.error(res.message);
      // }
    });
  };

  const reqCourses = () => {
    getCourse().then(res => {
      if (res?.status === 200) {
        let list = res.data
          ?.map((item: any) => item.entityData)
          ?.sort((a: any, b: any) => b.publishStatus - a.publishStatus)
          .sort((a: any, b: any) => (b.publishStatus != 1 ? -1 : 0)); //不为1的状态放到最后
        // list = list?.slice(0, list.length > 5 ? 5 : list.length);
        setCourses(list);
      }
      //  else {
      //   message.error(res.message);
      // }
    });
  };

  const getCoursePermissions = () => {
    getUserJurisdictionV2().then(res => {
      if (
        res &&
        res?.errorCode === 'success' &&
        typeof res.extendMessage.moduleFeatures === 'object'
      ) {
        const list = Object.keys(res.extendMessage.moduleFeatures)
          .map(key => res.extendMessage.moduleFeatures[key])
          .flat(2);

        setJurisdictionList(list);
      }
    });
  };

  // 百纳秒转时间
  const _100nsTos = (ns: number, framerate?: number): string => {
    return ns ? timecodeconvert.l100Ns2Tc$1(ns, framerate) : '';
  };

  // 隐藏右侧课表
  const hideRight = () => {
    try {
      const container = document.querySelector(
        '.content-container',
      ) as HTMLDivElement;
      const leftNode = document.querySelector(
        '.content-left',
      ) as HTMLDivElement;
      const rightNode = document.querySelector(
        '.content-right',
      ) as HTMLDivElement;
      const calendarNode = document.querySelector(
        '.calender-container',
      ) as HTMLDivElement;
      if (leftNode && rightNode && calendarNode && container) {
        let width = 0;
        if (!isFold) {
          // 折叠
          leftNode.style.width = '99%';
          rightNode.style.width = '1%';
          rightNode.style.minWidth = '0';
          calendarNode.style.display = 'none';
          width = container.clientWidth - 60;
        } else {
          leftNode.style.width = '62%';
          rightNode.style.width = '38%';
          rightNode.style.minWidth = '280px';
          calendarNode.style.display = 'block';
          width = (container.clientWidth - 40) * 0.62;
        }
        leftNode.style.transition = 'width 0.5s ease';
        rightNode.style.transition = 'width 0.5s ease, min-width 0.5s ease';
        calendarNode.style.transition = 'display 0.5s ease';
        setIsFold(!isFold);
        setShowCardCount(Math.floor(width / 218));
        setShowSourceCount(Math.floor(width / 220));
        setShowToolsCount(Math.floor(width / 104));
      }
    } catch (error) { }
  };

  // 搜索学期
  const handleSesmster = (key: any) => {
    setCurSemester(key);
    setLoadingList(true);
    // 取消之前的请求
    listReqAbort.current && listReqAbort.current()
    getSpocCourse({
      pageIndex: 1,
      pageSize: 20,
      courseType: getIndex,
      personalMode: isSuper,
      semester_teaching_courses: key,
    }, (c:any) => listReqAbort.current = c).then((res: any) => {
      if (res && res.status === 200) {
        let list = res.data.results
          ?.map((item: any) => item.entityData)
          ?.sort((a: any, b: any) => b.publishStatus - a.publishStatus)
          .sort((a: any, b: any) => (b.publishStatus != 1 ? -1 : 0));
        setCourses(list);
        setLoadingList(false);
      }
    });
  };

  // 点击我的资源跳转
  const onClickMyResource = (data: any) => {
    if (data.type_ == 'folder') {
      const href = '/rman/#/basic/rmanCenterList';
      sessionStorage.setItem('my_resource_contentId', data.contentId_);
      window.open(href);
    } else {
      window.open(`/rman/#/basic/rmanDetail/${data.contentId_}?showArrow=true`);
    }
  };

  /** 快捷操作 */
  const quickOpreationNode = (
    <Panel
      key="resource_manage"
      icon="iconziyuanku1"
      header={t('快捷操作')}
      width="100%"
    >
      <div className="shortcutModule">
        {isSHTech
          ? SHTechShortcutModule.map((item, index) => {
            return (
              <div
                className="moduleSet"
                key={index}
                onClick={() => {
                  window.open(item.link);
                }}
                style={{ width: isSHTech ? '22%' : '18%' }}
              >
                <div className="icon">
                  <Icon component={item.icon}></Icon>
                </div>
                <div className="setContent">{t(item.text)}</div>
              </div>
            );
          })
          : ShortcutModule.map((item, index) => {
            if (item.text == '更多>>') {
              return (
                <div
                  className="moduleSetMore"
                  key={index}
                  onClick={() => {
                    message.info('敬请期待');
                  }}
                >
                  <div className="setContent">{t(item.text)}</div>
                </div>
              );
            } else {
              const getSvg = (index: number) => {
                if (index == 1) {
                  return (
                    <Icon component={home1} width={30} height={30}></Icon>
                  );
                } else if (index == 2) {
                  return (
                    <Icon component={home2} width={60} height={60}></Icon>
                  );
                } else if (index == 3) {
                  return <Icon component={home3}></Icon>;
                } else if (index == 4) {
                  return <Icon component={home4}></Icon>;
                } else if (index == 5) {
                  return <Icon component={home5}></Icon>;
                }
              };
              return (
                <div
                  className="moduleSet"
                  key={index}
                  onClick={() => {
                    window.open(item.link);
                  }}
                >
                  <div className="icon"> {getSvg(index + 1)}</div>
                  <div className="setContent">{t(item.text)}</div>
                </div>
              );
            }
          })}
      </div>
    </Panel>
  );

  /** 教学助手 */
  const teachAssistant = (
    <Panel
      key="assistant1"
      icon="iconw_jiaoxue"
      header={t('教学助手')}
      width="49%"
    >
      <div className="assistant-container">
        <Badge key="1" count={getTotal ?? 10}>
          <img
            src={shenhe}
            alt={t('审核')}
            onClick={() => {
              if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
                message.info(t('暂不支持手机端，请前往电脑端操作'));
                return;
              }
              window.open('#/reviewcenter/process'); //屏蔽跳转
            }}
          />
          <div className="text">{t('审核')}</div>
        </Badge>
        <Badge count={courseAssistant.homeworkTotal ?? 0}>
          <img
            src={zuoye}
            alt={t('作业')}
            onClick={() => {
              if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
                message.info(t('暂不支持手机端，请前往电脑端操作'));
                return;
              }
              if (!courseAssistant.homeworkLinkUrl) return;
              window.open(courseAssistant.homeworkLinkUrl);
            }}
          />
          <div className="text">{t('作业')}</div>
        </Badge>
        <Badge count={courseAssistant.questionTotal ?? 0}>
          <img
            src={wenda}
            alt={t('问答')}
            onClick={() => {
              if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
                message.info(t('暂不支持手机端，请前往电脑端操作'));
                return;
              }
              if (!courseAssistant.questionLinkUrl) return;
              window.open(courseAssistant.questionLinkUrl);
            }}
          />
          <div className="text">{t('问答')}</div>
        </Badge>
      </div>
    </Panel>
  );

  /** 我的工具 */
  const myTools = (
    <Panel
      key="assistant"
      icon="iconw_jiaoxue"
      header={t('我的工具')}
      width={isSHTech ? '100%' : '49%'}
    >
      <div
        className="assistant-container"
        style={{ margin: isSHTech ? '15px 0' : 0 }}
      >
        {myToolsList.slice(0, showToolsCount).map((item, index) => {
          if (item.isShow) {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <img
                  src={item.iconSrc}
                  width={item.width}
                  height={item.height}
                  style={{
                    background: item.bgColor,
                    borderRadius: '5px',
                    padding: index > 2 ? '6px' : '0',
                  }}
                  alt={t(item.alt)}
                  onClick={() => {
                    if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
                      message.info(t(notSupportMessage));
                      return;
                    }
                    if (item.openUrl) {
                      window.open(item.openUrl);
                    }
                  }}
                />
                <div className="text">{t(item.name)}</div>
              </div>
            );
          }
        })}
      </div>
    </Panel>
  );

  /** 登录欢迎标语 */
  const welcomeNode = (
    <div className="welcome-teach">
      <div className="welcome-container" style={{ display: 'none' }}>
        <div className="box">
          <div className="welcome">
            {t('您好！')}
            {userInfo.nickName}
          </div>
          <p>
            {t('本月您已登录')}
            <span>{welcome.loginTimes ?? 0}</span>
            {t('次')}
          </p>
          <p>
            {t('本学期您有')}
            <span>{welcome.reviewTimes ?? 0}</span>
            {t('门在线课堂，您新建了')}
            <span>{welcome.courseTimes ?? 0}</span>
            {t('门课程')}
          </p>
          <p
            dangerouslySetInnerHTML={{
              __html: t(
                '本周您有新录播资源{name}个',
                `<span>${welcome.recordResources ?? 0}</span>`,
              ),
            }}
          ></p>
        </div>
        <div className="calendar-icon">
          <IconFont type="iconcalendar" onClick={() => setCalendarShow(true)} />
        </div>
      </div>
      {!isSHTech && teachAssistant}
      {myTools}
    </div>
  );

  /** 我的课程 */
  const myCourseNode = (
    <div className="panel-container1" style={{ width: '100%' }}>
      <div className="header">
        <div className="header-left">
          <div className="box"></div>
          <div className="text1">{t('我的课程')}</div>
          {isSHTech ? (
            <div className="my_select">
              <Select
                size="small"
                defaultValue={curSemester}
                value={curSemester}
                onChange={handleSesmster}
              >
                {courseSemester.map((item: any) => (
                  <Select.Option key={item.id} value={item.name}>
                    {item.showSemester || item.name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          ) : (
            <div className="tabs">
              <div
                className={`tab  ${getIndex === 0 ? 'active' : ''}`}
                onClick={() => {
                  setIndex(0);
                }}
              >
                {t('全部')}
              </div>
              <div
                className={`tab  ${getIndex === 2 ? 'active' : ''}`}
                onClick={() => {
                  setIndex(2);
                }}
              >
                {t('班级课')}
              </div>
              <div
                className={`tab  ${getIndex === 3 ? 'active' : ''}`}
                onClick={() => {
                  setIndex(3);
                }}
              >
                {t('微课')}
              </div>
              <div
                className={`tab  ${getIndex === 1 ? 'active' : ''}`}
                onClick={() => {
                  setIndex(1);
                }}
              >
                {t('公开课')}
              </div>
            </div>
          )}
        </div>
        {addCourseList.filter((item: any) =>
          jurisdictionList?.includes(item.showKey),
        ).length > 0 ||
          (isSHTech &&
            (window.innerWidth > 768 ? (
              <Button
                type="link"
                onClick={() => {
                  window.open('/learn/workbench/#/course');
                }}
              >
                {t('查看全部')}
                <RightCircleOutlined />
              </Button>
            ) : (
              <RightOutlined className="more-icon" />
            )))}
      </div>
      <div className="content">
        <Spin spinning={getLoadingList}>
          <div
            className="course-list-container"
            style={{
              justifyContent: courses.length ? '' : 'center',
              margin: isSHTech ? '15px 0' : 0,
            }}
          >
            {isSHTech ? (
              <>
                {courses.length > 0 ? (
                  courses
                    .filter((item: any, index: number) => index < showCardCount)
                    .map((item: any) => (
                      <CourseCard key={item.contentId_} data={item} />
                    ))
                ) : (
                  <Empty description={t('暂无课程')} />
                )}
              </>
            ) : (
              <>
                {courses.length > 0 ? (
                  courses
                    .filter((item: any, index: number) => index < showCardCount)
                    .map((item: any) => (
                      <CourseCard key={item.contentId_} data={item} />
                    ))
                ) : (
                  <div className="empty-course">
                    {addCourseList
                      .filter((item: any) =>
                        jurisdictionList?.includes(item.showKey),
                      )
                      .map((item: any) => (
                        <AddIcon
                          key={item.key}
                          link={item.link}
                          text={item.text}
                        />
                      ))}
                  </div>
                )}
              </>
            )}
          </div>
        </Spin>
      </div>
    </div>
  );

  /** 系统通知 */
  const systemNotify = (
    <Panel
      key="resource"
      icon="iconziyuanku1"
      header={t('系统通知')}
      width="100%"
    >
      <div className="messageBox">
        {data.length ? (
          <Spin spinning={loading}>
            <VirtualList
              data={data}
              height={containerHeight}
              itemHeight={47}
              itemKey="email"
              onScroll={onScroll}
            >
              {(item: any) => {
                return (
                  <div
                    className="messageContent"
                    onClick={() => {
                      window.open(item.linkUrl);
                    }}
                    key={item.id}
                  >
                    <div
                      className={`type ${getType(item.messageType) == t('资源')
                        ? 'color2'
                        : getType(item.messageType) == t('课程')
                          ? 'color3'
                          : 'color1'
                        }`}
                    >
                      {getType(item.messageType)}
                    </div>
                    <div className="title">{item.title}</div>
                    <div className="content">{item.content}</div>
                  </div>
                );
              }}
            </VirtualList>
          </Spin>
        ) : (
          <Empty description={t('暂无通知')} />
        )}
      </div>
    </Panel>
  );

  /** 我的资源 */
  const myResourceNode = (
    <div className="panel-container1" style={{ width: '100%' }}>
      <div className="header">
        <div className="header-left">
          <div className="box"></div>
          <div className="text1">{t('我的资源')}</div>
        </div>
        {window.innerWidth > 768 ? (
          <Button
            type="link"
            onClick={() => {
              sessionStorage.removeItem('my_resource_contentId');
              window.open('/rman/#/basic/rmanCenterList');
            }}
          >
            {t('查看全部')}
            <RightCircleOutlined />
          </Button>
        ) : (
          <RightOutlined className="more-icon" />
        )}
      </div>
      <div className="content">
        <Spin spinning={resLoading}>
          <div
            className="resource_list"
            style={{
              margin: '15px 0',
              justifyContent: myResList.length > 0 ? '' : 'center',
            }}
          >
            {myResList.length > 0 ? (
              myResList.slice(0, showSourceCount).map((item: any) => {
                return (
                  <div
                    className="resource_item"
                    onClick={() => onClickMyResource(item)}
                  >
                    <div className="cover">
                      <img
                        src={item.keyframe_}
                        alt=""
                        style={{
                          maxWidth: item.type_ == 'folder' ? '58px' : '100%',
                          maxHeight: item.type_ == 'folder' ? '58px' : '100%',
                        }}
                      />
                      {item.duration && (
                        <>
                          <div className="img_mask"></div>
                          <div className="time1">
                            {_100nsTos(item.duration, item.framerate)}
                          </div>
                        </>
                      )}
                      {item.fileext && (
                        <div className={`time2 ${item.type_}`}>
                          <span>{item.fileext}</span>
                        </div>
                      )}
                    </div>
                    <Tooltip title={item.name_ || item.name}>
                      <div className="name">{item.name_ || item.name}</div>
                    </Tooltip>
                  </div>
                );
              })
            ) : (
              <Empty description={t('暂无资源')} />
            )}
          </div>
        </Spin>
      </div>
    </div>
  );
  return (
    <div
      className={`home-page-container ${parameterConfig.target_customer ===
        CUSTOMER_NPU && 'homepage-npu'}`}
    >
      {parameterConfig.target_customer === CUSTOMER_NPU ? (
        <NPUHeader />
      ) : (
        <Header />
      )}
      {/* todo: 添加导航栏 */}
      {isCxd ? <HomeNav /> : <div className="home-box">
        {(parameterConfig.target_customer != null &&
          parameterConfig.target_customer !== CUSTOMER_NPU && parameterConfig.target_customer !== CUSTOMER_CXD) && <LeftMenu />}
        {/*<LeftMenu/>*/}
        <div className="content-container">
          {isSHTech ? (
            <>
              <div className="content-left">
                {(parameterConfig.target_customer == 'CQNU' ||
                  parameterConfig.target_customer == 'cqnu') && (
                    <Panel
                      key="resource_manage"
                      icon="iconziyuanku1"
                      header={t('个人数据')}
                      width="100%"
                    >
                      <div className="shortcutModuleCqsf">
                        {ShortcutModuleCqsf.map((item, index) => {
                          return (
                            <div
                              className="moduleSet"
                              key={index}
                              onClick={() => {
                                // window.open(item.link);
                              }}
                            >
                              <div className="num"> {item.num}</div>
                              <div className="setContent">{item.text}</div>
                            </div>
                          );
                        })}
                      </div>
                    </Panel>
                  )}

                {quickOpreationNode}
                <div></div>
                {myCourseNode}
                {myResourceNode}
                {myTools}
                {systemNotify}
              </div>
              <div className="content-right">
                <Tooltip title={isFold ? '展开' : '收起'}>
                  <div className="fold-icon" onClick={hideRight}>
                    {isFold ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
                  </div>
                </Tooltip>
                <ClassSchedule
                  terms={[{ start: '2022-09-01', end: '2023-01-15' }]}
                />
              </div>
            </>
          ) : (
            <>
              <div className="content-left">
                { parameterConfig.target_customer !== CUSTOMER_HNLG && quickOpreationNode}
                {welcomeNode}
                {myCourseNode}
                {systemNotify}
              </div>
              <div className="content-right">
                <div className="calender-container">
                  <ClassSchedule
                    terms={[{ start: '2022-09-01', end: '2023-01-15' }]}
                  />
                </div>
              </div>
              <Drawer
                className="calendar-drawer"
                placement="bottom"
                title={t('日程表')}
                height="auto"
                onClose={() => setCalendarShow(false)}
                open={calendarShow}
              >
                <Calendar
                  terms={[{ start: '2022-09-01', end: '2023-01-15' }]}
                />
              </Drawer>
            </>
          )}
        </div>
      </div>}

    </div>
  );
};

export default HomePage;
