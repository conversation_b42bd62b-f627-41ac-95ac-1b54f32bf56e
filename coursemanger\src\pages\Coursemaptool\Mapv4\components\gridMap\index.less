.mapv4_x6_view {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    // 这里是连线流动效果的css样式 用的时候可以取消注释
    @keyframes ant-line {
        to {
            stroke-dashoffset: -1000
        }
    }

    .mapfunction {
        position: absolute;
        width: 32px;
        top: 45%;
        left: 20px;
        display: flex;
        flex-wrap: wrap;
    }

    .minmap_box {
        flex: 1;
        position: absolute;
        background: #FFFFFF;
        border-radius: 6px;
        bottom: 20px;
        left: 20px;
        overflow: hidden;
    }

    .numerical_box{
        position: absolute;
        bottom: 15px;
        width: 575px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;

        .item_box{
            width: auto;
            display: flex;
            align-items: center;
            justify-content: center;

            .box{
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #56B800;
            }

            span{
                font-weight: 500;
                font-size: 14px;
                color: #333333;
                margin-left: 5px;
            }
        }
    }

    .map_canvas {
        width: 100% !important;
        height: 100% !important;
        flex: 1;

        .module_node{
            position: relative;
            width: 210px;
            height: 100%;
            border: 4px solid #fff;
            border-radius: 35px;
            text-align: center;
            background-color: #6C98FF;
            cursor: pointer;
            margin-left: -70px;

            .name{
                width: 80%;
                margin-left: 10%;
                height: 50%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #fff;


                span{

                    &:first-child{
                        width: 70%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                }

            }

            .time{
                width: 80%;
                margin-left: 10%;
                height: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
            }

            .tag_view{
                position: absolute;
                bottom: -30px;
                width: 100%;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: space-evenly;
                
                .tag_node{
                    width: 17px;
                    height: 17px;
                    background-color: rgb(255, 88, 84);
                    border-radius:50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 8px;
                    color: #fff;
                }
            }
        }

        .node_start_view{
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .node_bg{
                position: absolute;
                width: 100%;
                height: auto;
            }

            .span_number {
                position: absolute;
                font-weight: bold;
                font-size: 24px;
                color: #FFFFFF;
            }

            .name {
                position: absolute;
                min-width: 100px;
                max-width: 250px;
                text-align: center;
                bottom: -35px;
                font-weight: 400;
                font-size: 14px;
                color: #1148A0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

            }
        }

        .node_fenlei_view {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .node_bg{
                position: absolute;
                width: 120%;
                height: auto;
            }

            .span_number {
                position: absolute;
                font-weight: bold;
                font-size: 24px;
                color: #FFFFFF;
            }

            .expanded_view{
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-wrap: wrap;
                align-content: center;

                .minus_btn{
                    z-index: 2;
                    background-color: #FFFFFF;
                    width: 22px;
                    height: 5px;
                }

                .children_number {                    
                    position: absolute;
                    bottom: 10px;
                    z-index: 2;
                    font-weight: bold;
                    font-size: 16px;
                    color: #FFFFFF;
                }

            }


            .name {
                position: absolute;
                min-width: 100px;
                max-width: 250px;
                text-align: center;
                bottom: -35px;
                font-weight: 400;
                font-size: 14px;
                color: #1148A0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

            }
        }

        .node_zhishidian_view {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            .node_bg{
                position: absolute;
                width: 117%;
                height: auto;
            }

            .span_number {
                position: absolute;
                font-weight: bold;
                font-size: 18px;
                color: #FFFFFF;
            }

            .name_view {
                min-width: 100px;
                max-width: 250px;
                height: 24px;
                background-color: rgba(0, 0, 0, 0.3);
                position: absolute;
                text-align: center;
                bottom: -40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 12px;


                span {
                    font-weight: 400;
                    font-size: 14px;
                    color: #FFFFFF;
                    padding-left: 10px;
                    padding-right: 10px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    cursor: pointer;
                }
            }

            .tag_view{
                position: absolute;
                bottom: -65px;
                width: 100px;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: space-evenly;

                .tag_node{
                    width: 17px;
                    height: 17px;
                    background-color: rgb(255, 88, 84);
                    border-radius:50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 8px;
                    color: #fff;
                }
            }
        }
    }
}
