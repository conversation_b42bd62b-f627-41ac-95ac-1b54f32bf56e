
export default function  useCkeckRoles (roles:[]){
    if(roles.length){
        // console.log('当前用户角色：',roles);
        // 各种管理角色判断
        const rolemanager = ["r_sys_manager","r_currency_agent_manager","r_course_manager","r_teacher","public_space_role","r_resource_manager","r_teaching_manager","r_agent_manager","r_supervisor","r_teacher_assistant"];
        // 判断当前的传入的角色是否是其中一个
        const ishash = roles.some((item:string) => rolemanager.includes(item));
        if(!ishash) {
            // 跳转到未授权
            window.location.href = '/unifiedplatform/#/not_authority';
        }
    }
}


export const checkHashRool = (userinfo:any ,roles:string[]) => { 
    if(userinfo && userinfo.roles?.length && roles.length){
        return userinfo.roles.some((item: any) => roles.includes(item.roleCode))
    }else{
        return false;
    }

};