import React, { FC, useState, useEffect, useRef } from 'react';
import './index.less';
import { useSelector, useDispatch, useLocation } from 'umi';
import {
  Form,
  Input,
  Button,
  Select,
  Checkbox,
  Tooltip,
  DatePicker,
  message,
  Table,
  Space,
  Modal,
  Image,
  Pagination,
  Menu,
  Dropdown,
  Radio,
  Popover
} from
  'antd';
import { PlusCircleFilled, EllipsisOutlined } from '@ant-design/icons';
import {
  nweMoocCourse,
  getMoocList,
  deleteTemplateCourse,
  releaseCourse,
  offShelfCourse,
  exportword
} from
  '@/api/mooclist';
import { IconFont } from '@/components/iconFont';
import TemplateSearch from '@/components/TemplateSearch';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import courseTemplate from '@/api/courseTemplate';
import { ColumnsType } from 'antd/es/table';
import perCfg from '@/permission/config';
import { updateQuote } from '@/api/homework';
import { deleteTempHomeworks } from '@/api/homework';
import MobileSearch from '@/components/SearchForm/mobileSearch';
import TemplateCard from './components/TemplateCard';
import useLocale from '@/hooks/useLocale';
import TplShareModal from '@/components/TemplateShareModal';

const { RangePicker } = DatePicker;
const { confirm } = Modal;

const ellipsisSetting = (width: Number) => {
  return {
    style: {
      maxWidth: width,
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      cursor: 'pointer'
    }
  };
};

const CourseTemplate: FC<{}> = () => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newCourseName, setNewCourseName] = useState('');
  const [modeSwitch, setModeSwitch] = useState<boolean>(
    JSON.parse(localStorage.getItem('learn_view_mode') || '{}').
      coursetemplatelist != '0');
  // 视图切换

  const [checkedList, setCheckedList] = useState<Array<any>>([]); //选中的列表
  const [allList, setAllList] = useState<Array<any>>([]); //总的的列表
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);

  const [myOrShare, setMyOrShare] = useState<boolean>(true);
  const [operationData, setOperationData] = useState<any>();
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(true);

  const [release, setRelease] = useState<boolean>(false);
  const [releaseOrNot, setReleaseOrNot] = useState<boolean>(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false); //删除弹窗开关
  const [iscopyModalVisible, setIscopyModalVisible] = useState(false); //复制弹窗的开关
  const [deleteLoading, setDeleteLoading] = useState(false); //删除弹窗loading
  const [copyLoading, setCopyLoading] = useState(false); //复制弹窗loading
  const [updateLoading, setUpdateLoading] = useState(false);

  const [selectCopyitem, setSelectCopyitem] = useState<any>(null);
  const [tplShareVisible, setTplShareVisible] = useState<boolean>(false);
  //撤销
  const [remove,setRemove] = useState(false)
  const [removeLoading,setRemoveLoading] = useState(false)
  const handleRemoveOK = () => {
    setRemoveLoading(true)
    courseTemplate.releasestatus2({ contentId: operationData.contentId_,state: 2 }).then((res:any) => {
      if (res.status === 200) {
        message.success('撤回成功')
        getList()
      }  else {
        message.error('撤回失败')
      }
    }).catch(() => {
      message.error('撤回失败')
    }).finally(() => {
      setRemoveLoading(false)
      setRemove(false)
    })

  }
  const jurisdictionList = useSelector<{ jurisdiction: any; }, any>(
    ({ jurisdiction }) => {
      return jurisdiction.jurisdictionList;
    });

  useEffect(() => {
    const temp = localStorage.getItem('learn_view_mode') || '{}';
    const value = {
      ...JSON.parse(temp),
      coursetemplatelist: modeSwitch ? '1' : '0'
    };
    localStorage.setItem('learn_view_mode', JSON.stringify(value));
  }, [modeSwitch]);
  let searchobj: any = useRef({
    name: null,
    status: null,
    time: [],
    college: null,
    major: null,
    teacher: null
  });
  // const [searchobj,setSearch] = useState<any>({
  //     name:null,
  //     status:null,
  //     time:[]
  // })

  const [total, setTotal] = useState(0);
  // const [pageIndex, setPageIndex] = useState<number>(1);
  let pageIndex = useRef(1);
  const [size, setSize] = useState<number | undefined>(24); // 页码
  const [releaseDis, setReleaseDis] = useState(true);
  const [offShelfDis, setOffShelfDis] = useState(true);

  const [releaseValue, setReleaseValue] = useState<string>('no');
  const { mobileFlag, leftRightVisible } = useSelector<{ config: any; }, any>(
    ({ config }) => config);

  const [isQuoteModalVisible, setIsQuoteModalVisible] = useState(false);
  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const [sortData, setSortData] = useState<string>('createDate_,0');
  useEffect(() => {
    console.log(location.pathname)
    console.log(location.pathname === '/coursetemplate/mytemplate')
    setMyOrShare(location.pathname === '/coursetemplate/mytemplate');
    getList();
  }, []);
  const changenewinput = (e: any) => {
    setNewCourseName(e.target.value);
  };
  useEffect(() => {
    let newSelectedRows: any = [];
    allList.forEach((item) => {
      if (checkedList.includes(item.contentId_)) {
        newSelectedRows.push(item);
      }
    });
    let offshelf = newSelectedRows.some((item: any) => {
      return item.release_type !== "public";
    });
    setOffShelfDis(offshelf);
    let release = newSelectedRows.some((item: any) => {
      return item.release_type === "public";
    });
    setReleaseDis(release);
  }, [checkedList]);

  const getList = (sortFields: string = '') => {
    let isPublish =
      location.pathname === '/coursetemplate/mytemplate' ? false : true;
    let obj: any = {
      // "keyword": [
      //   "string"
      // ],
      conditions: [],
      // // "needHandleFilePathFields": [
      // //   "string"
      // // ],
      pageIndex: pageIndex.current,
      pageSize: size,
      sortFields: []
    };

    if (searchobj.current.name && searchobj.current.name != null) {
      obj.conditions.push({
        field: 'name',
        searchRelation: 0,
        value: searchobj.current.name
      });
    }

    if (searchobj.current.time && searchobj.current.time.length > 0) {
      obj.conditions.push({
        field: 'createDate',
        searchRelation: 4,
        value: [searchobj.current.time[0].format('yyyy-MM-dd')]
      });

      obj.conditions.push({
        field: 'createDate',
        searchRelation: 6,
        value: [searchobj.current.time[1].format('yyyy-MM-dd')]
      });
    }

    if (searchobj.current.release_type && searchobj.current.release_type != null) {
      obj.conditions.push({
        field: 'release_type',
        searchRelation: 0,
        value: [searchobj.current.release_type]
      });
    }

    if (searchobj.current.college && searchobj.current.college != null) {
      obj.conditions.push({
        field: 'college',
        searchRelation: 0,
        value: [searchobj.current.college.split(',')[1]]
      });
    }

    if (searchobj.current.major && searchobj.current.major != null) {
      obj.conditions.push({
        field: 'major',
        searchRelation: 0,
        value: [searchobj.current.major.split(',')[1]]
      });
    }
    if (
      searchobj.current.editorial_team &&
      searchobj.current.editorial_team != null) {
      obj.conditions.push({
        field: 'editorial_team',
        searchRelation: 0,
        value: [searchobj.current.editorial_team.split(',')[1]]
      });
    }

    if (sortFields != '') {
      obj.sortFields.push({
        field: sortFields.split(',')[0],
        isDesc: sortFields.split(',')[1] == '0' ? true : false
      });
    }
    courseTemplate.getTemplateCatalogue2(isPublish, obj).then((res) => {
      if (res && res.message === 'OK') {
        setAllList(
          res.data.results.map((element: any) => {
            return element?.entityData;
          }));

        setTotal(res.data.total);
      }
    });
  };

  // // 个人模板转共享模板
  // const personaltoshare = (id: string) => {
  //     courseTemplate.personaltoshare(id).then((res) => {
  //         if (res && res.errorCode === 'course_0000_0000') {
  //             message.success('发布成功')
  //         } else {
  //             message.error('发布失败')
  //         }
  //     })
  // }

  // //共享模板转个人模板
  // const sharetopersonal = (id: string) => {
  //     courseTemplate.sharetopersonal(id).then((res) => {
  //         if (res && res.errorCode === 'course_0000_0000') {
  //             message.success('引用成功！')
  //             setIsQuoteModalVisible(false);
  //         } else {
  //             message.error('引用失败')
  //         }
  //     })
  // }

  //
  const [addLoading, setAddLoading] = useState<boolean>(false);
  const handleOk = () => {
    if (addLoading) {
      return false;
    }
    setAddLoading(true);
    courseTemplate.newTemplate(newCourseName).then((res) => {
      if (res && res.message === 'OK') {
        window.open(
          `/learn/workbench/#/tempatedetail/courseInfo?id=${res.data}&type=edit&myOrShare=${myOrShare}&first=1`);

      } else {
        message.error(t('新建失败'));
      }
      getList();
      setIsModalVisible(false);
      setNewCourseName('');
    }).finally(() => {
      setAddLoading(false);
    });
  };

  // 删除
  const handleDeleteOk = () => {
    setDeleteLoading(true);
    let param: any = [];
    if (oneOrBatch) {
      param.push(operationData.contentId_);
    } else {
      param = [...checkedList];
    }
    deleteTemplateCourse(param).then((res) => {
      if (res && res.message === 'OK' && res.data) {
        deleteTempHomeworks(param);
        message.success(t('删除成功'));
      } else {
        message.error(t('删除失败'));
      }
      getList();
      setCheckedList([]);
      setDeleteLoading(false);
      setIsDeleteModalVisible(false);
    });
  };

  // 批量复制模板
  const handleCopyeOk = () => {
    setCopyLoading(true);
    // 单个复制
    if (selectCopyitem) {
      courseTemplate.copytemplate(selectCopyitem).then((res) => {
        if (res && res.message === 'OK') {
          getList();
          setCopyLoading(false);
          setSelectCopyitem(null);
          setIscopyModalVisible(false);
          message.success(t('复制成功'));
        } else {
          getList();
          setCopyLoading(false);
          setSelectCopyitem(null);
          setIscopyModalVisible(false);
          message.error(t('复制失败'));
        }
      });
    } else {
      // 批量复制
      let task_arr: any = [];
      checkedList.map((item: any) => {
        let p1 = new Promise((resolve, reject) => {
          courseTemplate.copytemplate(item).then((res) => {
            if (res && res.message === 'OK') {
              resolve(true);
            } else {
              resolve(false);
            }
          });
        });
        task_arr.push(p1);
      });
      Promise.all(task_arr).
        then((results) => {
          let copy_status = results.some((item) => item);
          if (copy_status) {
            getList();
            setCopyLoading(false);
            setIscopyModalVisible(false);
            message.success(t('复制成功'));
          } else {
            getList();
            setCopyLoading(false);
            setIscopyModalVisible(false);
            message.error(t('复制失败'));
          }
        }).
        catch((err) => {
          console.log(err);
        });
    }
  };

  // 下架&&发布
  const handleReleaseOK = (data?: any) => {
    console.log('data',data)
    setUpdateLoading(true);
    let param: any = [];
    if (oneOrBatch) {
      param.push(operationData.contentId_);
    } else {
      param = [...checkedList];
    }
    console.log('共享了',param)
    if (releaseOrNot) {
      const requestArr = param.map((item: any) => courseTemplate.personaltoshare(item, data,));
      Promise.all(requestArr).then((res) => {
        const success = res.every((item: any) => item.status === 200);
        if (success) {
          message.success(t('共享成功'));
          res.forEach((item: any) => {
            courseTemplate.
              copyhomework({
                orgCourseId: param[0],
                targetCourseId: item.data
              });
          });
          if (!oneOrBatch) {
            setCheckedList([]);
          }
        } else {
          message.error(t('共享失败'));
        }
        getList();
        setUpdateLoading(false);
        setRelease(false);
      });

      // console.log('发布')
      // releaseCourse(param, 1).then(res => {
      //     if (res && res.errorCode === 'course_0000_0000') {
      //         message.success('课程发布成功')
      //     } else {
      //         res && message.error(res.ExtendMessage ? res.ExtendMessage : res.errorMsg)
      //     }
      //     getlist()
      //     setSelectKey([])
      //     setRelease(false)
      // })
    } else {
      courseTemplate.releasestatus(0, param).then((res: any) => {
        if (res && res.message === 'OK') {
          getList();
          message.success(t('取消共享成功！'));
          setUpdateLoading(false);
          setRelease(false);
          if (!oneOrBatch) {
            setCheckedList([]);
          }
        } else {
          getList();
          message.error(t('取消共享失败！'));
          setUpdateLoading(false);
          setRelease(false);
        }
      });
      // offShelfCourse(param).then(res => {
      //     if (res && res.errorCode === 'course_0000_0000') {
      //         message.success('课程下架成功')
      //     }
      //     getlist()
      //     setSelectKey([])
      //     setRelease(false)
      // })
    }
  };
  // 引用公共模板
  const handleQuoteOk = () => {
    let param = [];
    if (oneOrBatch) {
      param.push(operationData.contentId_);
    } else {
      param = [...checkedList];
    }

    let task_arr: any = [];
    param.map((item: any) => {
      let p1 = new Promise((resolve, reject) => {
        courseTemplate.sharetopersonal(item).then((res) => {
          if (res && res.message === 'OK') {
            updateQuote({
              orgCourseId: item,
              targetCourseId: res.data
            });
            resolve(true);
          } else {
            resolve(false);
          }
        });
      });
      task_arr.push(p1);
    });
    Promise.all(task_arr).
      then((results) => {
        let copy_status = results.some((item) => item);
        if (copy_status) {
          getList();
          setIsQuoteModalVisible(false);
          message.success(t('引用成功!'));
        } else {
          getList();
          setIsQuoteModalVisible(false);
          message.error(t('引用失败!'));
        }
      }).
      catch((err) => {
        console.log(err);
      });
  };
  const handleChange = (value: string) => {
    setSortData(value);
    getList(value);
  };
  // 全选
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    // setReleaseDis(false);
    setCheckedList(
      e.target.checked ? allList.map((item) => item.contentId_) : []);

    setIndeterminate(false);
    setCheckAll(e.target.checked);
    setReleaseDis(false);
  };
  // 单选
  const onChange = (check: Array<any>) => {
    setCheckedList(check);
    setIndeterminate(!!check.length && check.length < allList.length);
    setCheckAll(check.length === allList.length);
  };

  // 导出课程word
  const exportWord = () => {
    confirm({
      title: t("导出"),
      content: t("确认导出课程资源包?"),
      onOk() {
        exportword(checkedList, { isTemplate: 1 }).then((res) => {
          setCheckedList([]);
          download(res);
        });
      },
      onCancel() {
        console.log('Cancel');
      }
    });
  };

  // 下载文件
  const download = (blob: any) => {
    // let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });//type是文件类，详情可以参阅blob文件类型
    // 创建新的URL并指向File对象或者Blob对象的地址
    const blobURL = window.URL.createObjectURL(blob);
    // 创建a标签，用于跳转至下载链接
    const tempLink = document.createElement('a');
    tempLink.style.display = 'none';
    tempLink.href = blobURL;
    tempLink.setAttribute('download', '课程资源包.zip');
    // 兼容：某些浏览器不支持HTML5的download属性
    if (typeof tempLink.download === 'undefined') {
      tempLink.setAttribute('target', '_blank');
    }
    // 挂载a标签
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    // 释放blob URL地址
    window.URL.revokeObjectURL(blobURL);
  };

  const columns: ColumnsType<any> | undefined = [
    {
      title: '',
      dataIndex: 'check',
      key: 'check',
      align: 'center',
      width: 50,
      render: (_text: any, record: any) =>
        <Checkbox value={record.contentId_}></Checkbox>

    },
    {
      title: t("课程封面"),
      dataIndex: 'cover',
      key: 'cover',
      align: 'center',
      width: 120,
      render: (text: any) => <Image width={120} src={text} />
    },
    {
      title: t("课程名称"),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 270,
      ellipsis: true,
      onCell: () => (ellipsisSetting(270) as any)
    },
    {
      title: t("适用专业"),
      dataIndex: 'majorName',
      key: 'major',
      align: 'center',
      width: 150,
      ellipsis: true,
      render: (text: any) => text?.join(','),
      onCell: () => (ellipsisSetting(150) as any)
    },
    {
      title: t("学院"),
      dataIndex: 'collegeName',
      key: 'collegeName',
      align: 'center',
      ellipsis: true,
      width: 180,
      render: (text: any) => text?.join(','),
      onCell: () => (ellipsisSetting(270) as any)
    },
    {
      title: t("共享状态"),
      dataIndex: 'release_type',
      key: 'release_type',
      align: 'center',
      width: 150,
      render: (text: any, record: any) =>
        text === "public" ? t("已共享") : ''
    },
    {
      title: t("创建时间"),
      dataIndex: 'createDate_',
      align: 'center',
      width: 200
    },
    {
      title: t("操作"),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 200,
      fixed: 'right',
      render: (_text: any, record: any) => {
        if (myOrShare) {
          return (
            <Space size="middle">
              {jurisdictionList.includes(perCfg.my_tpl_mg) &&
                <>
                  <Tooltip title={t("编辑")}>
                    <IconFont
                      type="iconedit"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        window.open(
                          `/learn/workbench/#/tempatedetail/courseInfo?id=${record.contentId_}&type=edit&myOrShare=${myOrShare}`);

                      }} />

                  </Tooltip>
                  <Tooltip title={t("预览")}>
                    <IconFont
                      type="iconviews"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        window.open(
                          `/learn/workbench/#/tempatedetail/courseInfo?id=${record.contentId_}&type=see&myOrShare=${myOrShare}`);

                      }} />

                  </Tooltip>
                  <Tooltip title={t("复制")}>
                    <IconFont
                      type="iconcopy"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        setSelectCopyitem(record.contentId_);
                        setIscopyModalVisible(true);
                      }} />

                  </Tooltip>
                  {record.release_type !== 'public' &&
                    <Tooltip title={t("删除")}>
                      <IconFont
                        type="icondelete"
                        onClick={(e: any) => {
                          e.stopPropagation();
                          setOneOrBatch(true);
                          setOperationData(record);
                          setIsDeleteModalVisible(true);
                        }} />

                    </Tooltip>}

                </>}

              {jurisdictionList.includes(perCfg.my_tpl_publish) && (
                record.release_type === 'public' ?
                  <Tooltip title={t("取消共享")}>
                    <IconFont
                      type="iconoffShelf"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        setOneOrBatch(true);
                        setOperationData(record);
                        setReleaseOrNot(false);
                        setRelease(true);
                      }} />

                  </Tooltip> :

                  <Tooltip title={t("共享")}>
                    <IconFont
                      type="iconrelease"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        setOneOrBatch(true);
                        setOperationData(record);
                        setReleaseOrNot(true);
                        setRelease(true);
                      }} />

                  </Tooltip>)}

            </Space>);

        } else {
          return (
            <Space size="middle">
              {jurisdictionList.includes(perCfg.public_tpl_browse) &&
                <Tooltip title={t("预览")}>
                  <IconFont
                    type="iconviews"
                    onClick={() =>
                      window.open(
                        `/learn/workbench/#/tempatedetail/courseInfo?id=${record.contentId_}&type=see&myOrShare=${myOrShare}`)} />



                </Tooltip>}

              {jurisdictionList.includes(perCfg.public_tpl_refer) &&
                <Tooltip title={t("引用")}>
                  <IconFont
                    type="iconyinyong"
                    onClick={() => {
                      setOneOrBatch(true);
                      setOperationData(record);
                      setIsQuoteModalVisible(true);
                    }} />

                </Tooltip>}

            </Space>);

        }
      }
    }];

  const getMenu = (item: any) => {
    return (
      <Menu>
        {jurisdictionList.includes(perCfg.my_tpl_publish) && (
          item.publishStatus == 0 ?
            <Menu.Item
              onClick={(e: any) => {
                setOperationData(item);
                setReleaseOrNot(true);
                setRelease(true);
                e.domEvent.stopPropagation();
              }}>

              <IconFont type="iconrelease" />{t("共享")}

            </Menu.Item> :

            <Menu.Item
              onClick={(e: any) => {
                setOperationData(item);
                setReleaseOrNot(false);
                setRelease(true);
                e.domEvent.stopPropagation();
              }}>

              <IconFont type="iconoffShelf" />{t("取消共享")}

            </Menu.Item>)}

        {jurisdictionList.includes(perCfg.my_tpl_mg) &&
          <>
            <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                if (mobileFlag) {
                  message.info(t('暂不支持手机端，请前往电脑端操作'));
                } else {
                  window.open(
                    `/learn/workbench/#/tempatedetail/courseInfo?id=${item.contentId_}&type=edit&myOrShare=${myOrShare}`);

                }
              }}>

              <IconFont type="iconedit" />{t("编辑")}

            </Menu.Item>
            <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                if (mobileFlag) {
                  message.info(t('暂不支持手机端，请前往电脑端操作'));
                } else {
                  window.open(
                    `/learn/workbench/#/tempatedetail/courseInfo?id=${item.contentId_}&type=see&myOrShare=${myOrShare}`);

                }
              }}>

              <IconFont type="iconviews" />{t("预览")}

            </Menu.Item>
            <Menu.Item
              onClick={(e: any) => {
                e.domEvent.stopPropagation();
                setSelectCopyitem(item.contentId_);
                setIscopyModalVisible(true);
              }}>

              <IconFont type="iconcopy" />{t("复制")}

            </Menu.Item>

            {item.publishStatus == 0 &&
              <Menu.Item
                onClick={(e: any) => {
                  e.domEvent.stopPropagation();
                  setOperationData(item);
                  setIsDeleteModalVisible(true);
                }}>

                <IconFont type="icondelete" />{t("删除")}

              </Menu.Item>}

          </>}

      </Menu>);

  };
  const onReleaseChange = (e: any) => {
    setReleaseValue(e.target.value);
  };
  let btn_list: any = [];
  if (myOrShare) {
    if (jurisdictionList.includes(perCfg.my_tpl_mg)) {
      btn_list.push({
        title: t("删除"),
        disabled: releaseDis || !checkedList.length,
        func: () => {
          setOneOrBatch(false);
          setIsDeleteModalVisible(true);
        },
        dom:
          <Button
            onClick={() => {
              setOneOrBatch(false);
              setIsDeleteModalVisible(true);
            }}
            type="text"
            ghost
            icon={<IconFont type="icondelete" />}
            disabled={releaseDis || !checkedList.length}>
          </Button>

      });
      btn_list.push({
        title: t("复制"),
        disabled: !checkedList.length,
        func: () => {
          setOneOrBatch(false);
          setIscopyModalVisible(true);
        },
        dom:
          <Button
            onClick={() => {
              setOneOrBatch(false);
              setIscopyModalVisible(true);
            }}
            icon={<IconFont type="iconcopy" />}
            disabled={!checkedList.length}
            type="text"
            ghost>
          </Button>

      });
    }
    if (jurisdictionList.includes(perCfg.my_tpl_publish)) {
      btn_list.push({
        title: t("共享"),
        disabled: releaseDis || !checkedList.length,
        func: () => {
          setReleaseOrNot(true);
          setOneOrBatch(false);
          setTplShareVisible(true);
        },
        dom:
          <Button
            onClick={() => {
              setReleaseOrNot(true);
              setOneOrBatch(false);
              setTplShareVisible(true);
            }}
            type="text"
            ghost
            icon={<IconFont type="iconrelease" />}
            disabled={releaseDis || !checkedList.length}>
          </Button>

      });
      btn_list.push({
        title: t("取消共享"),
        disabled: offShelfDis || !checkedList.length,
        func: () => {
          setReleaseOrNot(false);
          setOneOrBatch(false);
          setRelease(true);
        },
        dom:
          <Button
            onClick={() => {
              setReleaseOrNot(false);
              setOneOrBatch(false);
              setRelease(true);
            }}
            type="text"
            ghost
            icon={<IconFont type="iconoffShelf" />}
            disabled={offShelfDis || !checkedList.length}>
          </Button>

      });
    }
    if (jurisdictionList.includes(perCfg.course_package_export)) {
      btn_list.push({
        title: t("导出"),
        disabled: !checkedList.length,
        func: () => {
          exportWord();
        },
        dom:
          <Button
            type="text"
            ghost
            icon={<IconFont type="iconexport" />}
            // onClick={exportWord}
            disabled={!checkedList.length}>
          </Button>

      });
    }
  } else {
    if (jurisdictionList.includes(perCfg.public_tpl_refer)) {
      btn_list.push({
        title: t("引用"),
        disabled: !checkedList.length,
        func: () => {
          setOneOrBatch(false);
          setIsQuoteModalVisible(true);
        },
        dom:
          <Button
            onClick={() => {
              setOneOrBatch(false);
              setIsQuoteModalVisible(true);
            }}
            disabled={!checkedList.length}
            title={t("引用")}
            icon={<IconFont type="iconyinyong" />}
            type="text"
            ghost>
          </Button>

      });
    }
  }
  return (
    <div className="course_template">
      <div className="search_box">
        <TemplateSearch
          form={form}
          myOrShare={myOrShare}
          mobileFlag={mobileFlag}
          onsousuo={(e: any) => {
            searchobj.current = e;
            getList();
          }} />

      </div>
      <div className="splitLine"></div>
      <div className="change_box">
        <div className={`change_box_left${myOrShare ? ' sharePage' : ''}`}>
          <Checkbox
            indeterminate={indeterminate}
            onChange={onCheckAllChange}
            checked={checkAll}>
            {t("全部")}

          </Checkbox>
          <div className="btn-wrp">
            {myOrShare && jurisdictionList.includes(perCfg.my_tpl_mg) &&
              <Button
                type="primary"
                shape="round"
                icon={<PlusCircleFilled />}
                onClick={() => {
                  if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
                    message.info(t('暂不支持手机端，请前往电脑端操作'));
                    return;
                  }
                  setIsModalVisible(true);
                }}>
                {t("新建")}

              </Button>}

            {!mobileFlag ?
              btn_list.map((item: any, index: number) => {
                return (
                  <div
                    onClick={() => !item.disabled && item.func()}
                    className={`item_${item.disabled ? ' disabled' : ''}`}
                    key={index}>

                    {item.dom}
                    {item.title}
                  </div>);

              }) :

              //移动端取前几个展示即可
              <>
                {btn_list.slice(1, btn_list.length).length === 0 && btn_list.slice(0, 1).map((item: any, index: number) => {
                  return (
                    <div
                      className={`item_${item.disabled ? ' disabled' : ''}`}
                      key={index}>

                      {item.dom}
                    </div>);

                })}
                {btn_list.slice(1, btn_list.length).length > 0 &&
                  <Popover
                    className="mobile_btns_popover"
                    onOpenChange={(newOpen: boolean) =>
                      setOpreatMenuVisible(newOpen)}

                    open={operatMenuVisible}
                    content={
                      <div className="mobile_btns">
                        {btn_list.
                          map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className={item.disabled ? 'disabled' : ''}
                                onClick={() => {
                                  if (!item.disabled) {
                                    setOpreatMenuVisible(false);
                                    item.func();
                                  }
                                }}>

                                {item.dom}
                                {item.title}
                              </div>);

                          })}
                      </div>}>


                    <Button
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setOpreatMenuVisible(!operatMenuVisible);
                      }}>

                      <IconFont type="iconziyuanku1" />{t("管理")}
                    </Button>
                  </Popover>}

              </>}

          </div>
        </div>
        <div className="change_box_right">
          <Select
            style={{ width: 120 }}
            defaultValue="createDate_,0"
            onChange={handleChange}>

            <Select.Option value="createDate_,0">{t("创建时间")}

              <IconFont type="iconchangjiantou-shang" />
            </Select.Option>
            <Select.Option value="createDate_,1">{t("创建时间")}

              <IconFont type="iconchangjiantou-xia" />
            </Select.Option>
            <Select.Option value="name,0">{t("课程名称")}

              <IconFont type="iconchangjiantou-shang" />
            </Select.Option>
            <Select.Option value="name,1">{t("课程名称")}

              <IconFont type="iconchangjiantou-xia" />
            </Select.Option>
          </Select>
          <div onClick={() => setModeSwitch(true)} className="mode_switch">
            <Tooltip title={t("图例模式")}>
              <IconFont
                type="iconhebingxingzhuangfuzhi2"
                className={modeSwitch ? 'active' : ''} />

            </Tooltip>
          </div>
          <div onClick={() => setModeSwitch(false)} className="mode_switch">
            <Tooltip title={t("列表模式")}>
              <IconFont
                type="iconliebiao"
                className={modeSwitch ? '' : 'active'} />

            </Tooltip>
          </div>
        </div>
      </div>
      <div className="content_box">
        <Checkbox.Group
          value={checkedList}
          onChange={onChange}
          style={{ width: '100%' }}>

          {modeSwitch ?
            <div className="thumbnail_box">
              {allList.map((item) =>
                <TemplateCard
                  key={item.contentId_}
                  tpl={item}
                  showBtn
                  myOrShare={myOrShare}
                  onRemove={() => {
                    setOperationData(item);
                    setRemove(true)
                  }}
                  onReference={() => {
                    setOneOrBatch(true);
                    setOperationData(item);
                    setIsQuoteModalVisible(true);
                  }}
                  onPublish={() => {
                    setOperationData(item);
                    setReleaseOrNot(true);
                    setTplShareVisible(true);
                  }}
                  onUnPublish={() => {
                    setOperationData(item);
                    setReleaseOrNot(false);
                    setRelease(true);
                  }}
                  onCopy={() => {
                    setSelectCopyitem(item.contentId_);
                    setIscopyModalVisible(true);
                  }}
                  onDelete={() => {
                    setOperationData(item);
                    setIsDeleteModalVisible(true);
                  }} />)}


            </div> :

            <Table
              dataSource={allList}
              size="small"
              columns={columns}
              pagination={false}
              rowKey="contentId_"
              scroll={{ x: 'max-content', y: 'calc(100vh - 325px)' }} />}


        </Checkbox.Group>
        {/* <Pagination
           showSizeChanger={false}
           showQuickJumper={true}
           showTotal={total => `共 ${Math.ceil(total / 20)} 页`}
           defaultCurrent={1}
           current={pageIndex.current}
           defaultPageSize={20}
           total={total}
           onChange={page => {
             pageIndex.current = page;
             getList();
           }}
          /> */}
        <Pagination
          style={{ textAlign: 'center', marginBottom: 24 }}
          {...{
            size: 'small',
            showQuickJumper: true,
            showTotal: (total) => t("共{name}条", String(total)),
            current: pageIndex.current,
            total: total,
            pageSize: size,
            showSizeChanger: true,
            pageSizeOptions: ['24', '36', '48', '60'],
            onChange: (page, size) => {
              pageIndex.current = page;
              setSize(size);
              getList();
            }
          }} />

      </div>
      <Modal
        title={t("添加课程")}
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={() => {
          setIsModalVisible(false);
          setNewCourseName('');
        }}
        className="Mooc-addcourse"
        confirmLoading={addLoading}>

        <label>{t("课程名称：")}

          <Input
            style={{ width: 300 }}
            placeholder={t("请输入课程名称")}
            value={newCourseName}
            onChange={changenewinput}
            onKeyDown={(e: any) => {
              e.keyCode === 13 && handleOk();
            }} />

        </label>
      </Modal>
      <Modal
        title={t("引用")}
        visible={isQuoteModalVisible}
        onOk={handleQuoteOk}
        onCancel={() => setIsQuoteModalVisible(false)}>
        {t("确定要引用该课程模板吗？")}

      </Modal>
      <Modal
        title={t("删除")}
        visible={isDeleteModalVisible}
        onOk={handleDeleteOk}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteLoading}>
        {t("确定要删除该课程吗？")}

      </Modal>
      <Modal
        title={releaseOrNot ? t("共享") : t("取消共享")}
        visible={release}
        onOk={handleReleaseOK}
        onCancel={() => setRelease(false)}
        confirmLoading={updateLoading}>

        {/* {releaseOrNot ? (
           <div>
             <h3>请确认发布状态</h3>
             <Radio.Group onChange={onReleaseChange} value={releaseValue}>
               <Radio value="share">共享</Radio>
               <Radio value="no">不共享</Radio>
             </Radio.Group>
           </div>
          ) : (

          )} */}
        {`${t("确定要")}${releaseOrNot ? t("共享") : t("取消共享")}${t("该课程")}`}
      </Modal>
      <TplShareModal visible={tplShareVisible} onClose={() => setTplShareVisible(false)} onConfirm={handleReleaseOK} />

      <Modal
        title={t("复制")}
        visible={iscopyModalVisible}
        onOk={handleCopyeOk}
        onCancel={() => setIscopyModalVisible(false)}
        confirmLoading={copyLoading}>
        {t("确定要复制课程吗？")}

      </Modal>
      <Modal
        title={t("撤回")}
        visible={remove}
        onOk={handleRemoveOK}
        onCancel={() => setRemove(false)}
        confirmLoading={removeLoading}>
        {t("确定撤回课程资源包？")}
      </Modal>
    </div>);

};

export default CourseTemplate;
