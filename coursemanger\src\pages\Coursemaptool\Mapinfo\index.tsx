import React, { FC, useState, useEffect, useRef } from 'react';
import './index.less';
import { useSelector, useDispatch, useLocation } from 'umi';
import {
  Form,
  Input,
  Button,
  Select,
  TreeSelect,
  message
} from
  'antd';
import { updatemapinfo, getResourceIdsByMapId,getMapCourse } from '@/api/coursemap';
import { queryColleges, metaData } from "@/api/course";
import TagInput from '@/components/AddCourseForm/TagInput';
import { getLabels, getSensitiveWord } from '@/utils';
import useLocale from '@/hooks/useLocale';
import TextArea from 'antd/lib/input/TextArea';

const Mapinfo: FC<{mapid: string}> = ({mapid}) => {
  const { t } = useLocale();
  // form表单
  const [form] = Form.useForm();
  // 获取url参数
  const { query }: any = useLocation();
  const [courseSubjectList, setcourseSubjectList] = useState<any>([]);
  // 专业
  const [major, setMajor] = useState<any>([]);
  const dispatch = useDispatch();
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any; }; }) => state.coursemap.mapinfo);
  const [savebtn, setSavebtn] = useState<any>(false);

  // 用户选择的专业
  const [selectmajor, setSelectmajor] = useState<any>(null);

  const subjectObject = useRef<any>({
    primary_classification: '',
    secondary_classification: ''
  });

  const [tagLabels, setTagLabels] = useState<string[]>([]);
  const [colleges, setColleges] = useState<any[]>([]);

  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  // 所有关联的课程
  const [courseList, setCourseList] = useState<any>([]);

  const getcourse = (mapId: any) => {
    getMapCourse({
      mapId: mapId
    }).then((res) => {
      if(res.data.status === 200){
        setCourseList(res.data.data);
      }
    })
  }

  function beforeunload(e: any) {
    let confirmationMessage = t("你确定离开此页面吗?");
    (e || window.event).returnValue = confirmationMessage;
    return confirmationMessage;
  }

  useEffect(() => {
    window.addEventListener('beforeunload', beforeunload);
    queryCourseSearch();
    if(parameterConfig.target_customer == 'npu'){
      getColleges();
      setTagLabels(['思政课','总师文化',...tagLabels]);
    }
    getResourceIdsByMapId({
      mapId: mapid || query.id
    }).then((res) => {
      if (res.status === 200) {
        if (res.data.length) {
          getLabels(res.data).then((labels) => {
            if (labels.length) {
              setTagLabels(labels?.slice(0, 12));
            }
          });
        }
      }
    });
    return () => {
      form.submit();
      window.removeEventListener("beforeunload", beforeunload);
    };
  }, []);

  useEffect(() => {
    if (mapinfo) {
      getcourse(mapid || query.id);
      form.setFieldsValue({
        ...mapinfo,
        subject: mapinfo.subject ? mapinfo.subject : null,
        labels: mapinfo.labels != "" && mapinfo.labels ? mapinfo.labels?.split(',') : []
      });
    }
  }, [mapinfo]);


  const setTreeData = (root: any[], parentName: any = null, parentCode: any = null): any =>
    root.map((item) => {
      if (item.children && item.children.length > 0) {
        return {
          title: item.categoryName,
          value: item.categoryCode,
          key: item.categoryCode,
          parentName: parentName,
          parentCode: parentCode,
          children: setTreeData(item.children, item.categoryName, item.categoryCode)
        };
      } else {
        return {
          value: item.categoryCode,
          title: item.categoryName,
          key: item.categoryCode,
          parentName: parentName,
          parentCode: parentCode

        };
      }
    });

  // 课程分类
  const queryCourseSearch = () => {
    queryColleges().then((res) => {
      if (res.status === 200) {
        setcourseSubjectList(res.data?.subject ?? []);
        setMajor(setTreeData(res.data?.subjectEducation) ?? []);
      }
    });
  };

  const onFinish = (value: any) => {    
    let obj = {
      courseName: value.courseName,
      id: mapid || query.id,
      major: selectmajor ? selectmajor.code : null,
      mapName: value.mapName,
      subject: value.subject,
      labels: value.labels?.length ? value.labels.toString() : '',
      college: value.college,
      describe: value.describe
    };
    getSensitiveWord(JSON.stringify(obj), t('当前课程地图'), () => {
      updatemapinfo(obj).then((res) => {
        // 这个 savebtn 是解决 切换路由自动保存 和 页面保存按钮提示的问题
        if (savebtn) {
          if (res.status == 200) {
            message.success(t("修改成功"));
          } else {
            message.error(t("修改失败"));
          }
        }
        setSavebtn(false);
        dispatch({
          type: 'coursemap/fetchMapInfo',
          payload: {
            params: {
              mapId: mapid || query.id
            }
          }
        });
      });
    });

  };

  //专业TreeSelect的change函数
  const onProfessionChange = (value: any, label: any, extra: any, fieldName: any) => {
    console.log(value, label, extra, fieldName);
    if (fieldName === 'subject') {
      // setSubjectName(label);
      const parentName = extra.triggerNode.props.parentName;
      //给form 设置值
      setSelectmajor({
        code: extra.triggerNode.props.parentCode,
        value: parentName
      });
      subjectObject.current = {
        primary_classification: parentName || extra.triggerNode.props.title,
        secondary_classification: parentName ? extra.triggerNode.props.title : '',
        parentCode: extra.triggerNode.props.parentCode //后端让把父节点的code也传过去
      };
      return;
    }
  };


  const getColleges = () => {
    queryColleges().then((res: any) => {
      if (res.status === 200) {
        setColleges(res.data.organization ?? []);
      } else {
        message.error(res.message);
      }
    });
  };

  const opencourse = (course: any) => {  
    const typeEn:any = {
      1: 'mooc',
      2: 'spoc',
      3: 'training',
      4: 'map',
      5: 'microMajor'
    };
    // 新窗口打开课程详情页面
    window.open(`/learn/workbench/#/editcourse/baseInfo?id=${course.contentId_}&type=${typeEn[course.courseType]}&sm=${course.course_semester_id || 1}`);
  };

  return (
    <div className='mapinfo'>
      <Form
        name="basic"
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 8 }}
        initialValues={{ remember: true }}
        onFinish={onFinish}
        autoComplete="off">

        <Form.Item
          label={t("地图名称")}
          name="mapName"
          rules={[{ required: true, message: t("请输入地图名称") }]}>

          <Input />
        </Form.Item>
        <Form.Item
          label={t("关联课程")} >
          <div className='link_course'>
            {
              courseList.map((item: any, index: number) => {
                return <span className='course_item' key={index} onClick={()=>opencourse(item.entityData)} >{item.entityData.name}</span>
              })
            }
          </div>
        </Form.Item>
        <Form.Item
          label={parameterConfig.target_customer == 'npu' ? t("所属课程信息") : t("适用课程")}
          name="courseName"
          initialValue={null}>

          <Input placeholder={parameterConfig.target_customer == 'npu' ? t("请输入所属课程信息") :t("请输入适用课程")} />
        </Form.Item>
        <Form.Item
          label={parameterConfig.target_customer == 'npu' ? t("课程领域信息") : t("学科专业")}
          name="subject"
        // rules={[{ required: true, message: '请选择学科!' }]}
        >
          {
            //1 是川大  2是成信大 3高教 4职教 （3,4都是省平台）
            // ['3','4'].includes(homePageConfig.banner_plate_type)?
            <TreeSelect
              treeData={major}
              onChange={(value: any, label: any, extra: any) => {
                onProfessionChange(value, label, extra, 'subject');
              }}
              treeCheckable={courseSubjectList?.isMultiSelect}
              allowClear={true}
              treeNodeFilterProp="title"
              placeholder={parameterConfig.target_customer == 'npu' ? t("请选择课程领域信息") : t(`请选择学科专业`)}
              // defaultValue={[]}
               />
          }
        </Form.Item>
        <Form.Item
          label={parameterConfig.target_customer == 'npu' ? t("课程类型") :t("地图标签")}
          name="labels"
        // rules={[{ required: true, message: '请输入标签名称' }]}
        >
          {/* <Select
             mode="tags" open={false}
             style={{ width: '100%' }} placeholder="按Enter回车键创建标签"
            /> */}
          <TagInput  tagLabels={tagLabels} />
        </Form.Item>
        {parameterConfig.target_customer == 'npu' &&  
        <>
          <Form.Item
            label={t("信息管理部门")}
            name="college"
            initialValue={null}>
            <Select
              placeholder={t("请选择开课学院")}
              allowClear
              style={{ width: 300 }}>
              {colleges.map((item: any) =>
                <Select.Option key={item.code} value={item.name}>{item.name}</Select.Option>)}
            </Select>
          </Form.Item>

          <Form.Item
            label={t("描述")}
            name="describe"
            initialValue={null}>
            <TextArea placeholder={t("请输入描述")} rows={4} />
          </Form.Item>
        </>}  
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" style={{width: '120px'}} onClick={() => {
            setSavebtn(true);
            form.submit();
          }}>{t("保存")}

          </Button>
        </Form.Item>
      </Form>
    </div>);

};

export default Mapinfo;