import React, { FC, useState, useEffect, useRef } from 'react';
import { useSelector } from 'umi';
import './index.less';
import {
  Form,
  Input,
  Button,
  Select,
  Tooltip,
  message,
  Table,
  Modal,
  Row,
  Col,
  Divider,
  Checkbox,
  Collapse,
  Tag,
  Tabs,
  Dropdown,
  Menu
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { IconFont } from '@/components/iconFont';
const { confirm } = Modal;
import { getTreebylevel, bindretrieval, getAllpointbody, addlog } from '@/api/addCourse';
// 引用antd icon
import ResourceModal from '../../../components/ResourceModal';
import ResourcePreviewModal from '../../../components/ResourcePreviewModal';
import { createguid } from '../Editmap/util';
import TopicSelectModal from '@/pages/HomeworkManagement/components/TopicSelectModal';
import { optionType_ } from '@/pages/HomeworkManagement/utils/columns';
import { bindTopicList, fetchTopicList } from '@/api/homework';
import { bindQuestionToNode, getknowledgelist, deleteQuestionToNode, questionsanswers, finishKnowledge, getStudyResourceList, deleteResourceStudyRecord, autoBindQuestion, unbindAllQuestion } from '@/api/coursemap';
import useLocale from '@/hooks/useLocale';
import RenderHtml from '@/components/renderHtml';
import CaseModal from '@/components/CaseModal';
import CasePreviewModal from '@/components/CasePreviewModal';
import UploadFile from '../components/Uploadfile';
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import { getContentLength } from '@/pages/Coursemaptool/Editmap/util'

const Bindmange: FC<any> = ({ graph, updatanode, visible, setVisible, mapid, courseid, coursename }) => {
  const { t } = useLocale();
  const { userInfo } = useSelector<any, any>((state) => state.global);
  const mapinfo: any = useSelector<any, any>(
    (state: { coursemap: { mapinfo: any; }; }) => state.coursemap.mapinfo);

  const [inputtext, setInputtext] = useState('');
  // 折叠面板数据
  const [collapsedata, setCollapsedata] = useState<any>([]);
  //原数据
  const [originaldata, setOriginaldata] = useState<any>([]);

  // chexkbox 选中的资源节点
  const [checkedList, setCheckedList] = useState<any>([]);
  // chexkbox 选中的参考资料节点
  const [checkedReferenceList, setCheckedReferenceList] = useState<any>([]);
  // chexkbox 选中的作业节点
  const [checkedTopicList, setCheckedTopicList] = useState<any>([]);
  // 当前选中的案例
  const [checkedCaseList, setCheckedCaseList] = useState<any>([]);
  // 选择资源弹窗的数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]);
  //   选择资源弹窗的开关
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalLoading, setModalLoading] = useState<boolean>(false); //选择资源弹窗控制
  // 资源预览相关state
  const [entityModalVisible, setEntityModalVisible] = useState(false);
  const [entityPreview, setEntityPreview] = useState<any>(null);
  // 当前添加资源的下标
  const [currentindex, setCurrentindex] = useState(0);
  // 当前添加试题的下标
  const [currentTopicIndex, setCurrentTopicIndex] = useState(0);
  // 选择资源的模式  folder 选择文件夹  2 选择文件
  const [selectMode, setSelectMode] = useState<'folder' | 'file'>('file');
  //绑定弹窗的tab
  const [currentTab, setCurrentTab] = useState<string>('resource');
  const [topicVisible, setTopicVisible] = useState<boolean>(false);
  const [topicList, setTopicList] = useState<any>([]);

  // 选择一键绑定的弹窗
  const [bindtypevisible, setBindtypevisible] = useState<boolean>(false);
  const optionsWithDisabled = [
    { label: t("资源"), value: 'resource' },
    { label: t("试题"), value: 'topic' }];

    let optionsWithDisabled2 = [
      { label: t("资源"), value: 'resource' },
      { label: t("试题"), value: 'topic' }
    ];

  // 当前是解除 还是绑定
  const [isbtntype, setIsbtntype] = useState<number>(1);
  const [selecchebox, setSelecchebox] = useState<any>(['resource', 'topic','case']);

  // 当前新绑定的资源
  const newBindResource = useRef<any>([]);
  // 当前试题的id
  const [topicId, setTopicId] = useState<any>(null);
  // 当前已经绑定的所有试题
  const [allTopicList, setAllTopicList] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const [caseVisible, setCaseVisible] = useState<boolean>(false);
  const [casePreviewOpen, setCasePreviewOpen] = useState<boolean>(false);
  const [casePreviewData, setCasePreviewData] = useState<any>({});
  const parameterConfig = useSelector<any, any>(
    ({ global }) => global.parameterConfig,
  );

  // 添加上传组件的状态控制
  const [uploadVisible, setUploadVisible] = useState<boolean>(false);

  useEffect(()=>{
    if(parameterConfig.case_display == 'true'){
      optionsWithDisabled2 = [
        { label: t("资源"), value: 'resource' },
        { label: t("试题"), value: 'topic' },
        { label: t("案例"), value: 'case' }]
    }
  },[])

  // 初始化方法
  useEffect(() => {
    if (graph && visible == 5) {
      initdata();
    }
  }, [graph, visible]);

  // 初始化数据
  const initdata = () => {
    const data = graph.toJSON();
    let newdata = data.cells.
      filter((item: any) => item.component === 'react-compont' && item.data.type == 2)?.map((item: any) => ({
        ...item,
        data: {
          ...item.data,
          homework: item.data.homework?.map((item: any) => ({
            ...item,
            tempid: item.tempid || createguid() //解决从详情节点添加的试题没有id
          }))
        }
      }));
    console.log('重置了', newdata);
    setCollapsedata(newdata);
    setOriginaldata(newdata);
  };


  //显示Modal
  const showModal = () => {
    setModalLoading(true);
    const getTreeData = () => {
      getTreebylevel().then((res) => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalLoading(false);
            setModalVisible(true);

          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children ?
            forTree(item.children, [...parentsKeys, item.code]) :
            []
        };
      });
    };
    getTreeData();
  };

  // 知识点添加素材
  const addrecouse = (selectrecouse: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.contentId_,
        contentId_: item.contentId_,
        type: item.type_,
        keyframe_: item.keyframe_,
        tempid: createguid(),
        createDate: item.createDate,
        suffix: item.fileext,
        duration: item.duration || 0
      };
    });
    if (obj.data.bindresource && obj.data.bindresource.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.data.bindresource.every((item2: any) => item.contentId_ != item2.contentId_);
      });
      if (newarr.length) {
        obj.data.bindresource = [...obj.data.bindresource, ...newarr];
        createlogdata(obj, newarr, 1);
      } else {
        message.warning(t('已存在该资源'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.data.bindresource = newdata || [];
      createlogdata(obj, newdata, 1);
    }
    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setModalVisible(false);
    addlogs();
  };


  // 知识点添加案例
  const addcase = (selectrecouse: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.id,
        contentId_: item.id,
        type: 'biz_sobey_case',
        keyframe_: null,
        tempid: createguid(),
      };
    });
    if (obj.data.caselist && obj.data.caselist.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.data.caselist.every((item2: any) => item.contentId_ != item2.contentId_);
      });
      if (newarr.length) {
        obj.data.caselist = [...obj.data.caselist, ...newarr];
        createlogdata(obj, newarr, 1);
      } else {
        message.warning(t('已存在该资源'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.data.caselist = newdata || [];
      createlogdata(obj, newdata, 1);
    }
    if(obj.data.caselist.length > 0){
      obj.data.iscase = true;
    }else{
      obj.data.iscase = false;
    }
    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setModalVisible(false);
    addlogs();
  };

  // 知识节点绑定资源知识点
  const addpoint = (selectrecouse: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = selectrecouse.map((item: any) => {
      return {
        recousetype: item.video_clip_path == 'document' ? 'document_point' : 'point',
        name: item.title,
        contentId: item.guid_,
        contentId_: item.guid_,
        type: item.video_clip_path == 'document' ? 'biz_sobey_document_point' : 'biz_sobey_point',
        keyframe_: item.keyframepath,
        tempid: createguid(),
        keyframeno: item.keyframeno,
        parentcontentId: item.contentId,
        fragment_description: item.fragment_description,
        inpoint: item.inpoint,
        outpoint: item.outpoint,
        parentname: item.name,
        createDate: item.createDate,
        suffix: item.suffix,
        duration: item.outpoint - item.inpoint || 0
      };
    });
    if (obj.data.bindresource && obj.data.bindresource.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.data.bindresource.every((item2: any) => item.contentId_ != item2.contentId_);
      });
      if (newarr.length) {
        obj.data.bindresource = [...obj.data.bindresource, ...newarr];
        createlogdata(obj, newarr, 2);
      } else {
        message.warning(t('已存在该知识点'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.data.bindresource = newdata || [];
      createlogdata(obj, newdata, 2);
    }
    // selectrecouse.map((content: any) => {
    //     if (content.type_ == 'biz_sobey_video') {
    //         obj.videoIds.push(content);
    //     } else if (content.type_ == 'biz_sobey_audio') {
    //         obj.audioIds.push(content);
    //     } else if (content.type_ == 'biz_sobey_picture') {
    //         obj.pictureIds.push(content);
    //     } else if (content.type_ == 'biz_sobey_document') {
    //         obj.documentIds.push(content);
    //     } else {
    //         message.error('不支持绑定的文件类型！')
    //     }
    // })
    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setModalVisible(false);
    addlogs();
  };

  // 添加文档知识点
  const adddocumentpoint = (selectrecouse: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = selectrecouse.map((item: any) => {
      return {
        recousetype: 'biz_sobey_document_point',
        name: item.knowledgePoints[0].title,
        contentId: item.knowledgePoints[0].guid_,
        contentId_: item.knowledgePoints[0].guid_,
        type: 'biz_sobey_document_point',
        keyframe_: item.knowledgePoints[0].keyframepath,
        tempid: createguid(),
        keyframeno: item.knowledgePoints[0].keyframeno,
        parentcontentId: item.contentId,
        fragment_description: item.knowledgePoints[0].fragment_description,
        inpoint: item.knowledgePoints[0].inpoint,
        outpoint: item.knowledgePoints[0].outpoint,
        parentname: item.name,
        createDate: item.createDate,
        suffix: item.suffix
      };
    });
    if (obj.data.bindresource && obj.data.bindresource.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.data.bindresource.every((item2: any) => item.contentId_ != item2.contentId_);
      });
      if (newarr.length) {
        obj.data.bindresource = [...obj.data.bindresource, ...newarr];
        createlogdata(obj, newarr, 2);
      } else {
        message.warning(t('已存在该知识点'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.data.bindresource = newdata || [];
      createlogdata(obj, newdata, 2);
    }
    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setModalVisible(false);
    addlogs();

  }


  // 检索知识点
  const querynode = () => {
    const data = graph.toJSON();
    let newdata = data.cells.filter((item: any) => item.component == 'react-compont' && item.data.type == 2);
    let newarr = newdata.filter((item: any) => item.data.label.indexOf(inputtext) > -1);
    if (newarr.length) {
      setCollapsedata(newarr);
      setCurrentindex(0);
    } else {
      message.warning(t('未找到相关知识点'));
    }
  };

  const deleterouse = (index: number) => {
    let obj: any = collapsedata[currentindex];
    obj.data.bindresource = obj.data.bindresource.filter((item: any, i: number) => i != index);
    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
  };

  const deleteAllRouse = () => {
    confirm({
      title: t("确认解除当前选中的所有知识节点的绑定资源吗？"),
      onOk() {
        let newarr = collapsedata.map((item: any, index: number) => {
          let obj = {
            ...item.data,
            bindresource: (() => {
              if (item.data.bindresource && item.data.bindresource.length) {
                return item.data.bindresource.filter((item2: any) => {
                  return checkedList.every((item3: any) => item2.tempid != item3.tempid);
                });
              } else {
                return [];
              }
            })()
          };

          updatanode(item.id, obj);
          return {
            ...item,
            data: obj
          };
        });
        setCollapsedata([...newarr]);
        setCheckedList([]);
        message.success(t('删除成功！'));
      },
      onCancel() {
        console.log('Cancel');
      }
    });
  };

  // 删除案例
  const deleteAllCase = () => {
    confirm({
      title: t("确认解除当前选中的所有知识节点的绑定案例吗？"),
      onOk() {
        let newarr = collapsedata.map((item: any, index: number) => {
          let obj = {
            ...item.data,
            caselist: (() => {
              if (item.data.caselist && item.data.caselist.length) {
                return item.data.caselist.filter((item2: any) => {
                  return checkedCaseList.every((item3: any) => item2.tempid != item3.tempid);
                });
              } else {
                return [];
              }
            })()
          };
          if(obj.caselist.length > 0){
            obj.iscase = true;
          }else{
            obj.iscase = false;
          }
          updatanode(item.id, obj);
          return {
            ...item,
            data: obj
          };
        });
        setCollapsedata([...newarr]);
        setCheckedCaseList([]);
        message.success(t('删除成功！'));
      },
      onCancel() {
        console.log('Cancel');
      }
    });
  };

  const deleteAllTopic = () => {
    confirm({
      title: t("确认解除当前选中的所有知识节点的绑定试题吗？"),
      onOk() {
        let obj = collapsedata[currentindex].data.homework.filter((item: any) => {
          let bool = checkedTopicList.find((item2: any) => item2.tempid == item.tempid);
          if (bool) {
            return false;
          } else {
            return true;
          }
        });
        updatanode(collapsedata[currentindex].id, {
          ...collapsedata[currentindex].data,
          homework: obj
        });
        collapsedata[currentindex].data.homework = obj;
        setCollapsedata([...collapsedata]);
        message.success(t('删除成功！'));
      }
    });
  };

  // 根据名称匹配试题
  const bindtopic = async () => {
    let loading = message.loading('正在绑定试题...', 0);
    return new Promise((resolve) => {
      bindTopicList({
        points: collapsedata.map((item: any) => item.data.label)
      }).then((res: any) => {
        if (res.status == 200) {
          let flag = -1;
          let temp = [...collapsedata];
          res.data.map((item: any, index: number) => {
            if (item.testQuestions?.length > 0) {
              const arr_ = item.testQuestions.filter((item_0: any) => {
                if (temp[index].data.homework) {
                  return temp[index].data.homework.every((item_1: any) => item_0.id != item_1.id);
                } else {
                  return true;
                }
              });
              if (arr_.length > 0) {
                flag = flag == -1 ? index : flag;
                temp[index] = {
                  ...temp[index],
                  data: {
                    ...temp[index].data,
                    homework: [...(temp[index].data.homework || []), ...arr_.map((item: any) => ({ ...item, tempid: createguid() }))]
                  }
                };
                updatanode(temp[index].id, {homework:temp[index].data.homework});
              } else {

                //console.log('已存在该试题')
              }
            }
          });
          if (flag != -1) {
            setCollapsedata(JSON.parse(JSON.stringify(temp)));
            setModalVisible(false);
            message.success(t('绑定成功！'));
            setCurrentTopicIndex(flag);
          } else {
            message.info(t('暂未检索到新的可绑定试题!'));
          }
        } else {
          message.error(t('试题检索出错了'));
        }
        resolve(true);
        loading();
      });
    });

  };


  const bindresource = async () => {
    let loading = message.loading(t('正在绑定资源...'), 0);
    return new Promise((resolve) => {
      //获取用户code
      let userCode = userInfo.userCode;
      bindretrieval({
        "pageIndex": 1,
        "pageSize": 100,
        "keyword": collapsedata.map((item: any) => item.data.label),
        "folderPath": `global_sobey_defaultclass/public/共享资源/,global_sobey_defaultclass/private/${userCode}`
      }).then((res: any) => {
        if (res.data.data.length) {
          let dataarr: any = [];
          setCollapsedata((orange: any) => {
            dataarr = orange;
            return orange;
          });
          // 判断是否绑定成功  只要有一个匹配成功 就是成功 如果一个都没有就提示绑定失败！
          let flag = false;
          // 保存匹配到的第一个的下标
          let flagindex: any = null;
          // 当前成功绑定几个
          let successnum = 0;

          // 遍历知识点
          collapsedata.map((item: any, index: number) => {
            //通过名称进行匹配
            let recusearr = res.data.data.filter((item2: any) => item2.name == item.data.label);
            if (recusearr.length) {
              flag = true;
              if (flagindex === null) {
                flagindex = index;
              }
              let obj: any = dataarr[index];
              let newdata = recusearr.map((item2: any) => {
                return {
                  name: item2.name,
                  contentId: item2.contentId_,
                  contentId_: item2.contentId_,
                  type: item2.type_,
                  keyframe_: item2.keyframe_,
                  tempid: createguid(),
                  createDate: item2.createDate,
                  suffix: item2.fileext,
                  duration: item2.duration || 0
                };
              });
              if (obj.data.bindresource && obj.data.bindresource.length) {
                let newarr = newdata.filter((item: any) => {
                  return obj.data.bindresource.every((item2: any) => item.contentId_ != item2.contentId_);
                });
                if (newarr.length) {
                  successnum = successnum + newarr.length;
                  obj.data.bindresource = [...obj.data.bindresource, ...newarr];
                  createlogdata(item, newdata, 1);
                } else {
                  // message.warning('已存在该资源');
                  setModalVisible(false);
                  return;
                }
              } else {
                successnum = successnum + newdata.length;
                obj.data.bindresource = newdata || [];
                createlogdata(item, newdata, 1);
              }
              dataarr[index] = obj;
              updatanode(obj.id, {bindresource:obj.data.bindresource});
            }

          });
          if (flag) {
            setCollapsedata([...dataarr]);
            setModalVisible(false);
            if (successnum > 0) {
              message.success(t('绑定成功！'));
            } else {
              message.info(t('暂未检索到新的可绑定资源!'));
            }
            setCurrentindex(flagindex);
          } else {
            message.info(t('暂未检索到可绑定资源!'));
          }

        } else {
          message.info(t('暂未检索到可绑定资源!'));
        }
        loading();
        resolve(true);
      });
    });
  };

  const addlogs = () => {
    if (newBindResource.current.length) {
      addlog(mapinfo.mapName, newBindResource.current).then((res) => {
        if (res.success) {
          newBindResource.current = [];
          console.log('添加资源日志记录成功！');
        } else {
          console.log('添加资源日志记录失败！');
        }
      });
    }
  };

  const bingall = async () => {
    if (selecchebox.includes('topic')) {
      await bindtopic();
      // await autoBindTopic();
    }

    if (selecchebox.includes('resource')) {
      bindresource().then(() => {
        // 批量绑定知识点
        bindallpoint().then(() => {
          console.log('批量绑定的新知识点', newBindResource.current);
          addlogs();
        });
      });
    }
  };


  const createlogdata = (node: any, arr: any, type: number) => {
    arr.forEach((item: any) => {
      newBindResource.current.push({
        "contentId": item.contentId, //资源id
        "name": item.name, //资源名称
        "courseKnowledgeName": node.data.label, //知识节点名称
        "knowledgeNames": type == 2 ? [item.name] : []
      });
    });
  };

  const removbindall = () => {
    //如果是资源
    if (selecchebox.includes('resource')) {
      // 将所有节点的绑定资源数组清空
      let dataarr = [...collapsedata];
      dataarr.forEach((item: any) => {
        if (item.data.bindresource?.length) {
          item.data.bindresource = [];
          updatanode(item.id, item.data);
        }
      });
      setCollapsedata([...dataarr]);
    }

    if (selecchebox.includes('topic')) {
      // 将所有节点的绑定试题数组清空
      let dataarr = [...collapsedata];
      dataarr.forEach((item: any) => {
        if (item.data.homework?.length) {
          item.data.homework = [];
          updatanode(item.id, item.data);
        }
      });
      setCollapsedata([...dataarr]);
    }

    //如果是资源
    if (selecchebox.includes('case')) {
      // 将所有节点的绑定资源数组清空
      let dataarr = [...collapsedata];
      dataarr.forEach((item: any) => {
        if (item.data.caselist?.length) {
          item.data.caselist = [];
          item.data.iscase = false;
          updatanode(item.id, item.data);
        }
      });
      setCollapsedata([...dataarr]);
    }

    message.success(t('解除绑定成功！'));
  };

  // 一键绑定知识点
  const bindallpoint = async () => {
    let loading = message.loading(t('正在绑定知识点...'), 0);
    return new Promise((resolve) => {
      getAllpointbody({
        pageIndex: 1,
        pageSize: 100
      }, collapsedata.map((item: any) => item.data.label)).then((res) => {
        if (res.success) {
          if (res.data.data.length) {
            let dataarr: any = [];
            // 解决频繁更新导致取不到最新的数据问题
            setCollapsedata((orange: any) => {
              dataarr = orange;
              return orange;
            });
            // 判断是否绑定成功  只要有一个匹配成功 就是成功 如果一个都没有就提示绑定失败！
            let flag = false;
            // 保存匹配到的第一个的下标
            let flagindex: any = null;
            // 当前成功绑定几个
            let successnum = 0;
            // 遍历知识点
            collapsedata.map((item: any, index: number) => {
              //通过名称进行匹配
              let recusearr: any = [];
              res.data.data.forEach((item2: any) => {
                let knowledgePoints = item2.knowledgePoints.filter((item3: any) => item3.title == item.data.label);
                if (knowledgePoints) {
                  knowledgePoints.forEach((item4: any) => {
                    recusearr.push({
                      ...item4,
                      contentId: item2.contentId,
                      name: item2.name,
                      createDate: item2.createDate,
                      suffix: item2.suffix,
                      duration: item2.duration || 0
                    });
                  });
                }
              });
              if (recusearr.length) {
                flag = true;
                if (flagindex === null) {
                  flagindex = index;
                }
                let obj: any = dataarr[index];
                let newdata = recusearr.map((item: any) => {
                  return {
                    recousetype: 'point',
                    name: item.title,
                    contentId: item.guid_,
                    contentId_: item.guid_,
                    type: 'biz_sobey_point',
                    keyframe_: item.keyframepath,
                    tempid: createguid(),
                    keyframeno: item.keyframeno,
                    parentcontentId: item.contentId,
                    fragment_description: item.fragment_description,
                    inpoint: item.inpoint,
                    outpoint: item.outpoint,
                    parentname: item.name,
                    createDate: item.createDate,
                    suffix: item.suffix,
                    duration: item.outpoint - item.inpoint || 0
                  };
                });
                if (obj.data.bindresource && obj.data.bindresource.length) {
                  let newarr = newdata.filter((item: any) => {
                    return obj.data.bindresource.every((item2: any) => item.contentId_ != item2.contentId_);
                  });
                  if (newarr.length) {
                    successnum = successnum + newarr.length;
                    obj.data.bindresource = [...obj.data.bindresource, ...newarr];
                    createlogdata(item, newarr, 2);
                  } else {
                    // message.warning('已存在该资源');
                    setModalVisible(false);
                    return;
                  }
                } else {
                  successnum = successnum + newdata.length;
                  obj.data.bindresource = newdata || [];
                  createlogdata(item, newdata, 2);
                }
                dataarr[index] = obj;
                updatanode(obj.id, obj.data);
              }

            });
            if (flag) {
              setCollapsedata([...dataarr]);
              setModalVisible(false);
              if (successnum > 0) {
                message.success(t('绑定知识点成功！'));
              } else {
                message.info(t('暂未检索到新的可绑定知识点!'));
              }
              setCurrentindex(flagindex);
            } else {
              message.info(t('暂未检索到可绑定知识点!'));
            }
          } else {
            message.info(t('暂未检索到可绑定知识点!'));
          }
        }
        loading();
        resolve(true);
      });
    });
    // let res = await
  };
  

  //添加试题
  const addTopic = (data: any) => {
    // const temp = graph.toJSON();
    // let newdata = temp.cells.filter((item: any) => item.component == 'react-compont' && item.data.type == 2);
    const data_ = data.map((item: any) => ({ ...item, selected: false, tempid: createguid(), questionsNumber :getContentLength(item.questions_content)}));
    const current = collapsedata[currentTopicIndex].data.homework || [];
    const temp = {
      ...collapsedata[currentTopicIndex].data,
      homework: current.concat(data_),
      tempid: createguid()
    };
    debugger
    updatanode(collapsedata[currentTopicIndex].id, temp);
    setCollapsedata(collapsedata.map((item: any, index: number) => {
      if (index === currentTopicIndex) {
        return {
          ...item,
          data: temp
        };
      } else {
        return item;
      }
    }));
    console.log(data_);

    setTopicList([...current, ...data_]);
    setTopicVisible(false);
  };
  const columns: any = [
    // {
    //   title: '序号',
    //   width: '5%',
    //   ellipsis: true,
    //   render: (item: any, record: any, index: any) => (
    //     <div>{index + 1}</div>
    //   )
    // },
    {
      title: t("类型"),
      dataIndex: 'questions_type',
      key: 'questions_type',
      ellipsis: true,
      align: 'center',
      render: (item: any, record: any) =>
        <div>{t(optionType_[Number(item)])}</div>

    },
    {
      title: t("难度"),
      dataIndex: 'questions_difficulty',
      key: 'questions_difficulty',
      ellipsis: true,
      align: 'center'
    },
    {
      title: t("题目"),
      dataIndex: 'questions_content',
      key: 'questions_content',
      ellipsis: true,
      align: 'center',
      render: (value: any) =>
        <RenderHtml dangerouslySetInnerHTML={{ __html: value }} className='special-dom'></RenderHtml>

    },
    {
      title: t("答案"),
      dataIndex: 'questions_answers',
      key: 'questions_answers',
      ellipsis: true,
      align: 'center',
      render: (value: any, record: any) =>
        <div>{value?.join(',')}</div>

    },
    {
      title: t("创建人"),
      // width: '8%',
      dataIndex: 'add_username',
      key: 'add_username',
      ellipsis: true,
      align: 'center'
    }];


  const columns2: any = [
    {
      title: t("素材名"),
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      align: 'center'
    },
    {
      title: t("类型"),
      dataIndex: 'recousetype',
      key: 'recousetype',
      ellipsis: true,
      align: 'center',
      render: (value: any, record: any) => {
        if(value == 'point'){
          return <span>资源知识点</span>;
        }else if(value == 'biz_sobey_document_point' || value == 'document_point'){
          return <span>文档知识点</span>;
        }else{
          return <span>资源</span>;
        }
      }
    },
    {
      title: t("拓展名"),
      dataIndex: 'suffix',
      key: 'suffix',
      ellipsis: true,
      align: 'center',
      width: 80
    },
    {
      title: t("入库时间"),
      dataIndex: 'createDate',
      key: 'createDate',
      ellipsis: true,
      align: 'center',
      width: 160
    },
    {
      title: t("标注知识点"),
      // width: '8%',
      dataIndex: 'add_username',
      key: 'add_username',
      ellipsis: true,
      align: 'center',
      render: (value: any, record: any) =>
        <Button type="link" onClick={() => {
          if(record.recousetype == 'document_point' || record.recousetype == 'biz_sobey_document_point'){
            window.open(`/rman/#/basic/rmanDetail/${record.parentcontentId}?showArrow=true&guid_=${record.contentId}&inpoint=${record.inpoint}`);
          }else if (record.recousetype == 'point') {
            window.open(`/rman/#/basic/rmanDetail/${record.parentcontentId}?showArrow=true&inpoint=${record.inpoint}`);
          } else {
            window.open(`/rman/#/basic/rmanDetail/${record.contentId}`);
          }
        }} style={{ color: '#549CFF' }}>{t("查看")}</Button>

    },
    {
      title: t("来源"),
      // width: '8%',
      dataIndex: 'parentname',
      key: 'parentname',
      align: 'center',
      ellipsis: true,
      render: (value: any, record: any) =>
        <Button type="link" onClick={() => {
          if(record.recousetype == 'biz_sobey_document_point'){
            window.open(`/rman/#/basic/rmanDetail/${record.parentcontentId}?showArrow=true&guid_=${record.contentId}&inpoint=${record.inpoint}`);
          }else if (record.recousetype == 'point') {
            window.open(`/rman/#/basic/rmanDetail/${record.parentcontentId}?showArrow=true&inpoint=${record.inpoint}`);
          } else {
            window.open(`/rman/#/basic/rmanDetail/${record.contentId}`);
          }
        }} style={{ color: '#549CFF' }}>{(record.recousetype == 'point' || record.recousetype == 'biz_sobey_document_point') ? value : record.name}</Button>

    }];

  const columns3: any = [
    {
      title: t("案例名称"),
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      align: 'center'
    },
    {
      title: t("类型"),
      dataIndex: 'recousetype',
      key: 'recousetype',
      ellipsis: true,
      align: 'center',
      render: (value: any, record: any) => {
        return <span>案例</span>;
      }
    },
    {
      title: t("操作"),
      // width: '8%',
      dataIndex: 'parentname',
      key: 'parentname',
      align: 'center',
      ellipsis: true,
      render: (value: any, record: any) => <Button type="link" onClick={() => {
        setCasePreviewData({
          id:record.contentId,
          name:record.name
        })
        setCasePreviewOpen(true);
      }}>查看</Button>
    }
  ]

  const caseRowSelection: any = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      const current = collapsedata[currentTopicIndex]?.data.caselist?.map((item: any) => {
        return {
          ...item,
          selected: newSelectedRowKeys.includes(item.tempid)
        };
      });
      collapsedata[currentTopicIndex] = {
        ...collapsedata[currentTopicIndex],
        data: {
          ...collapsedata[currentTopicIndex].data,
          caselist: current
        }
      };
      setCollapsedata(JSON.parse(JSON.stringify(collapsedata)));
      setCheckedCaseList(newSelectedRows);
    },
    // preserveSelectedRowKeys: true,
    selectedRowKeys: checkedCaseList.map((item: any) => item.tempid)
  };

  const topicRowSelection: any = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      const current = collapsedata[currentTopicIndex]?.data.homework?.map((item: any) => {
        return {
          ...item,
          selected: newSelectedRowKeys.includes(item.tempid)
        };
      });
      collapsedata[currentTopicIndex] = {
        ...collapsedata[currentTopicIndex],
        data: {
          ...collapsedata[currentTopicIndex].data,
          homework: current
        }
      };
      setCollapsedata(JSON.parse(JSON.stringify(collapsedata)));
      setCheckedTopicList(newSelectedRows);
    },
    // preserveSelectedRowKeys: true,
    selectedRowKeys: checkedTopicList.map((item: any) => item.tempid)
  };

  const resourceRowSelection: any = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      const current = collapsedata[currentTopicIndex]?.data.bindresource?.map((item: any) => {
        return {
          ...item,
          selected: newSelectedRowKeys.includes(item.tempid)
        };
      });
      collapsedata[currentTopicIndex] = {
        ...collapsedata[currentTopicIndex],
        data: {
          ...collapsedata[currentTopicIndex].data,
          bindresource: current
        }
      };
      setCollapsedata(JSON.parse(JSON.stringify(collapsedata)));
      setCheckedList(newSelectedRows);
    },
    // preserveSelectedRowKeys: true,
    selectedRowKeys: checkedList.map((item: any) => item.tempid)
  };

  // 删除所有选中的参考资料
  const deleteAllReference = () => {
    confirm({
      title: t("确认解除当前选中的所有知识节点的绑定参考资料吗？"),
      onOk() {
        let newarr = collapsedata.map((item: any, index: number) => {
          let obj = {
            ...item.data,
            referenceMaterials: (() => {
              if (item.data.referenceMaterials && item.data.referenceMaterials.length) {
                return item.data.referenceMaterials.filter((item2: any) => {
                  return checkedReferenceList.every((item3: any) => item2.tempid != item3.tempid);
                });
              } else {
                return [];
              }
            })()
          };
          updatanode(item.id, obj);
          return {
            ...item,
            data: obj
          };
        });
        setCollapsedata([...newarr]);
        setCheckedReferenceList([]);
        message.success(t('删除成功！'));
      },
      onCancel() {
        console.log('Cancel');
      }
    });
  };

  const referenceRowSelection: any = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      const current = collapsedata[currentTopicIndex]?.data.referenceMaterials?.map((item: any) => {
        return {
          ...item,
          selected: newSelectedRowKeys.includes(item.tempid)
        };
      });
      collapsedata[currentTopicIndex] = {
        ...collapsedata[currentTopicIndex],
        data: {
          ...collapsedata[currentTopicIndex].data,
          referenceMaterials: current
        }
      };
      setCollapsedata(JSON.parse(JSON.stringify(collapsedata)));
      setCheckedReferenceList(newSelectedRows);
    },
    selectedRowKeys: checkedReferenceList.map((item: any) => item.tempid)
  };

  // 处理上传成功后的回调
  const handleUploadSuccess = (data: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = [{
      name: data.name,
      contentId: data.resourseId,
      contentId_: data.resourseId,
      type: `biz_sobey_${data.resourceType}`,
      keyframe_: data.keyframe_ || null,
      tempid: createguid(),
      createDate: new Date().toISOString(),
      duration: data.duration || 0
    }];
    if (obj.data.bindresource && obj.data.bindresource.length) {
      obj.data.bindresource = [...obj.data.bindresource, ...newdata];
    } else {
      obj.data.bindresource = newdata;
    }

    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setUploadVisible(false);
    message.success(t('上传成功'));
  };

  // 处理参考资料上传成功后的回调
  const handleReferenceUploadSuccess = (data: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = [{
      name: data.name,
      contentId: data.resourseId,
      contentId_: data.resourseId,
      type: `biz_sobey_${data.resourceType}`,
      keyframe_: data.keyframe_ || null,
      tempid: createguid(),
      createDate: new Date().toISOString(),
      duration: data.duration || 0
    }];
    if (obj.data.referenceMaterials && obj.data.referenceMaterials.length) {
      obj.data.referenceMaterials = [...obj.data.referenceMaterials, ...newdata];
    } else {
      obj.data.referenceMaterials = newdata;
    }

    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setUploadVisible(false);
    message.success(t('上传成功'));
  };

  // 修改资源添加下拉菜单
  const resourceMenu = (
    <Menu onClick={({key}) => handleResourceAdd(key)}>
      <Menu.Item key="local">{t("本地上传")}</Menu.Item>
      <Menu.Item key="library">{t("资源库")}</Menu.Item>
    </Menu>
  );

  // 处理资源添加方式选择
  const handleResourceAdd = (key: string) => {
    if(key === 'local') {
      setUploadVisible(true);
    } else if(key === 'library') {
      setSelectMode('file');
      showModal();
    }
  };

  // 处理参考资料添加方式选择
  const handleReferenceAdd = (key: string) => {
    if(key === 'local') {
      setUploadVisible(true);
    } else if(key === 'library') {
      setSelectMode('file');
      showModal();
    }
  };

  // 添加参考资料
  const addReference = (selectrecouse: any) => {
    let obj: any = collapsedata[currentindex];
    let newdata = selectrecouse.map((item: any) => {
      return {
        name: item.name,
        contentId: item.contentId_,
        contentId_: item.contentId_,
        type: item.type_,
        keyframe_: item.keyframe_,
        tempid: createguid(),
        createDate: item.createDate,
        suffix: item.fileext,
        duration: item.duration || 0
      };
    });
    if (obj.data.referenceMaterials && obj.data.referenceMaterials.length) {
      let newarr = newdata.filter((item: any) => {
        return obj.data.referenceMaterials.every((item2: any) => item.contentId_ != item2.contentId_);
      });
      if (newarr.length) {
        obj.data.referenceMaterials = [...obj.data.referenceMaterials, ...newarr];
      } else {
        message.warning(t('已存在该参考资料'));
        setModalVisible(false);
        return;
      }
    } else {
      obj.data.referenceMaterials = newdata || [];
    }
    collapsedata[currentindex] = obj;
    setCollapsedata([...collapsedata]);
    updatanode(obj.id, obj.data);
    setModalVisible(false);
  };

  // 参考资料添加下拉菜单
  const referenceMenu = (
    <Menu onClick={({key}) => handleReferenceAdd(key)}>
      <Menu.Item key="local">{t("本地上传")}</Menu.Item>
      <Menu.Item key="library">{t("资源库")}</Menu.Item>
    </Menu>
  );

  return (
    <div className='bindmange'>
      <Modal title={parameterConfig.target_customer === CUSTOMER_NPU ? t("资源配置") : t("绑定管理")} bodyStyle={{ paddingTop: '0px' }} open={visible == 5} className='bindmangeModal' centered width="1500px" footer={false} onCancel={() => setVisible(0)}>
        <div className='bindcontent_box'>
          <div className='left_box'>
            <div className='top_'>
              <div>
                {/* <Button type="primary" loading={modalLoading} onClick={() => { setBindtypevisible(true); setIsbtntype(1); }}>{t("一键绑定")}</Button> */}
                <Button onClick={() => { setBindtypevisible(true); setIsbtntype(2); }}>{t("一键解除")}</Button>
              </div>
            </div>
            <Divider className='Divider' />
            <Input.Group compact>
              <Input value={inputtext} allowClear onPressEnter={querynode} style={{ width: '98%' }} placeholder={t("请输入关键词")} onChange={(e) => {
                setInputtext(e.target.value);
                if (e.target.value == '') {
                  const data = graph.toJSON();
                  let newdata = data.cells.filter((item: any) => item.component == 'react-compont' && item.data.type == 2);
                  setCollapsedata(newdata);
                }
              }} suffix={
                <IconFont
                  style={{ color: '#333333', fontSize: '14px', opacity: '0.7' }}
                  type="iconsousuo2" onClick={querynode} />} />


            </Input.Group>
            <div className='tree_box'>
              {
                collapsedata.map((item: any, index: number) => {
                  return (
                    <div className={currentindex == index ? 'bind_panel_box_active' : 'bind_panel_box'} key={index} onClick={() => { setCurrentindex(index); setCurrentTopicIndex(index);}}>
                      <div className='point_name_row'>
                        <span className='point_name' title={item.data.label}>{item.data.label}</span>
                      </div>
                      <div className='status_row'>
                        {
                          item.data.bindresource != 0 && item.data.bindresource ?
                            <span style={{ color: currentindex == index ? '#ffff' : 'var(--primary-color)' }}>{t("资源（")}{item.data.bindresource.length}）</span> :
                            <span style={{ color: '#d9d9d9' }}>{t("资源（0）")}</span>}

                        {
                          item.data.homework?.length > 0 ?
                            <span style={{ color: currentindex == index ? '#ffff' : 'var(--primary-color)' }}>{t("试题（")}{item.data.homework?.length}）</span> :
                            <span style={{ color: '#d9d9d9' }}>{t("试题（0）")}</span>}

                        {
                          item.data.referenceMaterials?.length > 0 ?
                            <span style={{ color: currentindex == index ? '#ffff' : 'var(--primary-color)' }}>{t("参考资料（")}{item.data.referenceMaterials?.length}）</span> :
                            <span style={{ color: '#d9d9d9' }}>{t("参考资料（0）")}</span>

                        }

                        {
                          parameterConfig.case_display == 'true' && <>
                            { item.data.caselist?.length > 0 ?
                                <span style={{ color: currentindex == index ? '#ffff' : 'var(--primary-color)' }}>{t("案例（")}{item.data.caselist?.length}）</span> :
                                <span style={{ color: '#d9d9d9' }}>{t("案例（0）")}</span>
                            }
                          </>
                        }
                      </div>
                    </div>);

                })}

            </div>
            {/* </Checkbox.Group> */}
          </div>
          <div className='right_box'>
            <div className='tabs'>
              <div onClick={() => setCurrentTab('resource')} className={currentTab === 'resource' ? 'active' : ''}>{t("资源")}</div>
              <div onClick={() => setCurrentTab('topic')} className={currentTab === 'topic' ? 'active' : ''}>{t("试题")}</div>
              <div onClick={() => setCurrentTab('reference')} className={currentTab === 'reference' ? 'active' : ''}>{t("参考资料")}</div>
              { parameterConfig.case_display == 'true' && <div onClick={() => setCurrentTab('case')} className={currentTab === 'case' ? 'active' : ''}>{t("案例")}</div>}
            </div>
            <Divider className='Divider' />
            <Row align="middle">
              <Col span={24}>
                {currentTab === 'resource' &&
                  <>
                    <Dropdown overlay={resourceMenu}>
                      <Button type="primary" disabled={!collapsedata.length} loading={modalLoading}>
                        {t("添加资源")} <DownOutlined />
                      </Button>
                    </Dropdown>
                    <Button style={{ width: '100%' }} disabled={!checkedList.length} type="primary" onClick={() => deleteAllRouse()}>{t("解除")}</Button>
                  </>
                }
                {
                  currentTab === 'topic' &&
                  <>
                    <Button
                      type='primary' disabled={!collapsedata.length} loading={modalLoading}
                      onClick={() => setTopicVisible(true)}>{t("添加试题")}

                    </Button>
                    <Button style={{ width: '100%' }} disabled={!checkedTopicList.length} type="primary" onClick={() => deleteAllTopic()}>{t("解除")}</Button>
                  </>
                }
                {
                  currentTab === 'reference' &&
                  <>
                    <Dropdown overlay={referenceMenu}>
                      <Button type="primary" disabled={!collapsedata.length} loading={modalLoading}>
                        {t("添加参考资料")} <DownOutlined />
                      </Button>
                    </Dropdown>
                    <Button style={{ width: '100%' }} disabled={!checkedReferenceList.length} type="primary" onClick={() => deleteAllReference()}>{t("解除")}</Button>
                  </>
                }
                {
                  currentTab === 'case' &&
                  <>
                    <Button
                      type='primary' disabled={!collapsedata.length} loading={modalLoading}
                      onClick={() => setCaseVisible(true)}>{t("添加案例")}

                    </Button>
                    <Button style={{ width: '100%' }} disabled={!checkedCaseList.length} type="primary" onClick={() => deleteAllCase()}>{t("解除")}</Button>
                  </>
                }

              </Col>
            </Row>
            <Divider className='Divider' />
            {currentTab === 'resource' && (
              <div className="bind_img_model_view">
                <Table
                  columns={columns2}
                  size={'small'}
                  dataSource={collapsedata[currentindex]?.data.bindresource}
                  rowKey="tempid"
                  pagination={false}
                  scroll={{ y: 300 }}
                  rowSelection={resourceRowSelection} />

              </div>
            )
          }
          {
            currentTab === 'topic' && (
              <div className='bind_img_model_view'>
                <Table
                  columns={columns}
                  loading={loading}
                  size={'small'}
                  // allTopicList
                  dataSource={collapsedata[currentindex]?.data.homework}
                  rowKey={'tempid'}
                  pagination={false}
                  scroll={{ y: 300 }}
                  rowSelection={topicRowSelection} />

              </div>
            )
          }
          {
            currentTab === 'reference' && (
              <div className='bind_img_model_view'>
                <Table
                  columns={columns2}
                  size={'small'}
                  dataSource={collapsedata[currentindex]?.data.referenceMaterials}
                  rowKey="tempid"
                  pagination={false}
                  scroll={{ y: 300 }}
                  rowSelection={referenceRowSelection} />
              </div>
            )
          }
          {
            currentTab === 'case' && (
              <div className='bind_img_model_view'>
                <Table
                  columns={columns3}
                  size={'small'}
                  dataSource={collapsedata[currentindex]?.data.caselist}
                  rowKey="tempid"
                  pagination={false}
                  scroll={{ y: 300 }}
                  rowSelection={caseRowSelection} />

              </div>
            )
          }

          </div>
        </div>
      </Modal>
      {
        modalVisible ?
          <ResourceModal
            currentname={collapsedata[currentindex]?.data.label}
            treeData={modalTreeData}
            visible={modalVisible}
            onConfirm={(e: any) => {
              if(currentTab === 'resource') {
                addrecouse(e);
              } else if(currentTab === 'reference') {
                addReference(e);
              }
            }}
            PointConfirm={(e: any) => {
              addpoint(e);
            }}
            CanvasConfirm={(e: any) => {
              addrecouse(e);
            }}
            DocumentConfirm={(e: any) => {
              adddocumentpoint(e);
            }}
            showPoint={true}
            onCancel={() => setModalVisible(false)}
            onShowDetail={(id, detail) => {
              setEntityPreview({
                id: id,
                name: detail.name,
                type: detail.type
              });
              setEntityModalVisible(true);
            }}
            multi={true}
          /> : null}

      {
        topicVisible ?
          <TopicSelectModal
            visible={topicVisible}
            onConfirm={addTopic}
            disabled={[3]}
            onAdd={() => { }}
            onclose={() => setTopicVisible(false)} /> :
          null}


      {/* 资源预览modal */}
      <ResourcePreviewModal
        modalVisible={entityModalVisible}
        modalClose={() => setEntityModalVisible(false)}
        resource={entityPreview} />

      {/* 选择一键绑定的对象 */}
      <Modal title={isbtntype == 1 ? t("一键绑定") : t("一键解除")} open={bindtypevisible} onOk={() => {
        if (selecchebox.length) {
          if (isbtntype == 1) {
            bingall();
          } else {
            removbindall();
          }

          setBindtypevisible(false);
          setSelecchebox(['resource', 'topic']);
        } else {
          message.info(t('请选择对象！'));
        }
      }}
        onCancel={() => {
          setBindtypevisible(false);
          setSelecchebox(['resource', 'topic']);
        }}>
        <p>{t("对所有知识节点一键")}{isbtntype == 1 ? t("绑定") : t("解除")}：</p>
        <Checkbox.Group options={isbtntype == 1 ? optionsWithDisabled : optionsWithDisabled2} value={selecchebox}
          onChange={(e) => setSelecchebox(e)} />

      </Modal>

      {caseVisible && <CaseModal chapterItem={null} open={caseVisible} onOk={(id,name) => {
          addcase([{id,name}]);
          setCaseVisible(false);
      }} onClose={() => setCaseVisible(false)} />}
      <CasePreviewModal open={casePreviewOpen} onClose={() => setCasePreviewOpen(false)} data={casePreviewData} />

      {uploadVisible && (
        <Modal
          title={t(currentTab === 'resource' ? "上传资源" : "上传参考资料")}
          open={uploadVisible}
          footer={null}
          onCancel={() => setUploadVisible(false)}
          width={800}
        >
          <UploadFile
            onlyVideo={false}
            mapid={mapid}
            mapname={mapinfo.mapName}
            courseid={courseid}
            coursename={coursename}
            onSave={(data) => {
              if(data && data.length > 0) {
                if(currentTab === 'resource') {
                  handleUploadSuccess(data[0]);
                } else if(currentTab === 'reference') {
                  handleReferenceUploadSuccess(data[0]);
                }
              }
            }}
          />
        </Modal>
      )}
    </div>);

};

export default Bindmange;
