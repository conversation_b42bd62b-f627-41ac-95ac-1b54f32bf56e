import React, { FC, useEffect, useState } from "react";
import "./index.less";
import { Drawer, Steps } from "antd";
import { reqLogs } from "@/api/addCourse";
import moment from "moment";
import {logTypeText, logTypeText2} from "@/permission/moduleCfg";

interface IProps {
  entityType: string;
  visible: boolean;
  list?: any[];
  id?: string;
  open: boolean;
  onClose: () => void;
}
const LoggerModal: FC<IProps> = ({entityType, visible,id, list, open, onClose }) => {
  console.log('entityType',entityType)
  const [logList, setLogList] = useState<any>([]);
  const getLog = (courseId: string) => {
    reqLogs({ courseId }).then((res: any) => {
      if (res.status === 200) {
        setLogList(res.data?.map((item: any) => ({
          title: `${moment(item.approvalTime).format('YYYY-MM-DD HH:mm:ss') || '-'} ${item.processName}`,
          subTitle: item.approvalUserName,
          description: <>
            <div className="review-des">{ entityType === 'template' ? logTypeText2[item.operateType] : logTypeText[item.operateType]}</div>
            {item.operateType === 3 && <div className="review-des">驳回理由：{item.rejectReason || "无"}</div>}
          </>
        })));
      }
    });
  };
  useEffect(() => {
    if (list) {
      setLogList(list)
    } else if (id) {
      getLog(id);
    }
  }, [id, list,visible]);
  return <Drawer className="drawer-logger" title="审核日志" onClose={onClose} open={open}>
    <Steps
      progressDot
      // current={1}
      direction="vertical"
      items={logList}
    />
  </Drawer>;
};

export default LoggerModal;
