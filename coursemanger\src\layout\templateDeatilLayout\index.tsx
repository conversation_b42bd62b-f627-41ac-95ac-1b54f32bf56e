import React, {FC, useState, useEffect, useMemo} from 'react';
import './index.less';
import baseInfo from '@/api/baseInfo';
import { releaseCourse, offShelfCourse, updataCourse } from '@/api/mooclist';
import {useSelector, useHistory, useDispatch, useLocation} from 'umi';
import {Menu, message, Button, Modal, Radio, Input,Tooltip} from 'antd';
import { IconFont } from '@/components/iconFont';
import courseTemplate from '@/api/courseTemplate';
import Loading from '@/components/loading/loading';
import { IGlobalModelState } from '@/models/global';
import Icon, { LeftOutlined, LeftCircleFilled } from '@ant-design/icons';
import { ReactComponent as homework_icon } from '@/assets/imgs/icon/homework.svg';
import Header from '@/components/Header';
import NPUHeader from "@/components/NPUHeader/index.tsx";
import { CUSTOMER_NPU } from '@/permission/moduleCfg';
import useLocale from '@/hooks/useLocale';
import TplShareModal from '@/components/TemplateShareModal';
import LoggerModal from '@/components/LoggerModal';
import {getteacherlist} from "@/api/teacher";
const shareTypes = ["校内共享", "院内共享", "自定义共享"]

interface IauthorityList {
  icon: any;
  name: string;
  path: string;
  authorit: string;
  key: number;
}

const HomeworkIcon = (props: any) => <Icon component={homework_icon} {...props} />;

const TemplateDeatilLayout: FC<{}> = ({ children }) => {
  const { t } = useLocale();
  const location = useLocation()
  console.log('location',location)
  const searchParams = new URLSearchParams(location.search);
  const isSh = JSON.parse(searchParams.get('isSh') as string);
  console.log('isSh',isSh,typeof (isSh))
  let history: any = useHistory();

  const dispatch = useDispatch();
  const { userInfo } = useSelector<any, any>(config => config.global);
  const [courseData, setCourseData] = useState<any>();
  const [basicCollege, setBasicCollege] = useState<string>('');
  const [basicMajors, setBasicMajors] = useState<string>('');
  const [basicCover, setBasicCover] = useState<string>('');
  const [publishCount, setPublishCount] = useState<string>('');
  const [resourcesCount, setResourcesCount] = useState<string>('');
  const [peopleCount, setPeoplCount] = useState<string>('');
  const [publishStatus, setPublishStatus] = useState<number>(0);
  const [approvalStatus,setApprovalStatus] = useState<number | undefined>(undefined)
  const [processInstance,setProcessInstance] = useState<string | undefined | null>(undefined)
  const [authorityList, setAuthorityList] = useState<IauthorityList[]>([]);
  const [releaseValue, setReleaseValue] = useState<string>('no');
  const [release, setRelease] = useState<boolean>(false);
  const [releaseOrNot, setReleaseOrNot] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  const [shareLoading, setShareLoading] = useState<boolean>(false);
  const [tplShareVisible, setTplShareVisible] = useState<boolean>(false);
  const [remove,setRemove] = useState(false)
  const [removeLoading,setRemoveLoading] = useState(false)
  const queryData = history.location.query;
  const layouechange: any[] = useSelector<any, any>(
    (state) => state.updata.layoutUpdata);
  // 审核日志
  const isSuper = useMemo(() => {
    if (userInfo?.roles) {
      return (
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_sys_manager') || //是否是系统管理员
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_course_manager') || //是否是课程管理员
        userInfo.roles
          ?.map((item: any) => item.roleCode)
          ?.includes('r_second_manager') || //第二权限
        userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1')
      );
    } else {
      return false;
    }
  }, [userInfo]);

  const [logOpen,setLogOpen] = useState(false)
  const [logList,setLogList] = useState([])
  //驳回
  const [refuse,setRefuse] = useState(false)
  const [rejectReason, setRejectReason] = useState<string>('');
  const handleRemoveOK = () => {
    setRemoveLoading(true)
    courseTemplate.releasestatus2({ contentId: queryData.id,state: 2 }).then((res:any) => {
       if (res.status === 200) {
         message.success('撤回成功')
         getName();
       }
    }).catch(() => {
      setRemove(false)
      setRemoveLoading(false)
      message.error('撤回失败')
    }).finally(() => {
      setRemove(false)
      setRemoveLoading(false)
    })

  }
  const handleReleseOK = () => {
    courseTemplate.releasestatus2({ contentId: queryData.id,reasonRejection: rejectReason, state: 0}).then((res: any) => {
      if (res && res.message === 'OK') {
        getName();
        message.success(t('驳回成功！'));
        setRefuse(false)
      } else {
        getName();
        message.error(t('驳回失败！'));
        setRefuse(false)
      }
      dispatch({
        type: 'updata/changeCourse',
        payload: {}
      });
    })
  }
  const { parameterConfigObj } = useSelector<
    { global: IGlobalModelState; },
    IGlobalModelState>(
      (state) => state.global);
  // document.title = '教学空间';

  useEffect(() => {
    // history.push(
    //   `/tempatedetail/courseInfo?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}`,
    // );
    getName();
    getAct();
  }, [layouechange]);

  useEffect(() => {
    const key = history.location.pathname.replace("/tempatedetail/", "");
    setSelectedKeys([key]);
  }, [history.location]);

  // 共享下架课程
  const handleReleaseOK = (data?: any) => {
    let num = releaseOrNot ? 1 : 0;
    setShareLoading(true);
    if (num == 1) {
      const isReview = parameterConfig.template_course_release_review == 'true'
      if(!isReview) {
        courseTemplate.personaltoshare(queryData.id, data).then((res) => {
          if (res && res.message === 'OK') {
            message.success(t('共享成功'))
            // message.success( isSuper ? t('提交成功，您是管理员，已为您自动完成课程共享审核') : parameterConfig.template_course_release_review == 'true' ? t('课程已提交至管理员审核'): t('共享成功'));
            courseTemplate.copyhomework({
              "orgCourseId": queryData.id,
              "targetCourseId": res.data
            });
          } else {
            message.error(res.message);
          }
          getName();
          setRelease(false);
          dispatch({
            type: 'updata/changeCourse',
            payload: {}
          });
        }).finally(() => {
          setShareLoading(false);
        });
      } else {
        Modal.confirm({
          title: '共享',
          icon: null,
          content: t("共享后，将提交至管理员进行审核共享，是否确定提交"),
          onOk() {
            courseTemplate.personaltoshare(queryData.id, data).then((res) => {
              if (res && res.message === 'OK') {
                message.success(isSuper ? t('提交成功，您是管理员，已为您自动完成课程共享审核') : t('课程已提交至管理员审核'))
                // message.success( isSuper ? t('提交成功，您是管理员，已为您自动完成课程共享审核') : parameterConfig.template_course_release_review == 'true' ? t('课程已提交至管理员审核'): t('共享成功'));
                courseTemplate.copyhomework({
                  "orgCourseId": queryData.id,
                  "targetCourseId": res.data
                });
              } else {
                message.error(res.message);
              }
              getName();
              setRelease(false);
              dispatch({
                type: 'updata/changeCourse',
                payload: {}
              });
            }).finally(() => {
              setShareLoading(false);
            });
          },
          onCancel() {
            setRelease(false);
          }
        })
      }


    } else {
      console.log('data',data)
      courseTemplate.releasestatus(0, [queryData.id]).then((res: any) => {
        if (res && res.message === 'OK') {
          getName();
          message.success(t('取消共享成功！'));
          setRelease(false);
        } else {
          getName();
          message.error(t('取消共享失败！'));
          setRelease(false);
        }
        dispatch({
          type: 'updata/changeCourse',
          payload: {}
        });
      }).finally(() => {
        setShareLoading(false);
      });
    }
  };

  const getName = () => {
    courseTemplate.getTemplateDetail(queryData.id).then((res) => {
      if (res && res.message === 'OK') {
        setCourseData(res.data);
        setPublishStatus(res.data.entityData.release_type === "public" ? 1 : 0);
        setApprovalStatus(res.data.entityData.approvalStatus)
        setProcessInstance(res.data.entityData.process_instance)
        setBasicCollege(
          res.data.entityData.college ?
            res.data.entityData.college.map((item: any) => item.value) :
            '');

        let majors: string[] = [];
        res.data.entityData.major &&
          res.data.entityData.major.forEach((item: any) => {

            if (Array.isArray(res.data.entityData.major)) {
              majors.push(item.value);
            } else {
              majors.push(item.split(',')[1]);
            }
          });
        setBasicMajors(majors.join('，'));
        setBasicCover(res.data.entityData.cover);
        dispatch({
          type: 'moocCourse/updateState',
          payload: {
            templateCourseDetail: res.data
          }
        });
      }
    });
  };
  // const updataCourseState = () => {
  //   courseTemplate.updatatemCourse([queryData.id]).then(res => {
  //     if (res && res.message === 'OK') {
  //       message.success('课程更新成功');
  //       getName();
  //       dispatch({
  //         type: 'updata/changeCourse',
  //         payload: {},
  //       });
  //     } else {
  //       message.error(res.errorMsg);
  //     }
  //   });
  // };
  const getAct = () => {
    baseInfo.getactivities(queryData.id ?? 1).then((res) => {
      if (res && res.message === 'OK') {
        // console.log(res)
        setPublishCount(res.data.publishCount);
        setResourcesCount(res.data.resourcesCount);
      }
    });
  };
  const { parameterConfig } = useSelector<any, any>((state) => state.global);

  console.log('111',parameterConfigObj)

  return (
    <div className="edit-course">
      {parameterConfig.target_customer === CUSTOMER_NPU ? <NPUHeader /> : <Header />}
      <div className="edit-top">
        <div className="top-right">
          <div className="info_box">
            <a onClick={() => window.open(`#/coursetemplate/${queryData.myOrShare ? "my" : "share"}template`, "_self")}>
              {parameterConfig.target_customer === CUSTOMER_NPU ? <LeftCircleFilled /> : <><LeftOutlined />{t("返回")}</>}
            </a>
            <span className="course-title">{courseData?.name}</span>
            {publishStatus === 1 && <span className="publishStatus published">{t("已共享")}：{shareTypes?.[courseData?.entityData?.sharedType]}</span>}
            {/* <div className="course-data">
               <span>发布数：</span>
               <div>
                 <span className="publish">{publishCount}</span> /{' '}
                 {resourcesCount}
               </div>
              </div> */}
          </div>
          {queryData.type == 'edit' &&
            <div className="buttom_box">
              {/*共享状态*/}
              {  publishStatus === 1 && <>
                    <Button
                      className="btn_margin"
                      type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                      onClick={() => {
                        setReleaseOrNot(false);
                        setRelease(true);
                      }}>
                      {t("取消共享")}
                    </Button>
              </> }
              {/*待审核状态*/}
              {
                approvalStatus  === 0 &&  processInstance && (
                  <>
                    {userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_sys_manager')  ?     <>
                      <Button
                        className="btn_margin"
                        type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                        onClick={() => {
                          setReleaseOrNot(true);
                          setTplShareVisible(true);
                        }}>
                        {t("共享")}
                      </Button>
                      <Button
                        className="btn_margin"
                        type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                        onClick={() => {
                          setRefuse(true)
                        }}>
                        {t("驳回")}
                      </Button>
                    </> : userInfo.roles?.map((item: any) => item.roleCode)?.includes('r_teacher') ?       <>
                      <Tooltip
                        title={
                          <div className="recall-box">
                            {t('已提交审核，')}
                            <a
                              onClick={() => {
                                setReleaseOrNot(false);
                                setRemove(true);
                              }}
                            >
                              {t('撤回')}
                            </a>
                          </div>
                        }
                      >
                        <Button
                          className="btn_margin"
                          disabled
                          type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                          onClick={() => {
                            setReleaseOrNot(true);
                            setTplShareVisible(true);
                          }}>
                          {t("共享")}
                        </Button>
                      </Tooltip>
                    </> : <></>  }
                  </>


                )
              }
              {/*未共享状态*/}
              {
                publishStatus === 0 && !processInstance &&
                        <Button
                        className="btn_margin"
                        type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                        onClick={() => {
                          setReleaseOrNot(true);
                          setTplShareVisible(true);
                        }}>
                        {t("共享")}
                      </Button>
              }
              {/*审核日志*/}
              { parameterConfigObj.kczx?.includes('template_course_release_review') &&
              <Button
                className="btn_margin"
                type={`${parameterConfig.target_customer === CUSTOMER_NPU ? 'default' : 'primary'}`}
                onClick={() => {
                  setLogOpen(true);
                }}>
                {t("审核日志")}
              </Button>
              }
            </div>}
        </div>
      </div>

      <div className="edit-detail">
        <div className="edit-menu">
          <div className="course-img">
            <img src={basicCover}></img>
          </div>
          <Menu
            style={{ width: 230 }}
            defaultSelectedKeys={[queryData.key || 'courseInfo']}
            selectedKeys={selectedKeys}
            mode="inline"
          // onSelect={(e: any) => setSelectedKeys(e.selectedKeys)}
          >
            <Menu.Item
              key="courseInfo"
              icon={<IconFont type="iconkechengxinxi" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/courseInfo?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=1`)}>


              {t("基本信息")}

            </Menu.Item>
            <Menu.Item
              key="chapter"
              icon={<IconFont type="iconwenzhangpailie2-22" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/chapter?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=2`)}>


              {t("章节内容")}

            </Menu.Item>
            <Menu.Item
              key="teachingteam"
              icon={<IconFont type="iconshoukejiaoshi" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/teachingteam?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=5`)}>


              {t("教学团队")}

            </Menu.Item>
            <Menu.Item
              key="resources"
              icon={<IconFont type="iconziyuan1" />}
              onClick={() =>
                history.push(
                  `/tempatedetail/resources?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=6`)}>


              {t("资源")}

            </Menu.Item>
            {parameterConfigObj.kczx?.includes('course_library_school_assignment_display') && <Menu.Item
              key="homework"
              icon={<HomeworkIcon />}
              onClick={() =>
                history.push(
                  `/tempatedetail/homework?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=3`)}>


              {t("作业")}

            </Menu.Item>}
            {queryData.type === 'edit' ?
              <Menu.Item
                key="usage"
                icon={<IconFont type="iconshiyongqingkuang1" />}
                onClick={() =>
                  history.push(
                    `/tempatedetail/usage?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}&key=7`)}>


                {t("使用情况")}

              </Menu.Item> :

              ''}

            {/* soso统计 */}
            {/* <Menu.Item
               key="8"
               icon={<IconFont type="iconshezhi7" />}
               onClick={() =>
                 history.push(
                   `/tempatedetail/statistics?id=${queryData.id}&type=${queryData.type}&myOrShare=${queryData.myOrShare}`,
                 )
               }
              >
               统计
              </Menu.Item> */}
          </Menu>
        </div>
        <div className="edit-content">{children}</div>
      </div>
      <TplShareModal visible={tplShareVisible} onClose={() => setTplShareVisible(false)} onConfirm={handleReleaseOK} />
       {/*审核日志弹窗*/}
      <LoggerModal entityType={courseData?.entityData?.entityType}  visible={logOpen} id={queryData.id} open={logOpen} onClose={() => setLogOpen(false)} />
      {/*驳回*/}
      <Modal
        title="驳回"
        visible={refuse}
        onOk={handleReleseOK}
        onCancel={() => setRefuse(false)}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Input.TextArea
            rows={4}
            style={{ width: '100%' }}
            placeholder={t('请输入驳回原因')}
            onChange={(e: any) => {
              setRejectReason(e.target.value);
            }}
          />
        </div>
      </Modal>
      <Modal
        title={releaseOrNot ? t("共享") : t("取消共享")}
        visible={release}
        onOk={handleReleaseOK}
        onCancel={() => setRelease(false)}
        confirmLoading={shareLoading}>

        {/* {releaseOrNot ? (
           <div>
             <Radio.Group
               onChange={(e: any) => setReleaseValue(e.target.value)}
               value={releaseValue}
             >
               <Radio value="share">共享</Radio>
               <Radio value="no">不共享</Radio>
             </Radio.Group>
           </div>
          ) : (
           // `确定要${releaseOrNot ? '发布' : '下架'}该课程吗`
           `确定要下架该课程吗`
          )} */}
        {releaseOrNot ? t("确定将课程资源包至共享课程资源中？") : t("确定将课程资源包移除共享课程资源？")}
      </Modal>
      <Modal
        title={t("撤回")}
        visible={remove}
        onOk={handleRemoveOK}
        onCancel={() => setRemove(false)}
        confirmLoading={removeLoading}>
        {t("确定撤回课程资源包？")}
      </Modal>
      <Loading />
    </div>);

};

export default TemplateDeatilLayout;
