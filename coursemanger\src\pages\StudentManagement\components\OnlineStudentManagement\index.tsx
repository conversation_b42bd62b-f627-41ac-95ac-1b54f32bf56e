import React, { FC, useState, useEffect, ReactText } from 'react';
import './index.less';
import { getstudentlist, deletestudent, getmicroMajorStudentlist, getMicroPermission,studentExport } from '@/api/student';
import { fetchClassmates, metaData, queryColleges, queryInvitationCode } from '@/api/course';
import { useSelector, useDispatch, useHistory } from 'umi';
import { Form, Table, Button, Input, Avatar, Modal, message, Radio, Popover, Dropdown, Select, TreeSelect } from 'antd';
import { FormOutlined, DeleteOutlined, UserOutlined } from '@ant-design/icons';
import AddStudentModal from '@/components/AddStudentModal';
import QRCode from 'qrcode.react';
import useLocale from '@/hooks/useLocale';
import ExcelImportModal from "../ExcelImportModal";
import { ITree, setTreeData } from '@/pages/BaseInfo';

const sortObj = {
  "name": "isNameAsc",
  "sex": "isSexAsc",
  "jobNumber": "isUserCodeAsc",
  "college": "isCollegeAsc",
  "major": "isMajorAsc",
}

const OnlineStudentManagement: FC<{}> = () => {
  const { t } = useLocale();
  const [searchform] = Form.useForm();
  const history: any = useHistory();
  const [addStudentVis, setAddStudentVis] = useState<boolean>(false);
  const [importClassVis, setImportClassVis] = useState<boolean>(false);
  const [qrCodeVisible, setQrCodeVisible] = useState<boolean>(false);
  const [deleteVis, setDeleteVis] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<Array<IStudent.IstudentData>>([]);
  const [total, setTotal] = useState<number>(0);
  const [jobNumber, setJobNumber] = useState<string>();
  const [studentName, setStudentName] = useState<string>();
  const [batchOrAlone, setBatchOrAlone] = useState<string>('alone');
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [operationData, setOperationData] = useState<any>([]);
  const [iPagination, setIPagination] = useState<any>({
    page: 1,
    size: 10
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [operateFlag, setOperateFlag] = useState<number>(0);
  const [operateFlagTwo, setOperateFlagTwo] = useState<number>(3);
  const [invitationCode, setInvitationCode] = useState<string>("");
  const { courseDetail } = useSelector((state: any) => state.moocCourse);
  const [excelModal, setExcelModal] = useState<boolean>(false);
  const [isManager, setisManager] = useState<boolean>(false);
  // 确认按钮loading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [collegeList, setCollegeList] = useState<any>([]);
  const [majorList, setMajorList] = useState<ITree[]>([]);
  const college = Form.useWatch("college", searchform);
  const major = Form.useWatch("major", searchform);
  useEffect(() => {
    getTeacher();
  }, [jobNumber, studentName, iPagination, college, major]);

  useEffect(() => {
    getInvitationCode();
    getMicroPermission({courseId: history.location.query.id}).then(res => {
      if (res && res.status === 200) {
        setisManager(res.data.isManager)
      }
    })
    getCollegeList();
    courseCategory();
  }, []);
  const getTeacher = () => {
    const isMicroMajor = history.location.query.type === 'microMajor'
    setLoading(true);
    const pageUrl = new URLSearchParams("");
    for (let key in iPagination) {
      if (iPagination.hasOwnProperty(key)) {
        const value = encodeURIComponent(iPagination[key]);
        pageUrl.append(key, value);
      }
    }
    let data = `${isMicroMajor ? 'courseId': 'id'}=${history.location.query.id}&${pageUrl.toString()}`;
    if (jobNumber) {
      data = data + `&jobNum=${jobNumber}`;
    }
    if (studentName) {
      data = data + `&name=${studentName}`;
    }
    if (!isMicroMajor && major) {
      data = data + `&major=${major}`;
    }
    if (!isMicroMajor && college) {
      data = data + `&college=${college}`;
    }
    let request = isMicroMajor ? getmicroMajorStudentlist(data): getstudentlist(data)
    request.then((res: IStudent.IstudentDataRes) => {
      if (res && res.status === 200) {
        setDataSource(res.data.results);
        setTotal(res.data.total);
      }
    }).finally(() => {
      setLoading(false);
    });
  };
  const dropDown = [
    {
      key: 1, label: <div className='btn' onClick={() => {
        if (courseDetail?.entityData?.related_courses) {
          setImportClassVis(true);
        } else {
          message.warning(t("还未绑定线下课程，无法导入本班同学"));
        }
      }}>{t("导入本班同学")}</div>
    },
    { key: 2, label: <div className='btn' onClick={() => setQrCodeVisible(true)}>{t("邀请添加")}</div> },
    { key: 3, label: <div className='btn' onClick={() => setAddStudentVis(true)}>{t("手动添加")}</div> },
    { key: 4, label: <div className='btn' onClick={() => setExcelModal(true)}>{t("Excel导入")}</div> },
  ];

  const columns: any = [
    // {
    //   title: '头像',
    //   dataIndex: 'photo',
    //   key: 'photo',
    //   align: 'center',
    //   width: 100,
    //   render: (text: any, record: any) => {
    //     if (record.avatarUrl) {
    //       return <Avatar shape="square" size={64} src={record.avatarUrl} icon={<UserOutlined />} />;
    //     } else {
    //       return <Avatar shape="square" size={64} icon={<UserOutlined />} />;
    //     }
    //   },
    // },
    {
      title: t("序号"),
      dataIndex: "rowNum",
      key: "rowNum ",
      align: 'center'
    },
    {
      title: t("姓名"),
      dataIndex: history.location.query.type === 'microMajor'? 'userName' : 'name',
      key: history.location.query.type === 'microMajor'? 'userName' : 'name',
      align: 'center',
      sorter: history.location.query.type !== 'microMajor'
    },
    {
      title: t("性别"),
      dataIndex: 'sex',
      key: 'sex',
      align: 'center',
      sorter: history.location.query.type !== 'microMajor'
    },
    {
      title: t("学工号"),
      dataIndex: history.location.query.type === 'microMajor'? 'userCode' : 'jobNumber',
      key: history.location.query.type === 'microMajor'? 'userCode' : 'jobNumber',
      align: 'center',
      sorter: history.location.query.type !== 'microMajor'
    },
    {
      title: t("学院"),
      dataIndex: 'college',
      key: 'college',
      align: 'center',
      sorter: history.location.query.type !== 'microMajor'
    },
    {
      title: t("添加方式"),
      dataIndex: 'addType',
      key: 'addType',
      align: 'center',
      render: (text: number) => {
         return text === 0 ? '手动添加' : '系统添加'
      }
    },
    {
      title: t("专业"),
      dataIndex: 'major',
      key: 'major',
      align: 'center',
      sorter: history.location.query.type !== 'microMajor'
    },
    {
      title: t("操作"),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (text: any, record: any) =>
        <div className="action" onClick={() => setDeleteVis(true)}>
          <DeleteOutlined
            onClick={() => {
              setDeleteVis(true);
              setOperationData(record);
              setBatchOrAlone('alone');
            }} />

        </div>

    }];

  const rowSelection = {
    onChange: (newSelectedRowKeys: ReactText[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    }
  };
  const batchDelete = () => {
    if (selectedRowKeys.length) {
      setDeleteVis(true);
      setBatchOrAlone('batch');
    } else {
      message.info(t('请选择要删除的学生'));
    }
  };
  // 检索
  const searchStudent = () => {
    setJobNumber(searchform.getFieldsValue().account);
    setStudentName(searchform.getFieldsValue().keyword);
    setIPagination({ ...iPagination, page: 1 });
  };
  const handleDeleteOk = () => {
    let data: string[] = [];
    if (batchOrAlone === 'alone') {
      data = [operationData.userCode];
    } else {
      data = [...selectedRowKeys];
    }
    deletestudent(history.location.query.id, data, Number(history.location.query.sm ?? 1)).then((res: any) => {
      if (res && res.status === 200) {
        message.success(t('删除成功'));
      } else {
        message.error(res.message);
      }
      setDeleteVis(false);
      getTeacher();
      setSelectedRowKeys([]);
    });
  };
  /**
   * 导入本班同学
   *
   */
  const handleImportClass = () => {
    setConfirmLoading(true);
    // 门次id
    let courseid = history.location.query?.id;
    if (courseDetail?.entityData?.related_courses && courseid) {
      fetchClassmates({
        contentId: courseid,
        courseId: courseDetail?.entityData?.related_courses,
        ifInsert: true,
        operateFlag: operateFlag === 1 ? operateFlagTwo : operateFlag,
      }).then((res: any) => {
        if (res && res.status === 200) {
            message.success(t('导入成功'));
        } else {
          message.error(t('导入失败'));
        }
        setOperateFlag(0);
        setImportClassVis(false);
        getTeacher();
      }).catch(() => {
        message.error(t('导入失败'));
      }).finally(() => {
        setConfirmLoading(false);
      });
    }
  };
  const refresh = () => {
    setIPagination({ ...iPagination, page: 1 });
    getTeacher();
  };
  const getInvitationCode = () => {
    queryInvitationCode(history.location.query.id).then((res: any) => {
      if (res.status === 200) {
        setInvitationCode(res.data?.code);
      }
    });
  };
  const getCollegeList = () => {
    queryColleges().then((res: any) => {
      if (res.status === 200) {
        setCollegeList(res.data?.organization ?? []);
      }
    });
  };
  const courseCategory = () => {
    metaData(true).then(res => {
      if (res && res.success) {
        res.data.forEach((item: any) => {
          if (item.fieldName === 'major') {
            let majorList = setTreeData(JSON.parse(item.controlData));
            setMajorList(majorList);
            // setcurMajorList(majorList);
          }

        });
      }

      // getCourseDetail();
    });
  };
  const [exportLoading,setExportLoading] = useState(false);
  const exportFn = () => {
    setExportLoading(true);
    const isMicroMajor = history.location.query.type === 'microMajor'
    const pageUrl = new URLSearchParams("");
    for (let key in iPagination) {
      if (iPagination.hasOwnProperty(key)) {
        const value = encodeURIComponent(iPagination[key]);
        pageUrl.append(key, value);
      }
    }
    let data = `${isMicroMajor ? 'courseId': 'id'}=${history.location.query.id}&${pageUrl.toString()}`;
    if (jobNumber) {
      data = data + `&jobNum=${jobNumber}`;
    }
    if (studentName) {
      data = data + `&name=${studentName}`;
    }
    if (!isMicroMajor && major) {
      data = data + `&major=${major}`;
    }
    if (!isMicroMajor && college) {
      data = data + `&college=${college}`;
    }

    studentExport(data).then(res => {
      console.log(res,'222')
      const fileUrl = window.URL.createObjectURL(res.data);
      const a = document.createElement('a');
      a.href = fileUrl;
      console.log('url', fileUrl);
      a.setAttribute('download', '学生列表.xlsx');
      a.style.display = 'none';
      a.click();
      a.remove();
      // if (res.status === 200 && res.statusText === 'ok') {
      //   message.success('导出成功')
      // }
    }).catch(() => {
      setExportLoading(false);

    }).finally(() => {
      setExportLoading(false);
    })
  }
  return (
    <div className="student-management">
      <div className="top-search">
      <Form layout={'inline'} form={searchform}>
          <Form.Item label={t("学号")} name="account">
            <Input style={{ width: "150px" }} autoComplete="off" placeholder={t("请输入工号/学号")} onPressEnter={(e:any)=>{
              // setStudentName(e.target.value);
              searchStudent();
            }} />
          </Form.Item>
          <Form.Item label={t("姓名")} name="keyword">
            <Input style={{ width: "150px" }} autoComplete="off" placeholder={t("请输入姓名")} onPressEnter={(e:any)=>{
              // setJobNumber(e.target.value)
              searchStudent();
            }} />
          </Form.Item>
          {history.location.query.type !== 'microMajor' && <>
            <Form.Item
              label={t('学院')}
              name="college"
            >
              <Select
                style={{ width: "200px" }}
                showArrow
                allowClear
                placeholder={t('请选择学院')}
                // onChange={handleSelectCollege}
                showSearch
                filterOption={(input, option: any) => {
                  return (
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  );
                }}
              >
                {collegeList.map((item: any) => {
                  return (
                    <Select.Option value={item.name} key={item.name}>
                      {item.name}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item
              label={t('专业')}
              name="major"
            >
              <TreeSelect
                style={{ width: '200px' }}
                placeholder={t('请选择专业')}
                // treeData={curMajorList}
                allowClear
                showArrow
                treeData={majorList}
                treeNodeFilterProp="title"
                filterTreeNode
              />
            </Form.Item>
          </>}
        </Form>
        <Button type="primary" onClick={searchStudent} style={{ width: 100 }}>{t("检索")}
        </Button>
      </div>
      {(isManager && history.location.query.type === 'microMajor' || history.location.query.type !== 'microMajor') && <div className="top-buttom">
       <Dropdown
          menu={{ items: history.location.query.type === 'microMajor' ? [{ key: 4, label: <div className='btn' onClick={() => setExcelModal(true)}>{t("Excel导入")}</div> }]: dropDown }}
          trigger={["click"]}
          arrow={false}
          placement='bottom'>
           <Button type="primary">{t("添加学生")}</Button>
        </Dropdown>
        {/* <Button
           type="primary"
           style={{ width: 120 }}
           onClick={() => setImportClassVis(true)}
          >
           导入本班同学
          </Button>
          <Button
           type="primary"
           style={{ width: 100 }}
           onClick={() => setAddStudentVis(true)}
          >
           手动添加
          </Button> */}
        <Button style={{ width: 100 }} onClick={batchDelete}>{t("批量删除")}
        </Button>
        <Button type="primary" loading={exportLoading} onClick={exportFn}>{t("学生导出")}</Button>
      </div>}
      <Table
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        rowKey="userCode"
        onChange={(pagination, filters, sorter) => {
          const { current, pageSize } = pagination;
          const { columnKey, order } = sorter
          setIPagination({
            page: current,
            size: pageSize,
            [sortObj[columnKey]]: order === "ascend"
          })

        } }
        pagination={{
          // current: current,
          showSizeChanger: true,
          showQuickJumper: true,
          current: iPagination.page,
          pageSize: iPagination.size,
          // onChange: (page: any, size: any) => {
          //   setIPagination({ page, size });
          // },
          showTotal: (total) => t("共{name}条", String(total)),
          total: total
        }}
        rowSelection={{
          ...rowSelection
        }}
        scroll={{ y: 450 }} />

      <AddStudentModal
        courseSemester={history.location.query.sm ?? 1}
        modalVisible={addStudentVis}
        modalClose={() => setAddStudentVis(false)}
        refresh={refresh} />

      <Modal
        title={t("警告?")}
        visible={deleteVis}
        onOk={() => handleDeleteOk()}
        onCancel={() => setDeleteVis(false)}>

        <div className="deleteprompt">
          <div>{t("确定是否删除该学生？")}</div>
        </div>
      </Modal>
      <Modal
        title={t("导入本班同学")}
        confirmLoading={confirmLoading}
        visible={importClassVis}
        onOk={() => handleImportClass()}
        onCancel={() => setImportClassVis(false)}>

        <div className="deleteprompt">
          <div style={{ marginBottom: "10px" }}>{t("请选择导入方式？")}</div>
          <Radio.Group value={operateFlag} onChange={(e: any) => setOperateFlag(e.target.value)}>
            <Radio value={0}>{t("仅新增和修改")}</Radio>
            <Radio value={1}>{t("覆盖当前学生列表")}</Radio>
          </Radio.Group>
          <br />
          { operateFlag === 1 &&
            <Radio.Group style={{ marginTop: "10px" }} value={operateFlagTwo} onChange={(e: any) => setOperateFlagTwo(e.target.value)}>
              <Radio value={3}>{t("全覆盖")}</Radio>
              <Radio value={4}>{t("仅导入覆盖")}</Radio>
            </Radio.Group>
          }
        </div>
      </Modal>
      <Modal
        title={t("邀请添加")}
        open={qrCodeVisible}
        width={340}
        footer={null}
        onCancel={() => setQrCodeVisible(false)}>

        <div className="qr-code-wrp">
          <div className="code">{t("邀请码：")}{invitationCode}</div>
          <div className="prompt">{t("“ 我的学习 ” 模块中点击 “ 加入课程 ” 输入")}</div>
          {/* <QRCode value={`${location.origin}/learn/course/preview/spoc/${history.location.query.id}`} size={150} level="M" /> */}
        </div>
      </Modal>
      <ExcelImportModal visible={excelModal} onClose={() => {
        setExcelModal(false);
        setIPagination({ ...iPagination, page: 1 });
        getTeacher();
      }} />
    </div>);

};

export default OnlineStudentManagement;
