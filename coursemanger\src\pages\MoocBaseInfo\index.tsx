import { resourceDetail } from '@/api/addCourse';
import baseInfo from '@/api/baseInfo';
import { getSemester, metaData, queryColleges } from '@/api/course';
import { cultivationList } from '@/api/mooclist';
import { getteacherlist } from '@/api/teacher';
import TagInput from '@/components/AddCourseForm/TagInput';
import CoverModal from '@/components/CoverModal';
import FilePreviewModal from '@/components/FilePreviewModal';
import VideoModal from '@/components/VideoModal';
import useCover from '@/hooks/useCover';
import useLocale from '@/hooks/useLocale';
import { IGlobalModelState } from '@/models/global';
import { cultivationLevel, languageList } from '@/pages/BaseInfo/constants';
import { CUSTOMER_SHTECH, CUSTOMER_PPSUC, CUSTOMER_UTCM } from '@/permission/moduleCfg';
import { getLabels, toArray } from '@/utils';
import { CloudUploadOutlined, EyeOutlined } from '@ant-design/icons';
import {
  Button,
  DatePicker,
  Form,
  Image as Img,
  Input,
  message,
  Modal,
  Select,
  Tooltip,
  TreeSelect,
} from 'antd';
import 'antd/es/modal/style';
import 'antd/es/slider/style';
import moment from 'moment';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import './index.less';

const { RangePicker } = DatePicker;
const { SHOW_CHILD } = TreeSelect;
interface ITree {
  title: string;
  key: string;
  children?: ITree[];
}

interface ISemesters {
  id: string;
  code: string;
  name: string;
  showSemester?: string;
}

export interface IMoocBaseInfoRef {
  preservation: () => Promise<boolean | { param: any; check: number }>;
}
// let isEdit = false
const BaseInfo = forwardRef<IMoocBaseInfoRef, {}>((props, ref) => {
  useImperativeHandle(ref, () => ({
    preservation,
  }));
  const location: any = useLocation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [uploadLoading, setUploadLoading] = useState(false);
  const [cover, setCover] = useState('');
  const [detailData, setDetailData] = useState<any>({});
  const [courseCategoryList, setcourseCategoryList] = useState<any>({});
  const [collegeList, setCollegeList] = useState<any>([]);
  const [courseSubjectList, setcourseSubjectList] = useState<any>({});
  const [majorList, setMajorList] = useState<ITree[]>([]);
  // const [curMajorList, setcurMajorList] = useState<ITree[]>([]); // 选中学院后联动的专业

  const [collegeName, setCollegeName] = useState([]);
  const [classificationName, setClassificationName] = useState([]);
  const [majorName, setMajorName] = useState([]);
  const [subjectName, setSubjectName] = useState<any>([]);
  const subjectObject = useRef<any>({
    primary_classification: '',
    secondary_classification: '',
  });
  const courseDetailRef = useRef<any>({});
  const [semesters, setSemesters] = useState<ISemesters[]>([]);
  const [coverModalVisible, setCoverModalVisible] = useState<boolean>(false);
  const [teacherNames, setTeacherNames] = useState<string[]>([]);
  const { t } = useLocale();

  // const [isEdit, setIsEdit] = useState<boolean>(false)

  // const defaultCover = '/rman/static/images/mooc.png';
  // const restoreDefault = () => {
  //   isEdit = true
  //   setCover(defaultCover);
  // };
  const { courseDetail, canPageEdit } = useSelector<Models.Store, any>(
    state => state.moocCourse,
  );

  const { parameterConfig } = useSelector<any, any>(state => state.global);
  const { homePageConfig } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  const newcourseDetail = useRef(courseDetail);

  const timer = useRef<any>(null);
  const [oldResource, setOldResource] = useState<any>({});
  const [resource, setResource] = useState<any>({});
  const [videoVisible, setVideoVisible] = useState<boolean>(false);
  const [videoPreviewVisible, setVideoPreviewVisible] = useState<boolean>(
    false,
  );
  const { coverSetting, setCoverSetting, handleChangeCover } = useCover(
    location.query,
  );
  const [nameChangeTag, setNameChangeTag] = useState<boolean>(false);
  const [getCultivationList, setCultivationList] = useState<any>([]);
  useEffect(() => {
    form.setFieldsValue({ cover });
  }, [cover]);

  useEffect(() => {
    // setDetailData(res.data);
    console.info(courseDetail, newcourseDetail);
    if (Object.keys(courseDetail).length) {
      if (courseDetail.entityData.cover_video) {
        setResource(JSON.parse(courseDetail.entityData.cover_video));
      }
      setCover(
        courseDetail.entityData.cover ? courseDetail.entityData.cover : '',
      );

      const { name, entityData } = courseDetail;
      const {
        college,
        major,
        describe,
        target,
        tag,
        start_time,
        course_level,
        end_time,
        subject,
        classification,
        semester_teaching_courses,
        languages,
        educational,
      } = entityData;
      setClassificationName(entityData.classificationName);
      setCollegeName(entityData.collegeName);
      setMajorName(entityData.majorName);
      setSubjectName(entityData.subjectName);
      subjectObject.current = {
        primary_classification: entityData.primary_classification,
        secondary_classification: entityData.secondary_classification,
        parentCode: '',
      };
      let tempSubject = subject;
      if (subject?.length === 2) {
        //第一个是父级节点code
        subjectObject.current.parentCode = subject[0];
        tempSubject = [subject[1]];
      }
      form.setFieldsValue({
        college,
        name,
        majors: major,
        describe,
        tag,
        semester_teaching_courses,
        target,
        date: start_time === 0 ? [] : [moment(start_time), moment(end_time)],
        subject: tempSubject || [],
        course_level,
        classification: classification ?? undefined,
        languages,
        educational,
      });
      courseDetailRef.current = courseDetail;
    }
  }, [courseDetail]);

  useEffect(() => {
    if (resource.resourceId && !resource.src) {
      loopGetResource();
    }
  }, [resource.resourceId]);
  const [tagLabels, setTagLabels] = useState<string[]>([]);
  useEffect(() => {
    getCultivationLevel();
    courseCategory();
    querySemester();
    // queryTeachers();
    getCollegeList();
    getLabels(undefined, location.query.id).then(labels => {
      setTagLabels(labels?.slice(0, 12) ?? []);
    });
    // window.onbeforeunload = () => {
    //   if (isEdit) {
    //     return '还没有完成,确认退出吗?';
    //   }
    // };
    // return () => {
    //   if (isEdit) {
    //     const validateArr = courseDetailRef.current.entityData.publishStatus == 1 ? ['name', 'subject', 'college'] : ['name']
    //     const data = form.getFieldsValue(true);
    //     const flag = Object.keys(data).filter(item => validateArr.includes(item)).some(item => !(data[item]?.length > 0))
    //     if (flag) {
    //       message.warning("有必填项未填，未保存成功!");
    //       return
    //     }
    //     preservation()
    //   }
    // }
  }, []);

  useEffect(() => {
    if(coverModalVisible){
      queryTeachers();
    }
  }, [coverModalVisible]);
  const getCultivationLevel = () => {
    cultivationList().then(res => {
      console.log(res.data);
      if (res.status === 200) {
        setCultivationList(res.data);
      }
    }).catch(()=>{})
  };
  const queryTeachers = () => {
    getteacherlist({ id: location.query.id }).then(res => {
      if (res.status === 200) {
        const names = res.data.map((item: any) => item.name);
        setTeacherNames(names);
      }
    });
  };

  const querySemester = () => {
    getSemester().then((res: any) => {
      if (res.status === 200) {
        setSemesters(res.data);
      } else {
        message.error(res.message);
      }
    });
  };

  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 10 },
  };
  const tailLayout = {
    wrapperCol: { offset: 8, span: 16 },
  };
  const getCollegeList = () => {
    queryColleges().then(res => {
      if (res.status === 200) {
        setCollegeList(res.data?.organization ?? []);
      }
    });
  };
  // 课程分类
  const courseCategory = () => {
    //1 是川大  2是成信大 3高教 4职教 （3,4都是省平台）
    // metaData(['3','4'].includes(homePageConfig.banner_plate_type)?true:false).then(res => {
    metaData(true).then(res => {
      // metaData().then(res => {
      if (res && res.success) {
        res.data.forEach((item: any) => {
          if (item.fieldName === 'classification') {
            setcourseCategoryList(JSON.parse(item.controlData));
          }
          if (item.fieldName === 'subject') {
            // setcourseSubjectList(['3','4'].includes(homePageConfig.banner_plate_type)?item:JSON.parse(item.controlData));
            setcourseSubjectList(item);
          }
          if (item.fieldName === 'major') {
            let majorList = setTreeData(JSON.parse(item.controlData));
            setMajorList(majorList);
            // setcurMajorList(majorList);
          }
          // if (item.fieldName === 'college') {
          //   setCollegeList(JSON.parse(item.controlData));
          // }
        });
        // getCourseDetail();
      }
    });
  };
  const setTreeData = (t: any[]): any => {
    return t.map(item => {
      if (item.children && item.children.length > 0) {
        return {
          title: item.categoryName,
          value: item.categoryCode,
          children: setTreeData(item.children),
          disabled: item.categoryType !== 'major',
        };
      } else {
        return {
          title: item.categoryName,
          value: item.categoryCode,
          disabled: item.categoryType !== 'major',
        };
      }
    });
  };

  // 课程详情
  const getCourseDetail = () => {
    const id = location.query.id;
    baseInfo.getCourseDetails(id).then(res => {
      if (res && res.message === 'OK') {
        setDetailData(res.data);
        setCover(res.data.entityData.cover ? res.data.entityData.cover : '');

        const { name, entityData } = res.data;
        const {
          college,
          major,
          describe,
          target,
          start_time,
          end_time,
          subject,
          classification,
        } = entityData;
        setClassificationName(entityData.classificationName);
        setCollegeName(entityData.collegeName);
        setMajorName(entityData.majorName);
        setSubjectName(entityData.subjectName);
        form.setFieldsValue({
          college,
          name,
          majors: major,
          describe,
          target,
          date: start_time === 0 ? [] : [moment(start_time), moment(end_time)],
          subject: subject || [],
          classification: classification ?? undefined,
        });
      }
    });
  };

  // /**
  //  *
  //  * 学院专业联动
  //  * @param {string[]} colleges
  //  */
  // const handleSelectCollege = (colleges: string[]) => {
  //   setcurMajorList(
  //     majorList.filter((item: any) => colleges.includes(item.key)),
  //   );
  //   form.setFieldsValue({ ...form.getFieldsValue(), majors: [] });
  // };

  const onFinish = (values: any) => {
    console.log('Success:', values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // 遍历目录树 为了把父亲节点的name带上
  const forTree = (tree: any, parent?: any) => {
    return tree?.map((item: any) => {
      return {
        key: item.categoryCode,
        title: item.categoryName,
        parentName: parent ? parent.categoryName : '',
        parentCode: parent ? parent.categoryCode : '',
        value: item.categoryCode,
        children: item.children ? forTree(item.children, item) : [],
      };
    });
  };
  //专业TreeSelect的change函数
  const onProfessionChange = (
    value: any,
    label: any,
    extra: any,
    fieldName: any,
  ) => {
    console.log(value, label, extra, fieldName);
    if (fieldName === 'subject') {
      setSubjectName(label);
      const parentName = extra.triggerNode.props.parentName;
      subjectObject.current = {
        primary_classification: parentName || extra.triggerNode.props.title,
        secondary_classification: parentName
          ? extra.triggerNode.props.title
          : '',
        parentCode: extra.triggerNode.props.parentCode, //后端让把父节点的code也传过去
      };
      return;
    }
  };
  const onSelectChange = (value: string, label: any, flag: any) => {
    console.log(value, label, flag);
    const label_: any = Array.isArray(label) ? label : [label]; //避免有些是单选的
    if (flag === 'classification') {
      //classification
      setClassificationName(label_.map((item: any) => item.children));
    } else if (flag === 'college') {
      setCollegeName(label_.map((item: any) => item.children));
    } else if (flag === 'majors') {
      setMajorName(label_);
    } else if (flag === 'subject') {
      setSubjectName(label_.map((item: any) => item.children));
      subjectObject.current.primary_classification = label_[0].key;
    }
  };

  const getDate = (date: any, isStart: boolean) => {
    if (!date) return 0;
    const year = date?.year();
    const month = date?.month() + 1;
    const day = date?.date();
    if (isStart) {
      return new Date(`${year}-${month}-${day}`).valueOf();
    } else {
      return new Date(`${year}-${month}-${day} 23:59:59:999`).valueOf();
    }
  };

  const preservation = () => {
    const detail = courseDetailRef.current;
    const checkArr = detail.entityData.publishStatus == 1 && parameterConfig.target_customer !== CUSTOMER_SHTECH ? (parameterConfig.target_customer === CUSTOMER_UTCM ? ['name', 'college', 'subject'] : ['name', 'college']) : ['name'];
    return form.validateFields(checkArr).then(() => {
      const data = form.getFieldsValue(true);
      let check = 0;
      if (parameterConfig.target_customer === CUSTOMER_SHTECH) {
        check = 1;
      }
      if ( getDate(data.date?.[0], true) > 0) {
        check = 1;
      }
      // console.log('spocssssssssssssss',item)
      let tempSubject = data.subject ?  Array.isArray(data.subject) ? data.subject : [data.subject]: [];
      if (subjectObject.current.primary_classification) {
        subjectObject.current.parentCode && tempSubject.unshift(subjectObject.current.parentCode);
      }
      console.log('tempSubject', tempSubject);
      let param: any = {
        contentId: detail.contentId,
        Check: check,
        updateData: {
          college: data.college || [],
          collegeName: collegeName || [],
          name: data.name,
          start_time:
            data.date && getDate(data.date?.[0], true),
          end_time:
            data.date && getDate(data.date?.[1], false),
          major: data.majors,
          majorName: majorName || [],
          tag: data.tag || [],
          classification: data.classification || null,
          classificationName: classificationName || [],
          cover: data.cover,
          describe: data.describe,
          target: data.target,
          // subject: Array.isArray(data.subject)?data.subject:[data.subject],
          subject: tempSubject,
          course_level: data.course_level,
          subjectName: subjectName || [],
          publishStatus: detail.entityData.publishStatus,
          semester_teaching_courses: location.query.type !== "microMajor" ? data.semester_teaching_courses : null,
          semester_show_name: location.query.type !== "microMajor" ? (semesters.find(item => item.name === data.semester_teaching_courses)?.showSemester ?? data.semester_teaching_courses) : null,
          courseType: detail.entityData.courseType,
          cover_video: JSON.stringify(resource),
          languages: toArray(data.languages),
          educational: toArray(data.educational)
          // unLoginShow: location.query.type === 'mooc' ? data.unLoginShow : null
        }
      };
      //省平台
      param.updateData.primary_classification = subjectObject.current.primary_classification;
      param.updateData.secondary_classification = subjectObject.current.secondary_classification;
      return new Promise<{ param: any; check: number; }>((resolve) => {
        if (nameChangeTag) {
          Modal.confirm({
            content: t("检测到课程名已改变，是否按当前课程名更新课程封面？"),
            onOk() {
              const setting = {
                ...coverSetting,
                courseName: data.name
              };
              handleChangeCover(setting).then((res) => {
                if (res) {
                  param.updateData.cover = res;
                  setCover(res);
                  resolve({ param, check });
                  resolve({ param, check });
                } else {
                  throw new Error();
                }
              });
            },
            onCancel() {
              resolve({ param, check });
            }
          });
        } else {
          resolve({ param, check });
        }
      }).catch(() => {
        return false;
      });
      // baseInfo.changeBasicInfo(param, { Check: check }).then(res => {
      //   if (res?.message === 'OK') {
      //     isEdit = false
      //     message.success("编辑成功");
      //     dispatch({
      //       type: 'updata/changelayout',
      //       payload: {},
      //     });
      //   } else {
      //     message.error(res?.message);
      //   }
      // });
    }).catch(() => {
      return false;
    });
  };

  const handleCoverConfirm = (images: string, setting: any) => {
    setCover(images);
    setCoverSetting(setting);
  };

  const loopGetResource = () => {
    getResourceDetail(true).then(res => {
      if (res === true) {
        timer.current = setInterval(() => {
          getResourceDetail(false);
        }, 2000);
      }
    });
  };

  const getResourceDetail = (isFirst: boolean) => {
    return resourceDetail(resource.resourceId).then((res: any) => {
      if (res.success) {
        const { fileGroups } = res.data;
        const keyframeArr = fileGroups?.filter(
          (item: any) => item.typeCode === 'keyframefile',
        );
        const previewFile = fileGroups?.filter(
          (item: any) => item.typeCode === 'previewfile',
        );
        if (keyframeArr.length > 0 && previewFile.length > 0) {
          clearInterval(timer.current);
          timer.current = null;
          const src = previewFile?.[0]?.fileItems?.[0]?.displayPath;
          const keyframe = keyframeArr?.[0]?.fileItems?.[0]?.displayPath;
          setResource({
            resourceId: resource.resourceId,
            src,
            keyframe,
          });
          setOldResource({
            resourceId: resource.resourceId,
            src,
            keyframe,
          });
        }
      }
      if (isFirst) {
        return true;
      }
    });
  };

  return (
    <div className="base-info">
      <Form
        form={form}
        // {...layout}
        name="basic"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        onValuesChange={(values: any) => {
          if (values.name != undefined) {
            setNameChangeTag(true);
          }
        }}
        // onFieldsChange={() => { isEdit = true }}
      >
        <Tooltip title={!canPageEdit  &&  t('点击 “ 编辑 ” 按钮进行内容编辑')}>
          <Form.Item
            label={t('课程名称')}
            name="name"
            rules={[
              { required: true, message: t('请填写课程名称!') },
              {
                type: 'string',
                max: 50,
              },
            ]}
          >
            <Input
              disabled={!canPageEdit}
              autoComplete="off"
              showCount
              maxLength={50}
            />
          </Form.Item>
        </Tooltip>

        {/* <Form.Item
           label="课程专题"
           name="classification"
           // rules={[{ required: true, message: '请选择课程分类!' }]}
          >
           <Select
             mode="multiple"
             onChange={(e:any,label:any)=>onSelectChange(e,label,'classification')}
             filterOption={(input, option: any) => {
               return (
                 option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
               );
             }}
           >
             {courseCategoryList &&
               Object.keys(courseCategoryList).map((key: any) => {
                 return (
                   <Select.Option value={key} key={courseCategoryList[key].split(',')[0]}>
                     {courseCategoryList[key].split(',')[0]}
                   </Select.Option>
                 );
               })}
           </Select>
          </Form.Item> */}
        { <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
          <Form.Item
            label={t('学科专业')}
            name="subject"
            rules={[
              {
                required: parameterConfig.target_customer === CUSTOMER_UTCM,
                message: t('请选择学科专业!'),
              },
            ]}
          >
            {
              //1 是川大  2是成信大 3高教 4职教 （3,4都是省平台）
              // ['3','4'].includes(homePageConfig.banner_plate_type)?
              <TreeSelect
                disabled={!canPageEdit}
                treeData={forTree(
                  JSON.parse(courseSubjectList?.controlData || '[]'),
                )}
                onChange={(value: any, label: any, extra: any) =>
                  onProfessionChange(value, label, extra, 'subject')
                }
                treeCheckable={courseSubjectList?.isMultiSelect}
                // showCheckedStrategy={SHOW_PARENT}
                // placeholder={`请选择${courseSubjectList.showName}`}
                allowClear={true}
                showSearch
                treeNodeFilterProp="title"
                // defaultValue={[]}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              />
            }
          </Form.Item>
        </Tooltip>}
        {<Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
          <Form.Item
            label={t('开课时间')}
            name="date"
            rules={[
              { type: 'array', required: true, message: t('请选择开课时间！') },
            ]}
          >
            <RangePicker disabled={!canPageEdit} />
          </Form.Item>
        </Tooltip>}
        {location.query.type !== "microMajor" && <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
          <Form.Item label={t("开课学期")} name="semester_teaching_courses" rules={[{ required: false, message: t("请选择所属学期!") }]}>
            <Select disabled={!canPageEdit} allowClear>
              {semesters.map((item: ISemesters) => (
                <Select.Option key={item.id} value={item.name}>
                  {item.showSemester || item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Tooltip>}
        {<>
          <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
            <Form.Item
              label={t("开课学院")}
              name="college"
              >
              <Select
                disabled={!canPageEdit}
                mode="multiple"
                // onChange={handleSelectCollege}
                showArrow
                allowClear
                value={form.getFieldValue('college')}
                suffixIcon={(() => {
                  let collegelength = form.getFieldValue('college')?.length || 0;
                  return <span style={{ color: '#9D9D9D', fontSize: '14px' }}>{collegelength}/10</span>;
                })()}
                onChange={(e: any, label: any) => {
                  if (e.length > 10) {
                    message.error(t('最多选择10个开课学院'));
                    let value = form.getFieldValue('college');
                    // 截取前10个
                    form.setFieldValue('college', value.slice(0, 10));
                  } else {
                    onSelectChange(e, label, 'college');
                  }
                }}
                filterOption={(input, option: any) => {
                  return (
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0);

                }}>

                {collegeList.map((item: any) => {
                  return (
                    <Select.Option value={item.code} key={item.code}>
                      {item.name}
                    </Select.Option>);

                })}
              </Select>
            </Form.Item>
          </Tooltip>
          <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
            <Form.Item
              label={t("适用专业")}
              name="majors"
              rules={[{ required: false, message: t("请填写适用专业!") }]}>

              <TreeSelect
                disabled={!canPageEdit}
                style={{ width: '100%' }}
                allowClear
                showArrow
                value={form.getFieldValue('majors')}
                suffixIcon={(() => {
                  let majorslength = form.getFieldValue('majors')?.length || 0;
                  return <span style={{ color: '#9D9D9D', fontSize: '14px' }}>{majorslength}/10</span>;
                })()}
                placeholder={t("请选择专业")}
                // treeData={curMajorList}
                treeData={majorList}
                multiple
                treeCheckable
                showCheckedStrategy={SHOW_CHILD}
                // showCheckedStrategy={SHOW_PARENT}
                treeNodeFilterProp="title"
                onChange={(e: any, label: any) => {
                  if (e.length > 10) {
                    message.error(t('最多选择10个适用专业'));
                    let value = form.getFieldValue('majors');
                    // 截取前10个
                    form.setFieldValue('majors', value.slice(0, 10));
                  } else {
                    onSelectChange(e, label, 'majors');
                  }
                }} />

            </Form.Item>
          </Tooltip>
          {parameterConfig.target_customer === 'CDOU' ? (
            <Tooltip title={!canPageEdit && t('点击 “ 编辑 ” 按钮进行内容编辑')}>
              <Form.Item label={t('学习对象')} name="educational">
                <Select
                  disabled={!canPageEdit}
                  style={{ width: '100%' }}
                  optionFilterProp="children"
                  allowClear
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                >
                  {getCultivationList.map((item: any, index: number) => (
                    <Select.Option key={index} value={item}>
                      {item}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Tooltip>
          ) : (
            <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
              <Form.Item
                label={t("培养层次")}
                name="educational">

                <Select
                  disabled={!canPageEdit}
                  style={{ width: '100%' }}
                  optionFilterProp="children"
                  allowClear
                  mode='multiple'
                  getPopupContainer={(triggerNode) =>
                    triggerNode.parentNode}>


                  {cultivationLevel.map((item: any, index: number) =>
                    <Select.Option key={index} value={item}>
                      {item}
                    </Select.Option>)}

                </Select>
              </Form.Item>
            </Tooltip>)}
        </>}
        {location.query.type !== "microMajor" && <>
          <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
            <Form.Item
              label={t("语种")}
              name="languages">

              <Select
                disabled={!canPageEdit}
                style={{ width: '100%' }}
                optionFilterProp="children"
                allowClear
                getPopupContainer={(triggerNode) =>
                  triggerNode.parentNode}>


                {languageList.map((item: any, index: number) =>
                  <Select.Option key={index} value={item}>
                    {item}
                  </Select.Option>)}

              </Select>
            </Form.Item>
          </Tooltip>
          <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
            <Form.Item
              label={t("课程等级")}
              name="course_level">

              <Select
                disabled={!canPageEdit}
                style={{ width: '100%' }}
                optionFilterProp="children"
                // onChange={(e:any,label:any)=>onSelectChange(e,label,'course_level')}
                allowClear
                getPopupContainer={(triggerNode) =>
                  triggerNode.parentNode}>
                {[t("校级"), t("省级"), t("国家级")].map((item: any, index: number) =>
                  <Select.Option key={index} value={item}>
                    {item}
                  </Select.Option>)}

              </Select>
            </Form.Item>
          </Tooltip>
        </>}
        <Tooltip title={!canPageEdit && t("点击 “ 编辑 ” 按钮进行内容编辑")}>
          <Form.Item
            key={'tag'}
            name={'tag'}
            label={t("标签")}>

            <TagInput disabled={!canPageEdit} tagLabels={tagLabels} />
          </Form.Item>
        </Tooltip>

        <div className='img-video-wrp'>
          <Form.Item name="cover" label={t("课程封面")} className='img-control' getValueFromEvent={normFile}>
            <div className="img-box">
              <Img width={200} src={cover} />
            </div>
            {canPageEdit && <div className="upload-buttons">
              <Button type="primary" ghost onClick={() => setCoverModalVisible(true)}>{t("编辑封面")}</Button>
              {/* <Button type="primary" onClick={restoreDefault}>
                 恢复默认
                </Button> */}
            </div>}
          </Form.Item>
          {<Form.Item
            name="cover_video"
            label={t("宣传视频：")}
            // valuePropName="fileList"
            getValueFromEvent={normFile}
            // extra="longgggggggggggggggggggggggggggggggggg"
            // rules={[{ required: true, message: '请上传课程封面!' }]}
          >
            <div className="video-box">
              {resource.resourceId ?
                <div className='video-view-wrp wrp-box'>
                  {resource.keyframe ?
                    <Img width={200} src={resource.keyframe} preview={false} /> :
                    <div className='trans-coding'>{t("视频转码生成中")}</div>}
                  {resource.src && <div className='mask' onClick={() => setVideoPreviewVisible(true)}>
                    <EyeOutlined />{t("预览")}
                  </div>}
                </div> :
                <div className='video-btn wrp-box' onClick={() => { canPageEdit && setVideoVisible(true); }}>
                  {canPageEdit ? <>
                    <CloudUploadOutlined />
                    <div className='text'>{t("上传视频")}</div>
                  </> : <div className='text'>{t("暂无视频")}</div>}
                </div>}
              {resource.resourceId && canPageEdit && <div className='video-btn-wrp'>
                <Button type="primary" ghost style={{ margin: "10px 48px 0 0" }} onClick={() => setVideoVisible(true)}>{t("重新上传")}</Button>
                <Button type="primary" ghost onClick={() => setResource({})}>{t("删除")}</Button>
              </div>}
            </div>
          </Form.Item>}
        </div>
        <div className='tips-wrp'>封面建议尺寸：640 * 360{t("，有宣传视频即展示宣传视频，没有即不展示")}</div>


        {
          /* location.query.type === 'mooc' ?
          <Form.Item
            label="是否对外开放"
            name="unLoginShow"
            rules={[{ required: false, message: '请选择是否对外开放!' }]}
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item> : '' */}

      </Form>
      <CoverModal
        visible={coverModalVisible}
        name={form.getFieldValue('name')}
        coverSetting={coverSetting}
        teacher={teacherNames || []}
        coverConfirm={handleCoverConfirm}
        coverCancel={() => {
          setCoverModalVisible(false);
        }}
      />
      <VideoModal
        visible={videoVisible}
        onSave={(resourceId?: string) =>
          resourceId ? setResource({ resourceId }) : setResource({})
        }
        onClose={() => setVideoVisible(false)}
      />

      <FilePreviewModal
        visible={videoPreviewVisible}
        onClose={() => setVideoPreviewVisible(false)}
        fileType="video"
        width={1067}
        file={{ filePath: resource.src, extraData: t('视频预览') }}
      />
    </div>
  );
});

export default BaseInfo;
